package com.phonepe.merchant.gladius.models.tasks.request.command;

import com.phonepe.merchant.gladius.models.tasks.request.CreateTaskInstanceRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCreateRequest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class TaskCreateRequestTest {

    @Test
    public void testBuilder() {
        TaskCreateRequest actual = TaskCreateRequest.builder()
                .commentContent("comment")
                .rescheduleAt(123456L)
                .taskInstance(CreateTaskInstanceRequest.builder().taskDefinitionId("TD1").createdBy("Test")
                        .entityId("ENTITY1")
                        .campaignId("CAMP1")
                        .build())
                .markAvailable(true)
                .build();
        Assertions.assertNotNull(actual);
    }

}
