package com.phonepe.merchant.gladius.models.tasks.utils;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

public class TaskUtilsTest {

    @Test
    void testNullInput() {
        assertNull(TaskUtils.toTitleCase(null));
    }

    @Test
    void testBlankInput() {
        assertEquals("   ", TaskUtils.toTitleCase("   "));
        assertEquals("", TaskUtils.toTitleCase(""));
    }

    @Test
    void testSingleWord() {
        assertEquals("Lead", TaskUtils.toTitleCase("LEAD"));
        assertEquals("Lead", TaskUtils.toTitleCase("lEad"));
    }

    @Test
    void testMultipleWordsSeparatedBySpaces() {
        assertEquals("Lead Intent", TaskUtils.toTitleCase("LeAD    InTENT"));
    }

    @Test
    void testMultipleWordsSeparatedByUnderscore() {
        assertEquals("Lead Intent", TaskUtils.toTitleCase("LeAD_____InTENT"));
    }

}
