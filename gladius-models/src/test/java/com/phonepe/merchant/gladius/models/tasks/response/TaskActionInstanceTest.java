package com.phonepe.merchant.gladius.models.tasks.response;

import com.phonepe.merchant.gladius.models.tasks.enums.Namespace;
import com.phonepe.merchant.gladius.models.tasks.request.ActionMetaData;
import com.phonepe.models.merchants.tasks.EntityType;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;
import java.util.Set;

public class TaskActionInstanceTest {

    @Test
    void testValidTaskActionInstance_DefaultValue_Success() {
        TaskActionInstance instance = new TaskActionInstance();
        instance.setActionId("TASK_123");
        instance.setEntityType(EntityType.STORE);
        instance.setNamespace(Namespace.MERCHANT_ONBOARDING);
        instance.setCreatedBy("SYSTEM");
        instance.setUpdatedBy("SYSTEM");
        instance.setDescription("Test action description");

        instance.setSubSteps(List.of("step1", "step2"));
        instance.setAttributes(Map.of("key1", Set.of("val1", "val2")));

        Assertions.assertEquals(1000, instance.getMetaData().getActionLevelSelfAssignmentLimit());
    }

    @Test
    void testValidTaskActionInstance_NonDefaultValue_Success() {
        TaskActionInstance instance = new TaskActionInstance();
        instance.setActionId("TASK_123");
        instance.setEntityType(EntityType.STORE);
        instance.setNamespace(Namespace.MERCHANT_ONBOARDING);
        instance.setCreatedBy("SYSTEM");
        instance.setUpdatedBy("SYSTEM");
        instance.setDescription("Test action description");
        instance.setSubSteps(List.of("step1", "step2"));
        instance.setAttributes(Map.of("key1", Set.of("val1", "val2")));
        instance.setMetaData(ActionMetaData.builder().actionLevelSelfAssignmentLimit(50).build());
        Assertions.assertEquals(50, instance.getMetaData().getActionLevelSelfAssignmentLimit());
    }

}
