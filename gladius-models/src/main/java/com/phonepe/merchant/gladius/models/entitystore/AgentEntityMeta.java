package com.phonepe.merchant.gladius.models.entitystore;

import com.phonepe.merchant.legion.models.profile.enums.AgentType;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class AgentEntityMeta extends EntityMeta {

    private String phoneNumber;

    private String emailId;

    private AgentType role;

    // New org-level fields
    private String orgId;
    private String orgName;
    private String orgRoleId;
    private String orgRoleName;

    public AgentEntityMeta(AgentEntity entity) {
        super(entity);
        this.emailId = entity.getEmailId();
        this.phoneNumber = entity.getPhoneNumber();
        this.role = entity.getRole();
        this.orgId = entity.getOrgId();
        this.orgName = entity.getOrgName();
        this.orgRoleId = entity.getOrgRoleId();
        this.orgRoleName = entity.getOrgRoleName();
    }

    public AgentEntityMeta(AgentEntityMeta entityMeta) {
        super(entityMeta);
        this.emailId = entityMeta.getEmailId();
        this.phoneNumber = entityMeta.getPhoneNumber();
        this.role = entityMeta.getRole();
        this.orgId = entityMeta.getOrgId();
        this.orgName = entityMeta.getOrgName();
        this.orgRoleId = entityMeta.getOrgRoleId();
        this.orgRoleName = entityMeta.getOrgRoleName();
    }

}
