package com.phonepe.merchant.gladius.models.tasks.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.phonepe.merchant.gladius.models.tasks.domain.TaskDefinitionAttributes;
import com.phonepe.merchant.gladius.models.tasks.enums.Namespace;
import com.phonepe.merchant.gladius.models.tasks.enums.Priority;
import com.phonepe.merchant.gladius.models.tasks.request.ActionToRemarkConfig;
import com.phonepe.merchant.gladius.models.tasks.request.IntentWithRemarks;
import com.phonepe.merchant.gladius.models.tasks.request.TaskDefinitionMeta;
import com.phonepe.merchant.gladius.models.tasks.request.TaskOperationsMeta;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskDefinitionInstance {

    private String taskDefinitionId;

    @NotNull
    @NotEmpty
    private String name;

    @NotNull
    @NotEmpty
    private String updatedBy;

    @NotNull
    @NotEmpty
    private String createdBy;

    @NotNull
    private Priority priority;

    @NotNull
    private Namespace namespace;

    @NotNull
    private Integer points;

    @NotNull
    private String actionId;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private Date createdAt;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private Date updatedAt;

    private TaskDefinitionMeta taskDefinitionMeta;

    private TaskOperationsMeta taskOperationsMeta;

    private Set<String> tags;

    private Map<String, Set<String>> attributes;

    private TaskDefinitionAttributes definitionAttributes;

    @JsonIgnore
    public boolean isLeadCreationAllowed() {
        if (definitionAttributes == null || definitionAttributes.getLeadConfig() == null) {
            return false;
        }
        List<ActionToRemarkConfig> leadCreation = definitionAttributes.getLeadConfig().getLeadCreation();
        if (leadCreation == null || leadCreation.isEmpty()) {
            return false;
        }
        List<IntentWithRemarks> config = leadCreation.get(0).getConfig();
        return config != null && !config.isEmpty();
    }

    @JsonIgnore
    public boolean isLeadUpdationAllowed() {
        if (definitionAttributes == null || definitionAttributes.getLeadConfig() == null) {
            return false;
        }
        List<ActionToRemarkConfig> leadUpdation = definitionAttributes.getLeadConfig().getLeadUpdation();
        if (leadUpdation == null || leadUpdation.isEmpty()) {
            return false;
        }
        List<IntentWithRemarks> config = leadUpdation.get(0).getConfig();
        return config != null && !config.isEmpty();
    }
}
