package com.phonepe.merchant.gladius.models.tasks.utils;

import com.phonepe.merchant.gladius.models.tasks.request.IntentWithRemarks;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LeadUpdationConfig {
    private  List<IntentWithRemarks> leadUpdationIntents;
}
