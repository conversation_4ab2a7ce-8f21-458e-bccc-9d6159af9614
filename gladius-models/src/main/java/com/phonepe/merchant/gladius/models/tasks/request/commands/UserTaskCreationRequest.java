package com.phonepe.merchant.gladius.models.tasks.request.commands;

import com.phonepe.merchant.gladius.models.tasks.enums.UserGenTaskType;
import com.phonepe.models.merchants.tasks.EntityType;
import lombok.Builder;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
public class UserTaskCreationRequest extends TaskCommandRequest {

    @NotNull
    @NotEmpty
    private String merchantId;

    @NotNull
    @NotEmpty
    private String storeId;

    @NotNull
    private EntityType entityType;

    private UserGenTaskType userGenTaskType;

    private long rescheduleAt;

    private String leadIntent;

    private String remark;

    @Length(max = 2000, min = 2)
    private String commentContent;

    public UserTaskCreationRequest() {
        super(AppCommand.USER_TASK_CREATION);
    }

    @Builder
    public UserTaskCreationRequest(String merchantId,
                                   String storeId,
                                   EntityType entityType,
                                   UserGenTaskType userGenTaskType,
                                   long rescheduleAt,
                                   String leadIntent,
                                   String remark) {
        this();
        this.merchantId = merchantId;
        this.storeId = storeId;
        this.entityType = entityType;
        this.userGenTaskType = userGenTaskType;
        this.rescheduleAt = rescheduleAt;
        this.leadIntent = leadIntent;
        this.remark = remark;
    }

    @Override
    public <T> T accept(TaskCommandRequestVisitor<T> var1) {
        return var1.visit(this);
    }
}
