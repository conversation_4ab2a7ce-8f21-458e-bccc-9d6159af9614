//package com.phonepe.merchant.gladius.models.legion.v2;
//
//import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
//import com.phonepe.merchant.legion.models.profile.enums.AgentStatus;
//import com.phonepe.merchant.legion.models.profile.enums.AgentType;
//import com.phonepe.merchant.legion.models.profile.request.Address;
//import com.phonepe.models.merchants.BusinessUnit;
//import com.phonepe.merchant.legion.models.profile.enums.Region;
//import lombok.AllArgsConstructor;
//import lombok.Builder;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//
//import java.util.Date;
//import java.util.List;
//
///**
// * Extended Agent Profile model for Legion V2 API
// * Includes org-level information alongside existing agent data
// */
//@Data
//@Builder
//@NoArgsConstructor
//@AllArgsConstructor
//@JsonIgnoreProperties(ignoreUnknown = true)
//public class AgentProfileV2 {
//
//    // Existing agent fields
//    private String agentId;
//    private String name;
//    private String phoneNumber;
//    private String emailId;
//    private AgentType agentType;
//    private BusinessUnit businessUnit;
//    private Region region;
//    private AgentStatus status;
//    // Helper methods to maintain backward compatibility
//    private boolean active;
//    private String managerId;
//    private Date lastActiveDate;
//    private List<Address> addresses;
//
//    // New org-level fields
//    private String orgId;
//    private String orgName;
//    private String orgRoleId;
//    private String orgRoleName;
//    private String orgManagerId;
//    private List<OrgProfile.OrgAttribute> orgAttributes;
//
//    /**
//     * Get the effective role - prioritizes org role if available, falls back to agent type
//     */
//    public String getEffectiveRole() {
//        return orgRoleName != null ? orgRoleName : (agentType != null ? agentType.name() : null);
//    }
//
//    /**
//     * Get the effective manager - prioritizes org manager if available, falls back to agent manager
//     */
//    public String getEffectiveManagerId() {
//        return orgManagerId != null ? orgManagerId : managerId;
//    }
//
//    /**
//     * Get business unit from org attributes if available, falls back to agent business unit
//     */
//    public String getEffectiveBusinessUnit() {
//        if (orgAttributes != null) {
//            return orgAttributes.stream()
//                    .filter(attr -> "businessUnit".equals(attr.getKey()))
//                    .map(OrgProfile.OrgAttribute::getValue)
//                    .findFirst()
//                    .orElse(businessUnit != null ? businessUnit.name() : null);
//        }
//        return businessUnit != null ? businessUnit.name() : null;
//    }
//
//    /**
//     * Get region/channel from org attributes if available, falls back to agent region
//     */
//    public String getEffectiveRegion() {
//        if (orgAttributes != null) {
//            return orgAttributes.stream()
//                    .filter(attr -> "region".equals(attr.getKey()) || "channel".equals(attr.getKey()))
//                    .map(OrgProfile.OrgAttribute::getValue)
//                    .findFirst()
//                    .orElse(region != null ? region.name() : null);
//        }
//        return region != null ? region.name() : null;
//    }
//}
