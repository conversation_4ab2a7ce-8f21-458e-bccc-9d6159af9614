//package com.phonepe.merchant.gladius.models.legion.v2;
//
//import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
//import lombok.AllArgsConstructor;
//import lombok.Builder;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//
//import java.util.List;
//
///**
// * Organization profile model for Legion V2 API
// * Represents the new org-level structure from Legion revamp
// */
//@Data
//@Builder
//@NoArgsConstructor
//@AllArgsConstructor
//@JsonIgnoreProperties(ignoreUnknown = true)
//public class OrgProfile {
//
//    private String orgId;
//    private String name;
//    private List<OrgAttribute> attributes;
//    private List<OrgRole> roles;
//
//    @Data
//    @Builder
//    @NoArgsConstructor
//    @AllArgsConstructor
//    @JsonIgnoreProperties(ignoreUnknown = true)
//    public static class OrgAttribute {
//        private String key;
//        private String value;
//    }
//
//    @Data
//    @Builder
//    @NoArgsConstructor
//    @AllArgsConstructor
//    @JsonIgnoreProperties(ignoreUnknown = true)
//    public static class OrgRole {
//        private String roleId;
//        private String roleName;
//        private String managerId;
//    }
//}
