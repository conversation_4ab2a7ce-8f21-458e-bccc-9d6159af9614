package com.phonepe.merchant.gladius.models.tasks.request.commands;

import com.phonepe.merchant.gladius.models.tasks.request.CreateTaskInstanceRequest;
import com.phonepe.merchant.gladius.models.tasks.request.DefinitionIdIdentifier;
import lombok.Builder;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Data
public class TaskCreateRequest extends TaskCommandRequest implements DefinitionIdIdentifier {

    @Valid
    @NotNull
    private CreateTaskInstanceRequest taskInstance;

    private boolean markAvailable;

    @Length(max = 2000, min = 2)
    private String commentContent;

    private Long rescheduleAt;

    public TaskCreateRequest() {
        super(AppCommand.CREATE);
    }

    @Builder
    public TaskCreateRequest(CreateTaskInstanceRequest taskInstance, boolean markAvailable, String commentContent,  Long rescheduleAt) {
        this();
        this.taskInstance = taskInstance;
        this.markAvailable = markAvailable;
        this.commentContent = commentContent;
        this.rescheduleAt = rescheduleAt;
    }

    @Override
    public <T> T accept(TaskCommandRequestVisitor<T> var1) {
        return var1.visit(this);
    }

    @Override
    public String getDefinitionId() {
        return taskInstance.getTaskDefinitionId();
    }
}
