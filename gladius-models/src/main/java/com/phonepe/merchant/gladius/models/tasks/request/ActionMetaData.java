package com.phonepe.merchant.gladius.models.tasks.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActionMetaData implements Serializable {

    @Min(0)
    @Max(1000)
    private int actionLevelSelfAssignmentLimit = 1000;

}
