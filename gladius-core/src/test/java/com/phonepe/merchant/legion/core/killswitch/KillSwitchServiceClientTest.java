package com.phonepe.merchant.legion.core.killswitch;


import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.merchant.legion.core.utils.EventConstants;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import killswitch.KillSwitchServiceClient;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class KillSwitchServiceClientTest {

    @Mock
    private HttpConfiguration httpConfiguration;
    @Mock
    private ServiceEndpointProviderFactory serviceEndpointProviderFactory;
    @Mock
    private MetricRegistry metricRegistry;
    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private KillSwitchServiceClient killSwitchServiceClient;

    @Before
    public void setUp() {

    }

    @Test
    public void testDiscoveryEventsConstant() {
        EventConstants.DiscoveryEvents event = EventConstants.DiscoveryEvents.POST_FILTER_SORTING_DIFFERENCE_EVENT;
        String expectedEventName = "POST_FILTER_SORTING_DIFFERENCE_EVENT";
        Assertions.assertEquals(expectedEventName, event.toString(), "Event name is not correct");
    }

    @Test
    public void testHotspotEventsConstant() {
        EventConstants.HotspotEvents event = EventConstants.HotspotEvents.HOTSPOT_SYNC_FAILED;
        String expectedEventName = "HOTSPOT_SYNC_FAILED";
        Assertions.assertEquals(expectedEventName, event.toString(), "Event name is not correct");
        event = EventConstants.HotspotEvents.HOTSPOT_ACTIVE;
        expectedEventName = "HOTSPOT_ACTIVE";
        Assertions.assertEquals(expectedEventName, event.toString(), "Event name is not correct");
        event = EventConstants.HotspotEvents.HOTSPOT_DEACTIVATED;
        expectedEventName = "HOTSPOT_DEACTIVATED";
        Assertions.assertEquals(expectedEventName, event.toString(), "Event name is not correct");
    }

}