package com.phonepe.merchant.legion.core.repository;

import com.phonepe.commons.bonsai.models.KeyNode;
import com.phonepe.commons.bonsai.models.ValueNode;
import com.phonepe.commons.bonsai.models.value.BooleanValue;
import com.phonepe.commons.bonsai.models.value.StringValue;
import com.phonepe.frontend.chimera.client.ChimeraClientConfig;
import com.phonepe.frontend.chimera.lite.ChimeraLite;
import com.phonepe.frontend.chimera.models.context.inputs.InitialContext;
import com.phonepe.frontend.chimera.models.context.inputs.MerchantInitialContext;
import com.phonepe.merchant.gladius.models.tasks.filters.TaskFilters;
import com.phonepe.merchant.legion.core.LegionCoreTest;
import com.phonepe.merchant.legion.core.models.ChimeraLiteConfig;
import com.phonepe.merchant.legion.core.repository.impl.ChimeraRepositoryImpl;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.models.profile.enums.AgentStatus;
import com.phonepe.merchant.legion.models.profile.enums.AgentType;
import com.phonepe.merchant.legion.models.profile.enums.Region;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.models.merchants.BusinessUnit;

import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.Date;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

public class ChimeraLiteRepositoryTest extends LegionCoreTest {

    @Mock
    private static ChimeraLite chimeraLite;
    private static ChimeraLiteRepository chimeraLiteRepository;
    private static ChimeraLiteConfig chimeraLiteConfig;
    private static LegionService legionService;

    @BeforeClass
    public static void setupInner() {
        chimeraLite = Mockito.spy(ChimeraLite.class);
        legionService = Mockito.mock(LegionService.class);
        chimeraLiteConfig = ChimeraLiteConfig.builder()
                .chimeraClientConfig(ChimeraClientConfig.builder().build())
                .teamName("ace")
                .usecaseKeyMap(Map.of("STATIC_FILTERS", "filter"))
                .build();
        chimeraLiteRepository = new ChimeraRepositoryImpl(chimeraLite, legionService, chimeraLiteConfig);
    }

    @Test
    public void testStaticFilters() {
        when(chimeraLite.evaluate(anyString(), Mockito.any())).thenReturn(KeyNode.builder().build());
        TaskFilters taskFilters = chimeraLiteRepository.getStaticFilters();
        Assert.assertNull(taskFilters);
    }

    @Test
    public void testGetChimeraConfig() {
        String chimeraKey = "testKey";
        String agentId = "testAgentId";
        String expectedValue = "null";
        Region region = Region.URBAN;
        Class<String> classType = String.class;

        when(legionService.getAgentProfile(Mockito.anyString())).thenReturn(AgentProfile.builder()
                .businessUnit(BusinessUnit.ENTERPRISE)
                        .emailId("<EMAIL>")
                        .agentType(AgentType.AGENT)
                        .active(true)
                        .region(region)
                        .lastActiveDate((new Date()).getTime())
                        .status(AgentStatus.CREATED)
                .build());

        InitialContext initialContext = MerchantInitialContext.builder()
                .team(chimeraLiteConfig.getTeamName())
                .appContext(Map.of("agentId", agentId, "region", region))
                .build();
        when(chimeraLite.evaluate(chimeraKey, initialContext))
                .thenReturn(KeyNode.builder().key("testKey").node(ValueNode.builder().value(new StringValue("null")).build()).build());

        String actualValue = chimeraLiteRepository.getChimeraConfig(chimeraKey,"agentId", classType);

        assertEquals(null, actualValue);
    }

    @Test
    public void testGetChimeraConfig1() {
        String chimeraKey = "testKey";
        Class<String> classType = String.class;

        InitialContext initialContext = MerchantInitialContext.builder()
                .team(chimeraLiteConfig.getTeamName())
                .build();

        when(chimeraLite.evaluate(chimeraKey, initialContext))
                .thenReturn(KeyNode.builder().key("testKey").node(ValueNode.builder().value(new StringValue("null")).build()).build());

        String actualValue = chimeraLiteRepository.getChimeraConfig(chimeraKey, classType);

        assertEquals(null, actualValue);
    }

    @Test
    public void testGetChimeraBooleanFlag() {
        String chimeraKey = "testKey";
        InitialContext initialContext = MerchantInitialContext.builder()
                .team(chimeraLiteConfig.getTeamName())
                .build();

        when(chimeraLite.evaluate(chimeraKey, initialContext))
                .thenReturn(KeyNode.builder().key("testKey").node(ValueNode.builder().value(new BooleanValue(true)).build()).build());

        boolean actualValue = chimeraLiteRepository.isChimeraKeyEnabled(chimeraKey);

        assertTrue(actualValue);
    }

    @Test
    public void testGetStringValuedChimeraConfig() {
        String chimeraKey = "testKey";
        Class<String> classType = String.class;

        InitialContext initialContext = MerchantInitialContext.builder()
                .team(chimeraLiteConfig.getTeamName())
                .build();

        when(chimeraLite.evaluate(chimeraKey, initialContext))
                .thenReturn(KeyNode.builder().key("testKey").node(ValueNode.builder().value(new StringValue("val")).build()).build());

        String actualValue = chimeraLiteRepository.getStringValuedChimeraConfig(chimeraKey);

        assertEquals("val", actualValue);
    }

}
