package com.phonepe.merchant.legion.core;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hystrix.configurator.config.HystrixConfig;
import com.hystrix.configurator.core.HystrixConfigurationFactory;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.platform.http.v2.common.Endpoint;
import com.phonepe.platform.http.v2.common.ServiceEndpointProvider;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import io.appform.ranger.discovery.bundle.ServiceDiscoveryConfiguration;
import org.junit.BeforeClass;
import org.junit.Rule;
import org.junit.rules.ExpectedException;

import java.util.Optional;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public abstract class GladiusCoreBaseTest {

    private static ObjectMapper mapper;

    @Rule
    public ExpectedException expectedException = ExpectedException.none();
    protected static ServiceEndpointProviderFactory serviceEndpointProviderFactoryMock;
    protected static ServiceEndpointProvider serviceEndpointProvider;

    protected static ServiceDiscoveryConfiguration serviceDiscoveryConfiguration;


    @BeforeClass
    public static void setup(){
        mapper = new ObjectMapper();
        SerDe.init(mapper);
        serviceEndpointProvider = mock(ServiceEndpointProvider.class);
        serviceDiscoveryConfiguration = mock(ServiceDiscoveryConfiguration.class);
        serviceEndpointProviderFactoryMock = mock(ServiceEndpointProviderFactory.class);

        //Hystrix
        HystrixConfig hystrixConfig = new HystrixConfig();
        hystrixConfig.getDefaultConfig().getThreadPool().setTimeout(10000);
        HystrixConfigurationFactory.init(hystrixConfig);

    }
    protected static void getEndPointMock(String clientId) {
        when(serviceEndpointProviderFactoryMock.provider(clientId)).thenReturn(serviceEndpointProvider);
        Optional<Endpoint> endpointOptional = Optional.of(Endpoint.builder()
                .host("phonepe.com").port(8080).secure(false).build());
        when(serviceEndpointProvider.endpoint()).thenReturn(endpointOptional);
    }

}
