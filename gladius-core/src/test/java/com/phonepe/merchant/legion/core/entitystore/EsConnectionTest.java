package com.phonepe.merchant.legion.core.entitystore;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.phonepe.merchant.legion.core.AppConfig;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import org.apache.http.HttpHost;
import org.elasticsearch.client.RestHighLevelClient;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Arrays;

public class EsConnectionTest {

    @Mock
    private AppConfig mockAppConfig;

    @Mock
    private ESConfig mockESConfig;

    @Mock
    private RestHighLevelClient mockClient;

    @InjectMocks
    private ESConnection esConnection;

    @Captor
    ArgumentCaptor<HttpHost[]> httpHostCaptor;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        when(mockESConfig.getUserName()).thenReturn("user");
        when(mockESConfig.getPassword()).thenReturn("password");
        when(mockESConfig.getHosts()).thenReturn(Arrays.asList("localhost"));
        when(mockESConfig.isSecure()).thenReturn(true);
        when(mockAppConfig.getEsConfig()).thenReturn(mockESConfig);
        esConnection = new ESConnection(mockAppConfig);
    }

    @AfterEach
    public void tearDown() {
        Mockito.reset(mockAppConfig, mockESConfig, mockClient);
    }

    @Test
    public void testConstructor_ShouldInitializeClient() {
        assertNotNull(esConnection.client());
    }

    @Test
    public void testGetHostByNameThrowsException() {
        Mockito.when(mockESConfig.isSecure()).thenReturn(false);
        try (MockedStatic<InetAddress> mockedInetAddress = Mockito.mockStatic(InetAddress.class)) {
            mockedInetAddress.when(() -> InetAddress.getByName(anyString()))
                    .thenThrow(new UnknownHostException("Host not found"));
            assertThrows(LegionException.class, () -> {
                new ESConnection(mockAppConfig);
            });
        }
    }

    @Test
    public void testStop_ShouldCloseClient() throws IOException {
        esConnection.stop();
        verify(mockClient, times(0)).close();
    }

    @Test
    public void testGetHttpHosts_ShouldReturnHttpHosts() throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
        Method method = ESConnection.class.getDeclaredMethod("getHttpHosts");
        method.setAccessible(true);
        HttpHost[] httpHosts = (HttpHost[]) method.invoke(esConnection);

        assertEquals(1, httpHosts.length);
        assertEquals("localhost", httpHosts[0].getHostName());
    }
}


