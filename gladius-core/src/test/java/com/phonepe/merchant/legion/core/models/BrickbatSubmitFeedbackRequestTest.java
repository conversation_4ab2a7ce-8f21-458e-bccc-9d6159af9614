package com.phonepe.merchant.legion.core.models;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Assertions;

public class BrickbatSubmitFeedbackRequestTest {

    @Test
    void testGenerateAssetId_WithAssetId() {
        BrickbatSubmitFeedbackRequest request = BrickbatSubmitFeedbackRequest.builder()
                .assetId("testAssetId").build();
        String assetId = request.generateAssetId();
        Assertions.assertEquals("testAssetId", assetId);
    }

    @Test
    void testGenerateAssetId_WithOnlyMerchant() {
        BrickbatSubmitFeedbackRequest request = BrickbatSubmitFeedbackRequest.builder()
                .merchant(new StoreData("merchantId", "storeId")).build();
        String assetId = request.generateAssetId();
        Assertions.assertEquals("merchantId_storeId", assetId);
    }

    @Test
    void testGenerateAssetId_WithMerchantAndEmptyAssetId() {
        BrickbatSubmitFeedbackRequest request = BrickbatSubmitFeedbackRequest.builder()
                .assetId("")
                .merchant(new StoreData("merchantId", "storeId")).build();
        String assetId = request.generateAssetId();
        Assertions.assertEquals("merchantId_storeId", assetId);
    }

    @Test
    void testGenerateAssetId_WithNullMerchantAndAssetId() {
        BrickbatSubmitFeedbackRequest request = BrickbatSubmitFeedbackRequest.builder().build();
        String assetId = request.generateAssetId();
        Assertions.assertNull(assetId);
    }

    @Test
    void testGenerateAssetId_WithNullMerchantIdAndAssetId() {
        BrickbatSubmitFeedbackRequest request = BrickbatSubmitFeedbackRequest.builder()
                .merchant(new StoreData(null, null))
                .build();
        String assetId = request.generateAssetId();
        Assertions.assertNull(assetId);
    }

    @Test
    void testGenerateAssetId_WithNullStoreIdAndAssetId() {
        BrickbatSubmitFeedbackRequest request = BrickbatSubmitFeedbackRequest.builder()
                .merchant(new StoreData("merchantId", null))
                .build();
        String assetId = request.generateAssetId();
        Assertions.assertNull(assetId);
    }
}
