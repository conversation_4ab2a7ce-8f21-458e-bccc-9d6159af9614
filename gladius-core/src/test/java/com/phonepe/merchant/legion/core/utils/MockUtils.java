package com.phonepe.merchant.legion.core.utils;

import com.phonepe.models.response.GenericResponse;
import okhttp3.MediaType;
import okhttp3.ResponseBody;

/**
 * <AUTHOR> puri
 */
public class MockUtils {


    public static ResponseBody response(String responseJson) {
        ResponseBody response = ResponseBody.create(MediaType.parse("application/json"), responseJson);
        return response;
    }

    public static GenericResponse genericResponse(boolean success) {
        return GenericResponse.builder().success(success).build();
    }
}
