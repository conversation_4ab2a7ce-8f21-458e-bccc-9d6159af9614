package com.phonepe.merchant.legion.core.commands;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.ArgumentMatchers.any;

import com.phonepe.merchant.gladius.models.core.audit.Audit;
import com.phonepe.merchant.legion.core.daos.AuditDao;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.utils.EventConstants;
import org.hibernate.envers.query.criteria.AuditCriterion;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.List;

class AuditAccessCommandsTest {

    @Mock private AuditDao<SampleEntity> auditDao;
    @Mock private FoxtrotEventIngestionService eventIngestionService;

    private AuditAccessCommands<SampleEntity> auditAccessCommands;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        auditAccessCommands = new AuditAccessCommands<>(auditDao, eventIngestionService);
    }

    @Test
    void testGet_SuccessfulAuditRetrieval() {
        String objectId = "test-id";
        List<AuditCriterion> criteria = Collections.emptyList();
        Audit<SampleEntity> auditResult = Audit.<SampleEntity>builder().data(new SampleEntity()).build();

        // Mock the auditDao to return a sample audit result
        when(auditDao.select(objectId, criteria)).thenReturn(List.of(auditResult));

        // Call the get method
        List<Audit<SampleEntity>> result = auditAccessCommands.get(objectId, criteria);

        // Verify that the auditDao's select method was called and the result is as expected
        assertEquals(1, result.size());
        assertEquals(auditResult, result.get(0));
        verify(auditDao, times(1)).select(objectId, criteria);
        verify(eventIngestionService, never()).ingestDaoErrorEvent(any());
    }

    @Test
    void testGet_DaoExceptionHandled() {
        String objectId = "test-id";
        List<AuditCriterion> criteria = Collections.emptyList();
        Exception daoException = new RuntimeException("Database error");

        // Mock the auditDao to throw an exception
        when(auditDao.select(objectId, criteria)).thenThrow(daoException);

        // Verify that the exception is propagated and the error event is ingested
        LegionException thrownException = assertThrows(LegionException.class,
                () -> auditAccessCommands.get(objectId, criteria));
        assertEquals(CoreErrorCode.DAO_ERROR, thrownException.getErrorCode());

        // Verify that ingestDaoErrorEvent was called with the expected parameters
        verify(eventIngestionService, times(1)).ingestDaoErrorEvent(argThat(event ->
                EventConstants.AUDIT_SELECT.equals(event.getCommandName()) &&
                        "Database error".equals(event.getDaoErrorMessage()) &&
                        EventConstants.DB.equals(event.getSource())));
    }

    class SampleEntity {}
}
