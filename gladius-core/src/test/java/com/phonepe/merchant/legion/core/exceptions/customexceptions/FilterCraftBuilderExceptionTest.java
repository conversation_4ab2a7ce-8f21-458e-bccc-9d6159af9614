package com.phonepe.merchant.legion.core.exceptions.customexceptions;

import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;

import org.junit.Test;

import static org.junit.Assert.assertEquals;

public class FilterCraftBuilderExceptionTest {

    @Test
    public void testError() {
        // Arrange
        CoreErrorCode errorCode = CoreErrorCode.NOT_FOUND;
        String expectedMessage = "resource not found";
        // Act
        FilterCraftBuilderException exception = FilterCraftBuilderException.error(errorCode);
        // Assert
        assertEquals(expectedMessage, exception.getMessage());
    }
}