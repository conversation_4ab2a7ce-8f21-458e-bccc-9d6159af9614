package com.phonepe.merchant.legion.core.utils;


import com.phonepe.merchant.gladius.models.tasks.enums.Sorter;
import org.hibernate.criterion.Order;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class SortingOrderUtilTest {

    @Test
    void test() {
        Order orderDesc = SortingOrderUtil.getSortingOrderByField(Sorter.CREATED_AT);
        Order orderAsc = SortingOrderUtil.getSortingOrderByField(Sorter.CREATED_AT_ASC);
        Assertions.assertNotNull(orderDesc);
        Assertions.assertNotNull(orderAsc);
        Assertions.assertTrue(orderAsc.isAscending());
        Assertions.assertFalse(orderDesc.isAscending());
        Assertions.assertEquals("createdAt", orderAsc.getPropertyName());
    }

    @Test
    void testFailure() {
        Assertions.assertThrows(IllegalStateException.class, () -> SortingOrderUtil.getSortingOrder<PERSON>y<PERSON>ield(Sorter.DUE_DATE));
    }
}
