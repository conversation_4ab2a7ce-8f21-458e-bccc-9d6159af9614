package com.phonepe.merchant.legion.core.services;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.phonepe.merchant.legion.core.GladiusCoreBaseTest;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.utils.AuthHeaderProviderUtil;
import com.phonepe.merchant.legion.core.utils.HttpUtils;
import com.phonepe.merchant.legion.core.utils.LegionCoreConstants;
import com.phonepe.merchant.legion.core.utils.MockUtils;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.merchant.legion.location.model.common.responses.LocationListResponse;
import com.phonepe.merchant.legion.models.profile.request.VerificationMeta;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.models.profile.response.AgentProfilesInSectorResponse;
import com.phonepe.merchant.legion.models.profile.response.AgentSectorResponse;
import com.phonepe.merchant.legion.models.profile.response.Sector;
import com.phonepe.merchant.legion.models.profile.response.UserRestrictionResponse;
import com.phonepe.merchant.legion.models.profile.response.UserSectorResponse;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import okhttp3.Call;
import okhttp3.OkHttpClient;
import okhttp3.Protocol;
import okhttp3.Request;
import okhttp3.Response;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class LegionServiceTest extends GladiusCoreBaseTest {
    private static final Call call = mock(Call.class);
    private static final OkHttpClient client = mock(OkHttpClient.class);
    private static ObjectMapper objectMapper = new ObjectMapper();
    private static LegionService legionService;


    @BeforeClass
    public static void setupInner() {
        AuthHeaderProviderUtil.init(mock(OlympusIMClient.class));
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        HttpConfiguration clientConfiguration = mock(HttpConfiguration.class);
        getEndPointMock(LegionCoreConstants.LEGION_SERVICE);
        when(clientConfiguration.getUsername()).thenReturn("username");
        when(clientConfiguration.getPassword()).thenReturn("password");
        when(clientConfiguration.getClientId()).thenReturn("LegionService");
        try (MockedStatic<HttpUtils> httpUtil = Mockito.mockStatic(HttpUtils.class)) {
            httpUtil.when(() -> HttpUtils.makeOkHttpClient(any(), any()))
                    .thenReturn(client);
            legionService = spy(
                    new LegionService(clientConfiguration, serviceEndpointProviderFactoryMock, objectMapper, mock(MetricRegistry.class),
                            mock(FoxtrotEventIngestionService.class), serviceDiscoveryConfiguration));
        }

    }


    @Test
    public void getAllAccessibleSectorTest() throws IOException {
        when(serviceDiscoveryConfiguration.getEnvironment()).thenReturn("test");
        Response response = new Response.Builder().protocol(Protocol.HTTP_1_1)
                .code(200)
                .body(MockUtils.response(SerDe.writeValueAsString(GenericResponse.<UserSectorResponse>builder()
                        .success(true)
                        .data(UserSectorResponse.builder().sectors(Set.of(Sector.builder().sectorId("Sector1").build(), Sector.builder().sectorId("Sector2").build())).build()).build())))
                .request(new Request.Builder().url("http://legionservice.traefik.stg.com")
                        .build())
                .message("message")
                .build();
        when(client.newCall(any(Request.class))).thenReturn(call);
        when(call.execute()).thenReturn(response);
        List<String> sectorIds = legionService.getAllAccessibleSectors("tsmId", "token");
        Assert.assertNotNull(sectorIds);
        Assert.assertTrue(!sectorIds.isEmpty());
    }

    @Test
    public void isImageVerificationStatusPendingTest() throws IOException {
        String agentId = "testAgentId";
        when(serviceDiscoveryConfiguration.getEnvironment()).thenReturn("test");

        GenericResponse<VerificationMeta> mockResponse = GenericResponse.<VerificationMeta>builder()
                .success(true)
                .data(VerificationMeta.builder().docstoreId("id").build())
                .build();

        Response response = new Response.Builder().protocol(Protocol.HTTP_1_1)
                .code(200)
                .body(MockUtils.response(SerDe.writeValueAsString(mockResponse)))
                .request(new Request.Builder().url("http://legionservice.traefik.stg.com")
                        .build())
                .message("message")
                .build();
        when(client.newCall(any(Request.class))).thenReturn(call);
        when(call.execute()).thenReturn(response);
        GenericResponse<VerificationMeta> userRestrictions = legionService.isImageVerificationStatusPending(agentId);

        Assert.assertNotNull(userRestrictions);
    }

    @Test
    public void fetchUserRestrictionsTest() throws IOException {
        String agentId = "testAgentId";
        when(serviceDiscoveryConfiguration.getEnvironment()).thenReturn("test");

        GenericResponse<UserRestrictionResponse> mockResponse = GenericResponse.<UserRestrictionResponse>builder()
                .success(true)
                .data(UserRestrictionResponse.builder().enabledAttributes(Set.of()).allowedActions(List.of()).build())
                .build();

        Response response = new Response.Builder().protocol(Protocol.HTTP_1_1)
                .code(200)
                .body(MockUtils.response(SerDe.writeValueAsString(mockResponse)))
                .request(new Request.Builder().url("http://legionservice.traefik.stg.com")
                        .build())
                .message("message")
                .build();
        when(client.newCall(any(Request.class))).thenReturn(call);
        when(call.execute()).thenReturn(response);
        UserRestrictionResponse userRestrictions = legionService.fetchUserRestrictions(agentId);

        Assert.assertNotNull(userRestrictions);
    }

    @Test
    public void getAgentMetaTest() throws IOException {
        String agentId = "testAgentId";
        when(serviceDiscoveryConfiguration.getEnvironment()).thenReturn("test");

        GenericResponse<List<AgentProfile>> mockResponse = GenericResponse.<List<AgentProfile>>builder()
                .success(true)
                .data(List.of(AgentProfile.builder().build()))
                .build();

        Response response = new Response.Builder().protocol(Protocol.HTTP_1_1)
                .code(200)
                .body(MockUtils.response(SerDe.writeValueAsString(mockResponse)))
                .request(new Request.Builder().url("http://legionservice.traefik.stg.com")
                        .build())
                .message("message")
                .build();
        when(client.newCall(any(Request.class))).thenReturn(call);
        when(call.execute()).thenReturn(response);
        List<AgentProfile> agentProfiles = legionService.getAgentsMeta(Set.of(agentId));

        Assert.assertNotNull(agentProfiles);
    }

    @Test
    public void getAgentProfileTest() throws IOException {
        String agentId = "testAgentId";
        when(serviceDiscoveryConfiguration.getEnvironment()).thenReturn("test");

        GenericResponse<AgentProfile> mockResponse = GenericResponse.<AgentProfile>builder()
                .success(true)
                .data(AgentProfile.builder().build())
                .build();

        Response response = new Response.Builder().protocol(Protocol.HTTP_1_1)
                .code(200)
                .body(MockUtils.response(SerDe.writeValueAsString(mockResponse)))
                .request(new Request.Builder().url("http://legionservice.traefik.stg.com")
                        .build())
                .message("message")
                .build();
        when(client.newCall(any(Request.class))).thenReturn(call);
        when(call.execute()).thenReturn(response);
        AgentProfile agentProfile = legionService.getAgentProfile(agentId);

        Assert.assertNotNull(agentProfile);
    }

    @Test
    public void getAgentSectorsTest() throws IOException {
        String agentId = "testAgentId";
        when(serviceDiscoveryConfiguration.getEnvironment()).thenReturn("test");

        GenericResponse<List<AgentSectorResponse>> mockResponse = GenericResponse.<List<AgentSectorResponse>>builder()
                .success(true)
                .data(List.of(AgentSectorResponse.builder().sectorId("X").build()))
                .build();

        Response response = new Response.Builder().protocol(Protocol.HTTP_1_1)
                .code(200)
                .body(MockUtils.response(SerDe.writeValueAsString(mockResponse)))
                .request(new Request.Builder().url("http://legionservice.traefik.stg.com")
                        .build())
                .message("message")
                .build();
        when(client.newCall(any(Request.class))).thenReturn(call);
        when(call.execute()).thenReturn(response);
        List<AgentSectorResponse> sectors = legionService.getAgentSectors(agentId);

        Assert.assertNotNull(sectors);
    }

    @Test
    public void isAccessibleTest() throws IOException {
        String agentId = "testAgentId";
        when(serviceDiscoveryConfiguration.getEnvironment()).thenReturn("test");

        GenericResponse mockResponse = GenericResponse.builder()
                .success(true)
                .build();

        Response response = new Response.Builder().protocol(Protocol.HTTP_1_1)
                .code(200)
                .body(MockUtils.response(SerDe.writeValueAsString(mockResponse)))
                .request(new Request.Builder().url("http://legionservice.traefik.stg.com")
                        .build())
                .message("message")
                .build();
        when(client.newCall(any(Request.class))).thenReturn(call);
        when(call.execute()).thenReturn(response);
        legionService.isSectorAccessible("sectorId", agentId);
        verify(legionService, times(1)).isSectorAccessible("sectorId", agentId);
    }

    @Test
    public void isSectorAccessibleBooleanTest() throws IOException {
        String agentId = "testAgentId";
        when(serviceDiscoveryConfiguration.getEnvironment()).thenReturn("test");

        GenericResponse mockResponse = GenericResponse.builder()
                .success(true)
                .build();

        Response response = new Response.Builder().protocol(Protocol.HTTP_1_1)
                .code(200)
                .body(MockUtils.response(SerDe.writeValueAsString(mockResponse)))
                .request(new Request.Builder().url("http://legionservice.traefik.stg.com")
                        .build())
                .message("message")
                .build();
        when(client.newCall(any(Request.class))).thenReturn(call);
        when(call.execute()).thenReturn(response);
        Assert.assertTrue(legionService.isSectorAccessibleBoolean("sectorId", agentId));
    }


    @Test
    public void geActiveAgentAgentsInSectors() throws IOException{
        when(serviceDiscoveryConfiguration.getEnvironment()).thenReturn("test");
        AgentProfilesInSectorResponse data = AgentProfilesInSectorResponse.builder().profilesPerSector(Map.of("S1", List.of(AgentProfile.builder().build()))).build();
        GenericResponse<AgentProfilesInSectorResponse> mockResponse = GenericResponse.<AgentProfilesInSectorResponse>builder()
                .data(data)
                .success(true)
                .build();

        Response response = new Response.Builder().protocol(Protocol.HTTP_1_1)
                .code(200)
                .body(MockUtils.response(SerDe.writeValueAsString(mockResponse)))
                .request(new Request.Builder().url("http://legionservice.traefik.stg.com")
                        .build())
                .message("message")
                .build();
        when(client.newCall(any(Request.class))).thenReturn(call);
        when(call.execute()).thenReturn(response);
        Assert.assertEquals(data, legionService.getAgentProfilesInSector(List.of("S1")));
    }

    @Test
    public void geActiveAgentInSectors() throws IOException{
        when(serviceDiscoveryConfiguration.getEnvironment()).thenReturn("test");
        AgentProfile agentProfile1 = AgentProfile.builder().agentId("agentId1").build();
        AgentProfile agentProfile2 = AgentProfile.builder().agentId("agentId2").build();
        AgentProfilesInSectorResponse data = AgentProfilesInSectorResponse.builder().profilesPerSector(Map.of("sector1", List.of(agentProfile1, agentProfile2))).build();
        GenericResponse<AgentProfilesInSectorResponse> mockResponse = GenericResponse.<AgentProfilesInSectorResponse>builder()
                .data(data)
                .success(true)
                .build();

        Response response = new Response.Builder().protocol(Protocol.HTTP_1_1)
                .code(200)
                .body(MockUtils.response(SerDe.writeValueAsString(mockResponse)))
                .request(new Request.Builder().url("http://legionservice.traefik.stg.com")
                        .build())
                .message("message")
                .build();
        when(client.newCall(any(Request.class))).thenReturn(call);
        when(call.execute()).thenReturn(response);
        Assert.assertEquals(data, legionService.getAgentProfilesInSector(List.of("sector1")));
    }

    @Test
    public void listLocationsTest() throws IOException {
        when(serviceDiscoveryConfiguration.getEnvironment()).thenReturn("test");

        GenericResponse<LocationListResponse> mockResponse = GenericResponse.<LocationListResponse>builder()
                .success(true)
                .data(LocationListResponse.builder().build())
                .build();

        Response response = new Response.Builder().protocol(Protocol.HTTP_1_1)
                .code(200)
                .body(MockUtils.response(SerDe.writeValueAsString(mockResponse)))
                .request(new Request.Builder().url("http://legionservice.traefik.stg.com")
                        .build())
                .message("message")
                .build();
        when(client.newCall(any(Request.class))).thenReturn(call);
        when(call.execute()).thenReturn(response);
        LocationListResponse listResponse = legionService.listLocations("", List.of());

        Assert.assertNotNull(listResponse);
    }
}
