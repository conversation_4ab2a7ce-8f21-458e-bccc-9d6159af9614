package com.phonepe.merchant.legion.core.repository;
import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

import com.google.inject.Provider;
import com.phonepe.merchant.legion.core.entitystore.ESConnection;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.eventingestion.models.DaoErrorEvent;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.repository.impl.ESRepositoryImpl;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.util.Collections;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class ESRepositoryImplTest {

    @Mock
    private FoxtrotEventIngestionService eventIngestionService;

    private ESRepositoryImpl esRepository;

    @Mock
    private QueryBuilder queryBuilder;

    @Mock
    private AggregationBuilder aggregationBuilder;

    @Mock
    private SearchResponse searchResponse;

    @Mock
    private Provider<ESConnection> esConnectionProvider;
    @Mock
    private ESConnection esConnection;

    @Mock
    private RestHighLevelClient client;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        when(esConnectionProvider.get()).thenReturn(esConnection);
        when(esConnection.client()).thenReturn(client);
        esRepository = new ESRepositoryImpl(esConnectionProvider, eventIngestionService);
    }

    @Test
    public void testGetAggregationResult_Success() throws Exception {
        String index = "test-index";
        List<AggregationBuilder> aggregationBuilders = Collections.singletonList(aggregationBuilder);

        when(client.search(any(SearchRequest.class), eq(RequestOptions.DEFAULT)))
                .thenReturn(searchResponse);

        SearchResponse result = esRepository.getAggregationResult(index, queryBuilder, aggregationBuilders);

        assertNotNull(result);
        assertEquals(searchResponse, result);
        verify(client, times(1))
                .search(any(SearchRequest.class), eq(RequestOptions.DEFAULT));
    }

    @Test(expected = LegionException.class)
    public void testGetAggregationResult_Failure() throws Exception {
        String index = "test-index";
        List<AggregationBuilder> aggregationBuilders = Collections.singletonList(aggregationBuilder);
        IOException ioException = new IOException("Test exception");
        when(client.search(any(SearchRequest.class), eq(RequestOptions.DEFAULT)))
                .thenThrow(ioException);
        try {
            esRepository.getAggregationResult(index, queryBuilder, aggregationBuilders);
        } catch (LegionException e) {
            verify(eventIngestionService, times(1))
                    .ingestDaoErrorEvent(any(DaoErrorEvent.class));
            throw e;
        }
    }

}