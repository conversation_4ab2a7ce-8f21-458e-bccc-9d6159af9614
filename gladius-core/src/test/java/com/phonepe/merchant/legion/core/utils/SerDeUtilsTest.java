package com.phonepe.merchant.legion.core.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.merchant.filtercraft.internal.models.Filter;
import com.phonepe.merchant.filtercraft.internal.models.conditions.InCondition;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import java.util.List;

public class SerDeUtilsTest {

    private static ObjectMapper mapper;

    @BeforeAll
    public static void setup() {
        mapper = new ObjectMapper();
        SerDe.init(mapper);
    }

    @Test
    public void testToJsonNodeShouldConvertFilterObject() {
        // Create a sample Filter object similar to what createFilterCraftFilter() produces
        Filter filter = new Filter();
        InCondition<String> actionCondition = new InCondition<>();
        actionCondition.setField("actionId");
        actionCondition.setValues(List.of("A1", "A2"));

        InCondition<String> definitionCondition = new InCondition<>();
        definitionCondition.setField("definitionId");
        definitionCondition.setValues(List.of("D1", "D2"));

        filter.setOrConditions(List.of(List.of(actionCondition, definitionCondition)));

        // Act
        JsonNode jsonNode = SerDe.toJsonNode(filter);

        // Assert
        Assertions.assertNotNull(jsonNode);
        Assertions.assertTrue(jsonNode.has("orConditions"));
        Assertions.assertEquals(1, jsonNode.get("orConditions").size());

        JsonNode conditionsArray = jsonNode.get("orConditions").get(0);
        Assertions.assertEquals(2, conditionsArray.size());

        Assertions.assertEquals("actionId", conditionsArray.get(0).get("field").asText());
        Assertions.assertEquals("A1", conditionsArray.get(0).get("values").get(0).asText());

        Assertions.assertEquals("definitionId", conditionsArray.get(1).get("field").asText());
        Assertions.assertEquals("D1", conditionsArray.get(1).get("values").get(0).asText());
    }

    @Test
    public void testToJsonNodeShouldReturnNullForNullInput() {
        JsonNode jsonNode = SerDe.toJsonNode(null);
        Assertions.assertNull(jsonNode);
    }

    @Test
    public void testToJsonNodeShouldThrowLegionExceptionOnFailure() {
        Object invalid = new Object() {
            public Object getX() {
                throw new RuntimeException("Simulated failure");
            }
        };

        Assertions.assertThrows(LegionException.class, () -> {
            SerDe.toJsonNode(invalid);
        });
    }
}
