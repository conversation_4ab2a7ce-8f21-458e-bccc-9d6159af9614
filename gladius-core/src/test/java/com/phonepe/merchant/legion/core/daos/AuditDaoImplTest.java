package com.phonepe.merchant.legion.core.daos;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.phonepe.merchant.gladius.models.core.audit.Audit;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredAuditRevision;
import io.appform.dropwizard.sharding.utils.ShardCalculator;
import io.appform.dropwizard.sharding.utils.Transactions;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.envers.AuditReader;
import org.hibernate.envers.RevisionType;
import org.hibernate.envers.query.AuditQuery;
import org.hibernate.envers.query.criteria.AuditCriterion;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

class AuditDaoImplTest {

    @Mock private SessionFactory sessionFactory;
    @Mock private ShardCalculator<String> shardCalculator;
    @Mock private Session session;
    @Mock private AuditReader auditReader;
    @Mock private AuditQuery auditQuery;

    private AuditDaoImpl<SampleEntity> auditDao;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        auditDao = new AuditDaoImpl<>(Collections.singletonList(sessionFactory), SampleEntity.class, shardCalculator);
    }
    

    @Test
    void testSelect_ValidSelection() {
        String objectId = "test-id";
        List<AuditCriterion> criteria = Collections.emptyList();
        when(shardCalculator.shardId(objectId)).thenReturn(0);
        when(sessionFactory.getCurrentSession()).thenReturn(session);
        ;
        try (MockedStatic<Transactions> mockedTransactions = mockStatic(Transactions.class)) {
            List<Object[]> resultSet = new ArrayList<>();
            resultSet.add(new Object[]{new SampleEntity(), new StoredAuditRevision(), RevisionType.ADD });
            mockedTransactions.when(() -> Transactions.execute(any(), eq(true), any(), any()))
                    .thenReturn(resultSet);
            List<Audit<SampleEntity>> audits = auditDao.select(objectId, criteria);
            assertEquals(1, audits.size());
            assertNotNull(audits.get(0).getData());
            assertEquals(RevisionType.ADD, audits.get(0).getRevisionType());
            assertTrue(audits.get(0).getUpdatedAt().before(new Date()));
        }

    }
    
    class SampleEntity {
        
    }

}
