package com.phonepe.merchant.legion.core.services;

import com.phonepe.merchant.gladius.models.legion.v2.AgentProfileV2;
import com.phonepe.merchant.gladius.models.legion.v2.OrgProfile;
import com.phonepe.merchant.legion.core.config.LegionMigrationConfig;
import com.phonepe.merchant.legion.models.profile.enums.AgentStatus;
import com.phonepe.merchant.legion.models.profile.enums.AgentType;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.models.merchants.BusinessUnit;
import com.phonepe.merchant.legion.models.profile.enums.Region;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;
import java.util.List;
import java.util.Set;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Test class for LegionServiceAdapter
 * Demonstrates testing approach for org-level migration
 */
@RunWith(MockitoJUnitRunner.class)
public class LegionServiceAdapterTest {

    @Mock
    private LegionService legionServiceV1;

    @Mock
    private LegionServiceV2 legionServiceV2;

    @Mock
    private LegionMigrationConfig migrationConfig;

    private LegionServiceAdapter legionServiceAdapter;

    @Before
    public void setUp() {
        legionServiceAdapter = new LegionServiceAdapter(legionServiceV1, legionServiceV2, migrationConfig);
    }

    @Test
    public void testGetAgentProfile_V1_WhenV2Disabled() {
        // Given
        String agentId = "test-agent-1";
        when(migrationConfig.isUseV2ForReads()).thenReturn(false);
        
        AgentProfile expectedProfile = createMockAgentProfileV1(agentId);
        when(legionServiceV1.getAgentProfile(agentId)).thenReturn(expectedProfile);

        // When
        AgentProfile result = legionServiceAdapter.getAgentProfile(agentId);

        // Then
        assertEquals(expectedProfile, result);
        verify(legionServiceV1).getAgentProfile(agentId);
        verify(legionServiceV2, never()).getAgentProfileV2(anyString());
    }

    @Test
    public void testGetAgentProfile_V2_WhenV2Enabled() {
        // Given
        String agentId = "test-agent-1";
        when(migrationConfig.isUseV2ForReads()).thenReturn(true);
        
        AgentProfileV2 profileV2 = createMockAgentProfileV2(agentId);
        when(legionServiceV2.getAgentProfileV2(agentId)).thenReturn(profileV2);

        // When
        AgentProfile result = legionServiceAdapter.getAgentProfile(agentId);

        // Then
        assertNotNull(result);
        assertEquals(agentId, result.getAgentId());
        assertEquals("Test Agent", result.getName());
        assertEquals(AgentType.AGENT, result.getAgentType());
        verify(legionServiceV2).getAgentProfileV2(agentId);
        verify(legionServiceV1, never()).getAgentProfile(anyString());
    }

    @Test
    public void testGetAgentProfile_FallbackToV1_WhenV2Fails() {
        // Given
        String agentId = "test-agent-1";
        when(migrationConfig.isUseV2ForReads()).thenReturn(true);
        when(migrationConfig.isFallbackToV1OnError()).thenReturn(true);
        
        when(legionServiceV2.getAgentProfileV2(agentId)).thenThrow(new RuntimeException("V2 API Error"));
        
        AgentProfile expectedProfile = createMockAgentProfileV1(agentId);
        when(legionServiceV1.getAgentProfile(agentId)).thenReturn(expectedProfile);

        // When
        AgentProfile result = legionServiceAdapter.getAgentProfile(agentId);

        // Then
        assertEquals(expectedProfile, result);
        verify(legionServiceV2).getAgentProfileV2(agentId);
        verify(legionServiceV1).getAgentProfile(agentId);
    }

    @Test
    public void testGetAgentProfile_ThrowsException_WhenV2FailsAndFallbackDisabled() {
        // Given
        String agentId = "test-agent-1";
        when(migrationConfig.isUseV2ForReads()).thenReturn(true);
        when(migrationConfig.isFallbackToV1OnError()).thenReturn(false);
        
        RuntimeException expectedException = new RuntimeException("V2 API Error");
        when(legionServiceV2.getAgentProfileV2(agentId)).thenThrow(expectedException);

        // When & Then
        try {
            legionServiceAdapter.getAgentProfile(agentId);
            fail("Expected exception to be thrown");
        } catch (RuntimeException e) {
            assertEquals(expectedException, e);
        }
        
        verify(legionServiceV2).getAgentProfileV2(agentId);
        verify(legionServiceV1, never()).getAgentProfile(anyString());
    }

    @Test
    public void testGetAgentProfileV2_AlwaysUsesV2() {
        // Given
        String agentId = "test-agent-1";
        AgentProfileV2 expectedProfile = createMockAgentProfileV2(agentId);
        when(legionServiceV2.getAgentProfileV2(agentId)).thenReturn(expectedProfile);

        // When
        AgentProfileV2 result = legionServiceAdapter.getAgentProfileV2(agentId);

        // Then
        assertEquals(expectedProfile, result);
        verify(legionServiceV2).getAgentProfileV2(agentId);
        verify(legionServiceV1, never()).getAgentProfile(anyString());
    }

    @Test
    public void testIsSectorAccessible_V2_WhenEnabled() {
        // Given
        String sectorId = "sector-1";
        String agentId = "agent-1";
        when(migrationConfig.isUseV2ForReads()).thenReturn(true);

        // When
        legionServiceAdapter.isSectorAccessible(sectorId, agentId);

        // Then
        verify(legionServiceV2).isSectorAccessibleV2(sectorId, agentId);
        verify(legionServiceV1, never()).isSectorAccessible(anyString(), anyString());
    }

    @Test
    public void testIsSectorAccessible_V1_WhenV2Disabled() {
        // Given
        String sectorId = "sector-1";
        String agentId = "agent-1";
        when(migrationConfig.isUseV2ForReads()).thenReturn(false);

        // When
        legionServiceAdapter.isSectorAccessible(sectorId, agentId);

        // Then
        verify(legionServiceV1).isSectorAccessible(sectorId, agentId);
        verify(legionServiceV2, never()).isSectorAccessibleV2(anyString(), anyString());
    }

    @Test
    public void testConvertV2ToV1Profile() {
        // Given
        String agentId = "test-agent-1";
        AgentProfileV2 profileV2 = createMockAgentProfileV2(agentId);
        when(migrationConfig.isUseV2ForReads()).thenReturn(true);
        when(legionServiceV2.getAgentProfileV2(agentId)).thenReturn(profileV2);

        // When
        AgentProfile result = legionServiceAdapter.getAgentProfile(agentId);

        // Then
        assertNotNull(result);
        assertEquals(agentId, result.getAgentId());
        assertEquals("Test Agent", result.getName());
        assertEquals("<EMAIL>", result.getEmailId());
        assertEquals(AgentType.AGENT, result.getAgentType());
        assertEquals(BusinessUnit.OFFLINE, result.getBusinessUnit());
        assertEquals(Region.URBAN, result.getRegion());
        assertEquals("manager-1", result.getManagerId()); // Should use effective manager
        assertTrue(result.isActive());
    }

    // Helper methods to create mock objects
    private AgentProfile createMockAgentProfileV1(String agentId) {
        return AgentProfile.builder()
                .agentId(agentId)
                .name("Test Agent")
                .phoneNumber("1234567890")
                .emailId("<EMAIL>")
                .agentType(AgentType.AGENT)
                .businessUnit(BusinessUnit.OFFLINE)
                .region(Region.URBAN)
                .status(AgentStatus.ACTIVATED)
                .active(true)
                .managerId("manager-1")
                .lastActiveDate(new Date())
                .build();
    }

    private AgentProfileV2 createMockAgentProfileV2(String agentId) {
        return AgentProfileV2.builder()
                .agentId(agentId)
                .name("Test Agent")
                .phoneNumber("1234567890")
                .emailId("<EMAIL>")
                .agentType(AgentType.AGENT)
                .businessUnit(BusinessUnit.OFFLINE)
                .region(Region.URBAN)
                .status(AgentStatus.ACTIVATED)
                .active(true)
                .managerId("manager-1")
                .lastActiveDate(new Date())
                // Org-level fields
                .orgId("org-1")
                .orgName("Test Organization")
                .orgRoleId("org-role-1")
                .orgRoleName("Field Agent")
                .orgManagerId("org-manager-1")
                .orgAttributes(List.of(
                        OrgProfile.OrgAttribute.builder()
                                .key("businessUnit")
                                .value("offline")
                                .build(),
                        OrgProfile.OrgAttribute.builder()
                                .key("region")
                                .value("URBAN")
                                .build()
                ))
                .build();
    }
}
