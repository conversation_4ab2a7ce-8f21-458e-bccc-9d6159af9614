package com.phonepe.merchant.legion.core;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.merchant.legion.core.utils.SerDe;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

public class LegionCoreTest {

    public static ObjectMapper mapper;

    @BeforeClass
    public static void setup() throws Exception {
        mapper = new ObjectMapper();
        SerDe.init(mapper);
    }

    @Test
    public void testPlaceholder() {
        Assert.assertTrue(true);
    }
}
