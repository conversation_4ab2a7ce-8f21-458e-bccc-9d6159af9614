package com.phonepe.merchant.legion.core.services;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.phonepe.merchant.legion.core.GladiusCoreBaseTest;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.utils.AuthHeaderProviderUtil;
import com.phonepe.merchant.legion.core.utils.HttpUtils;
import com.phonepe.merchant.legion.core.utils.LegionCoreConstants;
import com.phonepe.merchant.legion.core.utils.MockUtils;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.models.merchants.scout.CompetitionQrResponse;
import com.phonepe.models.merchants.tasks.TaskVpaMeta;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.models.userservice.common.GenericError;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import okhttp3.Call;
import okhttp3.OkHttpClient;
import okhttp3.Protocol;
import okhttp3.Request;
import okhttp3.Response;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

public class IntelServiceTest extends GladiusCoreBaseTest {

    private static final Call call = mock(Call.class);
    private static final OkHttpClient client = mock(OkHttpClient.class);
    private static ObjectMapper objectMapper = new ObjectMapper();

    private static IntelService intelService;



    @BeforeClass
    public static void setupInner() {
        AuthHeaderProviderUtil.init(mock(OlympusIMClient.class));
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        HttpConfiguration clientConfiguration = mock(HttpConfiguration.class);
        getEndPointMock(LegionCoreConstants.INTEL_SERVICE);
        when(clientConfiguration.getUsername()).thenReturn("username");
        when(clientConfiguration.getPassword()).thenReturn("password");
        when(clientConfiguration.getClientId()).thenReturn("IntelService");
        try (MockedStatic<HttpUtils> httpUtil = Mockito.mockStatic(HttpUtils.class)) {
            httpUtil.when(() -> HttpUtils.makeOkHttpClient(any(), any()))
                    .thenReturn(client);
            intelService = spy(
                    new IntelService(clientConfiguration, serviceEndpointProviderFactoryMock, objectMapper, mock(MetricRegistry.class),
                            mock(FoxtrotEventIngestionService.class), serviceDiscoveryConfiguration));
        }
    }

    @Test
    public void testGetVpaDetailsWithException() throws IOException {
        when(serviceDiscoveryConfiguration.getEnvironment()).thenReturn("test");
        // Mocking dependencies
        Response response = new Response.Builder().protocol(Protocol.HTTP_1_1)
                .code(500)
                .body(MockUtils.response(SerDe.writeValueAsString(
                        GenericError.builder().code("INVALID_VPA_ID").build())))
                .request(new Request.Builder().url("http://intelservice.traefik.stg.com")
                        .build())
                .message("message")
                .build();
        when(client.newCall(any(Request.class))).thenReturn(call);
        when(call.execute()).thenReturn(response);
        // Test data
        String vpaId = "some_vpa_id";

        // Verify behavior
        Assertions.assertThrows(LegionException.class, () -> intelService.getVpaDetails(vpaId));
    }


    @Test
    public void testGetVpaDetails() throws IOException {
        when(serviceDiscoveryConfiguration.getEnvironment()).thenReturn("test");
        // Mocking dependencies
        Response response = new Response.Builder().protocol(Protocol.HTTP_1_1)
                .code(200)
                .body(MockUtils.response(SerDe.writeValueAsString(
                        GenericResponse.<CompetitionQrResponse>builder()
                        .success(true)
                        .data(CompetitionQrResponse.builder().build())
                        .build())))
                .request(new Request.Builder().url("http://intelservice.traefik.stg.com")
                        .build())
                .message("message")
                .build();
        when(client.newCall(any(Request.class))).thenReturn(call);
        when(call.execute()).thenReturn(response);
        // Test data
        String vpaId = "some_vpa_id";

        // Perform the method under test
       CompetitionQrResponse finalResponse = intelService.getVpaDetails(vpaId);
        // Verify behavior
        assertEquals(CompetitionQrResponse.class, finalResponse.getClass());
    }


    @Test
    public void testGetListOfVpas() throws IOException {
        when(serviceDiscoveryConfiguration.getEnvironment()).thenReturn("test");

        // Mocking dependencies
        Response response = new Response.Builder().protocol(Protocol.HTTP_1_1)
                .code(200)
                .body(MockUtils.response(SerDe.writeValueAsString(
                        GenericResponse.<List<TaskVpaMeta>>builder()
                                .success(true)
                                .data(List.of(TaskVpaMeta.builder().build()))
                                .build())))
                .request(new Request.Builder().url("http://intelservice.traefik.stg.com")
                        .build())
                .message("message")
                .build();
        when(client.newCall(any(Request.class))).thenReturn(call);
        when(call.execute()).thenReturn(response);

        // Test data
        Set<String> vpaIds = new HashSet<>();
        vpaIds.add("vpa1");
        vpaIds.add("vpa2");

        // Perform the method under test
        Set<TaskVpaMeta> result = intelService.getListOfVpas(vpaIds);

        // Verify behavior
        assertEquals(1, result.size()); // Assuming empty list returned for simplicity
    }
}
