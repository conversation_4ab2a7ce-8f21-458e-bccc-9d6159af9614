package com.phonepe.merchant.legion.core.flows;

import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineEvent;
import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState;
import com.phonepe.merchant.gladius.models.tasks.request.CreateTaskInstanceRequest;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventExecutor;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.flows.models.LegionStateMachineContext;
import com.phonepe.models.merchants.tasks.EntityType;
import io.github.fsm.StateMachine;
import io.github.fsm.models.entities.Context;
import io.github.fsm.models.entities.Transition;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


public class LegionStateMachineEngineEngineTest {


    @Mock
    private StateMachine<Context> engine;

    @Mock
    private FoxtrotEventExecutor eventExecutor;

    private LegionStateMachineEngine<LegionStateMachineContext> legionEngine;

    @Mock
    private LegionStateMachineContext context;

    @Mock
    private Transition transition;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        legionEngine = new LegionStateMachineEngine<LegionStateMachineContext>(engine, eventExecutor) {};
    }

    @Test
    void testFire_TransitionNotFound() throws Exception {
        when(context.getFrom()).thenReturn(LegionTaskStateMachineState.INITIATED);
        when(context.getCausedEvent()).thenReturn(LegionTaskStateMachineEvent.AVAILABLE);
        when(engine.getTransition(any(), any())).thenReturn(Optional.empty());

        LegionException thrownException = assertThrows(LegionException.class, () -> legionEngine.fire(context));

        assert(thrownException.getErrorCode() == CoreErrorCode.TRANSITION_NOT_FOUND);

        verify(engine, never()).fire(any(), any());
        verify(eventExecutor, never()).ingest(any());
    }

    @Test
    void testFire_LegionExceptionDuringEngineFire() throws Exception {
        when(context.getFrom()).thenReturn(LegionTaskStateMachineState.INITIATED);
        when(context.getCausedEvent()).thenReturn(LegionTaskStateMachineEvent.CREATED);
        when(context.getTo()).thenReturn(LegionTaskStateMachineState.CREATED);
        when(engine.getTransition(any(), any())).thenReturn(Optional.of(transition));
        when(context.getAdditionalContextData(CreateTaskInstanceRequest.class.getSimpleName(),
                CreateTaskInstanceRequest.class)).thenReturn(CreateTaskInstanceRequest.builder().entityId("entityId").taskDefinitionId("TD1")
                .campaignId("camp1").build());
        doThrow(LegionException.class).when(engine).fire(any(), any());

        assertThrows(LegionException.class, () -> legionEngine.fire(context));


        verify(eventExecutor, times(1)).ingest(any());
    }

    @Test
    void testFire_GenericExceptionDuringEngineFire() throws Exception {

        when(context.getFrom()).thenReturn(LegionTaskStateMachineState.INITIATED);
        when(context.getCausedEvent()).thenReturn(LegionTaskStateMachineEvent.CREATED);
        when(context.getTo()).thenReturn(LegionTaskStateMachineState.CREATED);
        when(engine.getTransition(any(), any())).thenReturn(Optional.of(transition));
        when(context.getAdditionalContextData(CreateTaskInstanceRequest.class.getSimpleName(),
                CreateTaskInstanceRequest.class)).thenReturn(CreateTaskInstanceRequest.builder().entityId("entityId").taskDefinitionId("TD1")
                .campaignId("camp1").build());
        doThrow(new RuntimeException("Mock runtime error")).when(engine).fire(any(), any());

        LegionException thrownException = assertThrows(LegionException.class, () -> legionEngine.fire(context));

        assert(thrownException.getErrorCode() == CoreErrorCode.INTERNAL_ERROR);
    }


    @Test
    void testFire_Success() throws Exception {
        // Mock the behavior for a successful transition
        when(context.getFrom()).thenReturn(LegionTaskStateMachineState.INITIATED);
        when(context.getCausedEvent()).thenReturn(LegionTaskStateMachineEvent.CREATED);
        when(context.getTo()).thenReturn(LegionTaskStateMachineState.CREATED);
        when(context.getAdditionalContextData(CreateTaskInstanceRequest.class.getSimpleName(),
                CreateTaskInstanceRequest.class)).thenReturn(CreateTaskInstanceRequest.builder().entityId("entityId").taskDefinitionId("TD1")
                .campaignId("camp1").build());
        when(context.getEntityType()).thenReturn(EntityType.STORE.name());
        when(engine.getTransition(any(), any())).thenReturn(Optional.of(transition));

        assertDoesNotThrow(() -> legionEngine.fire(context));

        verify(engine, times(1)).fire(any(), any());
        verify(eventExecutor, times(2)).ingest(any()); // One for state machine, one for task state
    }
}