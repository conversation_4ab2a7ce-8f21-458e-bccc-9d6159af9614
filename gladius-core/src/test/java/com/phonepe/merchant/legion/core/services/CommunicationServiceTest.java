package com.phonepe.merchant.legion.core.services;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.services.CommunicationService.CallTypes;
import com.phonepe.merchant.legion.core.utils.LegionCoreConstants;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.models.userservice.common.GenericError;
import com.phonepe.platform.http.v2.common.Endpoint;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.common.OkHttpUtils;
import com.phonepe.platform.http.v2.common.ServiceEndpointProvider;
import io.appform.ranger.discovery.bundle.ServiceDiscoveryConfiguration;
import java.io.IOException;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Supplier;
import okhttp3.Headers;
import okhttp3.HttpUrl;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

public class CommunicationServiceTest {

    public ObjectMapper objectMapper;
    private CommunicationService communicationService;
    private OkHttpClient client;
    private FoxtrotEventIngestionService eventIngestionService;
    private Response mockResponse;

    private HttpConfiguration httpConfigurationMock;

    private Supplier<ServiceEndpointProvider> serviceEndpointProvider;
    private ServiceEndpointProvider mockServiceEndpointProvider;

    @BeforeEach
    void setUp() {
        // init SerDe
        objectMapper = new ObjectMapper();
        SerDe.init(objectMapper);

        // Mock the ServiceEndpointProvider
        mockServiceEndpointProvider = mock(ServiceEndpointProvider.class);

        // Configure the mocked ServiceEndpointProvider
        Endpoint mockEndpoint = mock(Endpoint.class);
        when(mockServiceEndpointProvider.endpoint()).thenReturn(Optional.of(mockEndpoint));

        // Mock the Supplier<ServiceEndpointProvider>
        serviceEndpointProvider = mock(Supplier.class);

        // Configure the Supplier to return the mocked ServiceEndpointProvider
        when(serviceEndpointProvider.get()).thenReturn(mockServiceEndpointProvider);

        // basic mocks
        eventIngestionService = mock(FoxtrotEventIngestionService.class);
        mockResponse = mock(Response.class);
        client = mock(OkHttpClient.class);
        httpConfigurationMock = mock(HttpConfiguration.class);
        ServiceDiscoveryConfiguration serviceDiscoveryConfiguration = mock(ServiceDiscoveryConfiguration.class);

        eventIngestionService = mock(FoxtrotEventIngestionService.class);

        // Create an anonymous subclass of CommunicationService for testing
        communicationService = spy(
                new CommunicationService(httpConfigurationMock, client, serviceEndpointProvider, objectMapper,
                        eventIngestionService, serviceDiscoveryConfiguration) {

                });

        doNothing().when(communicationService)
                .ingestServiceFailureEvent(anyString(), anyInt(), anyInt());
        doNothing().when(communicationService)
                .ingestServiceFailureEvent(anyString(), anyInt());

    }

    @Test
    void is5xxTest() {
        Assertions.assertFalse(communicationService.is5xx(202));
        Assertions.assertTrue(communicationService.is5xx(500));
    }

    @Test
    void handleResponseFailureTest() {

        GenericError genericError = GenericError.builder()
                .code("TEST123")
                .message("TEST MESSAGE")
                .build();

        LegionException exception1 = assertThrows(LegionException.class, () -> {
            communicationService.handleResponseFailure("TEST_200", SerDe.writeValueAsString(genericError), 200);
        });
        LegionException exception2 = assertThrows(LegionException.class, () -> {
            communicationService.handleResponseFailure("TEST_500", SerDe.writeValueAsString(genericError), 500);
        });
    }


    @Test
    void callTest() {

        Headers headers = Headers.of(Map.of("Authorization", "ABCDEFGDFEGBDSFS"));
        Object requestBody = Map.of("workflowId", "workflowId");
        String callType = CallTypes.POST;

        doReturn(null).when(communicationService)
                .getEndPoint();
        Exception exception1 = assertThrows(Exception.class,
                () -> communicationService.call("TEST", "TEST", requestBody, callType, headers));

        doThrow(LegionException.propagate(CoreErrorCode.DAO_ERROR, new Exception("test case"))).when(
                        communicationService)
                .getEndPoint();

        Exception exception2 = assertThrows(LegionException.class,
                () -> communicationService.call("TEST", "TEST", requestBody, callType, headers));

        when(httpConfigurationMock.isSecure()).thenReturn(true);
        Endpoint mockEndpoint = mock(Endpoint.class);
        HttpUrl blankUrl = new HttpUrl.Builder().scheme("https")
                .host("www.example.com")
                .addPathSegment("api")
                .addPathSegment("data")
                .build();

        doReturn(Optional.of(mockEndpoint)).when(mockServiceEndpointProvider)
                .endpoint();
        when(mockEndpoint.url(anyString())).thenReturn(blankUrl);
        doReturn(mockResponse).when(communicationService)
                .callWithoutCheck(anyString(), anyString(), any(), any(), any(Headers.class));

        when(mockResponse.isSuccessful()).thenReturn(true);
        Response response = communicationService.call("TEST", "TEST", requestBody, callType, headers);
        Assertions.assertEquals(mockResponse, response);

        when(mockResponse.isSuccessful()).thenReturn(false);
        assertThrows(LegionException.class,
                () -> communicationService.call("TEST", "TEST", requestBody, callType, headers));

    }

    @Test
    void endPointTest() {
        doReturn(client).when(communicationService)
                .getClient();
        doReturn(mockServiceEndpointProvider).when(serviceEndpointProvider)
                .get();
        doReturn(Optional.empty()).when(mockServiceEndpointProvider)
                .endpoint();
        assertThrows(LegionException.class, () -> communicationService.getEndPoint());
    }

    @Test
    void deleteCallTypeTest() {
        HttpUrl url = HttpUrl.parse("http://localhost/test");
        Headers headers = new Headers.Builder().add("key", "value")
                .build();
        String requestEnv = "testEnv";

        Request request = communicationService.getRequest(url, null, CallTypes.DELETE, headers, requestEnv);

        assertNotNull(request);
        assertEquals("DELETE", Objects.requireNonNull(request.method())); // Verify HTTP method
        assertEquals(url, request.url()); // Verify URL
        assertEquals("value", request.header("key")); // Verify headers
        assertEquals(requestEnv, request.header(LegionCoreConstants.X_REQUEST_ENV)); // Verify X_REQUEST_ENV header
    }

    @Test
    void postCallTypeTest() {
        HttpUrl url = HttpUrl.parse("http://localhost/test");
        Headers headers = new Headers.Builder().add("key", "value")
                .build();
        String requestEnv = "testEnv";
        RequestBody body = RequestBody.create(MediaType.parse("application/json"), "{\"key\":\"value\"}");

        Request request = communicationService.getRequest(url, body, CallTypes.POST, headers, requestEnv);

        assertNotNull(request);
        assertEquals("POST", Objects.requireNonNull(request.method())); // Verify HTTP method
        assertEquals(url, request.url()); // Verify URL
        assertEquals("value", request.header("key")); // Verify headers
        assertEquals(requestEnv, request.header(LegionCoreConstants.X_REQUEST_ENV)); // Verify X_REQUEST_ENV header
        assertNotNull(request.body()); // Verify body is not null
    }

    @Test
    void postCallTypeNullBodyTest() {
        HttpUrl url = HttpUrl.parse("http://localhost/test");
        Headers headers = new Headers.Builder().add("key", "value")
                .build();
        String requestEnv = "testEnv";

        Request request = communicationService.getRequest(url, null, CallTypes.POST, headers, requestEnv);

        assertNotNull(request);
        assertEquals("POST", Objects.requireNonNull(request.method())); // Verify HTTP method
        assertEquals(url, request.url()); // Verify URL
        assertEquals("value", request.header("key")); // Verify headers
        assertEquals(requestEnv, request.header(LegionCoreConstants.X_REQUEST_ENV)); // Verify X_REQUEST_ENV header
        assertNotNull(request.body()); // Verify body is not null
    }

    @Test
    void putCallTypeTest() {
        HttpUrl url = HttpUrl.parse("http://localhost/test");
        Headers headers = new Headers.Builder().add("key", "value")
                .build();
        String requestEnv = "testEnv";
        RequestBody body = RequestBody.create(MediaType.parse("application/json"), "{\"key\":\"value\"}");

        Request request = communicationService.getRequest(url, body, CallTypes.PUT, headers, requestEnv);

        assertNotNull(request);
        assertEquals("PUT", Objects.requireNonNull(request.method())); // Verify HTTP method
        assertEquals(url, request.url()); // Verify URL
        assertEquals("value", request.header("key")); // Verify headers
        assertEquals(requestEnv, request.header(LegionCoreConstants.X_REQUEST_ENV)); // Verify X_REQUEST_ENV header
        assertNotNull(request.body()); // Verify body is not null
    }

    @Test
    void patchCallTypeTest() {
        HttpUrl url = HttpUrl.parse("http://localhost/test");
        Headers headers = new Headers.Builder().add("key", "value")
                .build();
        String requestEnv = "testEnv";
        RequestBody body = RequestBody.create(MediaType.parse("application/json"), "{\"key\":\"value\"}");

        Request request = communicationService.getRequest(url, body, CallTypes.PATCH, headers, requestEnv);

        assertNotNull(request);
        assertEquals("PATCH", Objects.requireNonNull(request.method())); // Verify HTTP method
        assertEquals(url, request.url()); // Verify URL
        assertEquals("value", request.header("key")); // Verify headers
        assertEquals(requestEnv, request.header(LegionCoreConstants.X_REQUEST_ENV)); // Verify X_REQUEST_ENV header
        assertNotNull(request.body()); // Verify body is not null
    }

    @Test
    void defaultCallTypeTest() {
        HttpUrl url = HttpUrl.parse("http://localhost/test");
        Headers headers = new Headers.Builder().add("key", "value")
                .build();
        String requestEnv = "testEnv";

        Request request = communicationService.getRequest(url, null, "UNKNOWN", headers, requestEnv);

        assertNotNull(request);
        assertEquals("GET", Objects.requireNonNull(request.method())); // Verify HTTP method
        assertEquals(url, request.url()); // Verify URL
        assertEquals("value", request.header("key")); // Verify headers
        assertEquals(requestEnv, request.header(LegionCoreConstants.X_REQUEST_ENV)); // Verify X_REQUEST_ENV header
    }

    @Test
    void bodySuccessTest() {
        // Mock the Response object
        Response response = mock(Response.class);

        // Mock OkHttpUtils behavior
        byte[] expectedBytes = "response body".getBytes();
        try (MockedStatic<OkHttpUtils> okHttpUtilsMock = mockStatic(OkHttpUtils.class)) {
            okHttpUtilsMock.when(() -> OkHttpUtils.bodyAsBytes(response))
                    .thenReturn(expectedBytes);

            // Call the method
            byte[] actualBytes = communicationService.body(response);

            // Assertions
            assertNotNull(actualBytes);
            assertArrayEquals(expectedBytes, actualBytes);
        }
    }

    @Test
    void bodyTest() {
        // Mock the Response object
        Response response = mock(Response.class);

        // Mock OkHttpUtils to throw IOException
        try (MockedStatic<OkHttpUtils> okHttpUtilsMock = mockStatic(OkHttpUtils.class)) {
            okHttpUtilsMock.when(() -> OkHttpUtils.bodyAsBytes(response))
                    .thenThrow(new IOException("Test IO Exception"));

            // Call the method and assert exception
            LegionException exception = assertThrows(LegionException.class, () -> communicationService.body(response));
            assertEquals(CoreErrorCode.IO_ERROR, exception.getErrorCode());
        }
    }

    @Test
    void bodyStrSuccessTest() {
        // Mock the Response object
        Response response = mock(Response.class);

        // Mock OkHttpUtils behavior
        String expectedString = "response body";
        try (MockedStatic<OkHttpUtils> okHttpUtilsMock = mockStatic(OkHttpUtils.class)) {
            okHttpUtilsMock.when(() -> OkHttpUtils.bodyAsString(response))
                    .thenReturn(expectedString);

            // Call the method
            String actualString = communicationService.bodyStr(response);

            // Assertions
            assertNotNull(actualString);
            assertEquals(expectedString, actualString);
        }
    }

    @Test
    void bodyStrTest() {
        // Mock the Response object
        Response response = mock(Response.class);

        // Mock OkHttpUtils to throw IOException
        try (MockedStatic<OkHttpUtils> okHttpUtilsMock = mockStatic(OkHttpUtils.class)) {
            okHttpUtilsMock.when(() -> OkHttpUtils.bodyAsString(response))
                    .thenThrow(new IOException("Test IO Exception"));

            // Call the method and assert exception
            LegionException exception = assertThrows(LegionException.class,
                    () -> communicationService.bodyStr(response));
            assertEquals(CoreErrorCode.IO_ERROR, exception.getErrorCode());
        }
    }
}