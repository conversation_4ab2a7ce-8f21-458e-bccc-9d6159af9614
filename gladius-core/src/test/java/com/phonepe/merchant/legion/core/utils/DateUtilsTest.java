package com.phonepe.merchant.legion.core.utils;

import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class DateUtilsTest {

    @Test
    public void testConvertToLocalDateTimeFromDate() {
        // Arrange
        Date date = new Date();
        // Act
        LocalDateTime result = DateUtils.convertToLocalDateTimeFromDate(date);
        // Assert
        assertEquals(date.toInstant().atZone(TimeZone.getDefault().toZoneId()).toLocalDateTime(), result);
    }

    @Test
    public void testConvertToDateFromLocalDateTime() {
        // Arrange
        LocalDateTime dateTime = LocalDateTime.now();
        // Act
        Date result = DateUtils.convertToDateFromLocalDateTime(dateTime);
        // Assert
        assertEquals(dateTime.atZone(TimeZone.getDefault().toZoneId()).toInstant().toEpochMilli(), result.getTime());
    }

    @Test
    public void testAddDaysToDate() {
        // Arrange
        Date date = new Date();
        int daysToAdd = 2;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, daysToAdd);
        Date expectedDate = calendar.getTime();
        // Act
        Date result = DateUtils.addDaysToDate(date, daysToAdd);
        // Assert
        assertEquals(expectedDate, result);
    }

    @Test
    public void testAddDaysToEpochDate() {
        // Arrange
        long date = new Date().getTime();
        int daysToAdd = 2;
        Instant instant = Instant.ofEpochMilli(date);
        Instant expectedInstant = instant.plus(daysToAdd, ChronoUnit.DAYS);
        long expectedDate = expectedInstant.toEpochMilli();
        // Act
        long result = DateUtils.addDaysToEpochDate(date, daysToAdd);
        // Assert
        assertEquals(expectedDate, result);
    }

    @Test
    public void testConvertEpochToDate() {
        // Arrange
        long epochTime = new Date().getTime();
        SimpleDateFormat dateFormat = new SimpleDateFormat("MMM, dd yyyy");
        String expectedDateString = dateFormat.format(new Date(epochTime));
        // Act
        String result = DateUtils.convertEpochToDate(epochTime, "MMM, dd yyyy");
        // Assert
        assertEquals(expectedDateString, result);
    }

    @Test
    public void testGetTimeInEpochInMS() {
        ZoneId zoneId = ZoneId.of("Asia/Calcutta");
        ZonedDateTime zonedDateTime = ZonedDateTime.ofInstant(Instant.ofEpochSecond(1725759537), zoneId);
        MockedStatic<ZonedDateTime> zonedDateTimeMockedStatic = Mockito.mockStatic(ZonedDateTime.class);
        zonedDateTimeMockedStatic.when(() -> ZonedDateTime.now(zoneId)).thenReturn(zonedDateTime);
        // Assert
        assertEquals(zonedDateTime.toEpochSecond()*1000, DateUtils.getTimeInEpochInMS());
        zonedDateTimeMockedStatic.close();
    }

    @Test
    public void testConvertDate() {
        // Define a sample date (e.g., January 1, 2023)
        Date date = new Date(123, 0, 1);  // Date(year - 1900, month - 1, day)

        // Define pattern and expected output
        String pattern = "yyyy-MM-dd";
        String expectedOutput = "2023-01-01";

        // Call the convertDate method
        String result = DateUtils.convertDate(pattern, date);

        // Assert the result
        assertEquals(expectedOutput, result, "The formatted date should match the expected output.");
    }

    @Test
    public void testGetEpochRangeForPreset() {
        ZoneId istZone = ZoneId.of("Asia/Kolkata");
        LocalDate today = LocalDate.now(istZone);

        // TODAY
        Pair<Long, Long> todayRange = DateUtils.getEpochRangeForPreset(DateUtils.Preset.TODAY);
        long todayStart = today.atStartOfDay(istZone).toEpochSecond();
        long todayEnd = today.plusDays(1).atStartOfDay(istZone).toEpochSecond() - 1;
        assertEquals(todayStart, todayRange.getLeft());
        assertEquals(todayEnd, todayRange.getRight());

        // YESTERDAY
        Pair<Long, Long> yesterdayRange = DateUtils.getEpochRangeForPreset(DateUtils.Preset.YESTERDAY);
        long yStart = today.minusDays(1).atStartOfDay(istZone).toEpochSecond();
        long yEnd = today.atStartOfDay(istZone).toEpochSecond() - 1;
        assertEquals(yStart, yesterdayRange.getLeft());
        assertEquals(yEnd, yesterdayRange.getRight());

        // THIS_WEEK
        Pair<Long, Long> weekRange = DateUtils.getEpochRangeForPreset(DateUtils.Preset.THIS_WEEK);
        LocalDate monday = today.with(DayOfWeek.MONDAY);
        long weekStart = monday.atStartOfDay(istZone).toEpochSecond();
        long weekEnd = today.plusDays(1).atStartOfDay(istZone).toEpochSecond() - 1;
        assertEquals(weekStart, weekRange.getLeft());
        assertEquals(weekEnd, weekRange.getRight());

        // THIS_MONTH
        Pair<Long, Long> monthRange = DateUtils.getEpochRangeForPreset(DateUtils.Preset.THIS_MONTH);
        LocalDate firstOfMonth = today.withDayOfMonth(1);
        long monthStart = firstOfMonth.atStartOfDay(istZone).toEpochSecond();
        long monthEnd = today.plusDays(1).atStartOfDay(istZone).toEpochSecond() - 1;
        assertEquals(monthStart, monthRange.getLeft());
        assertEquals(monthEnd, monthRange.getRight());

        // PREVIOUS_MONTH
        Pair<Long, Long> prevMonthRange = DateUtils.getEpochRangeForPreset(DateUtils.Preset.PREVIOUS_MONTH);
        LocalDate firstDayPrevMonth = today.minusMonths(1).withDayOfMonth(1);
        LocalDate lastDayPrevMonth = firstDayPrevMonth.withDayOfMonth(firstDayPrevMonth.lengthOfMonth());
        long prevMonthStart = firstDayPrevMonth.atStartOfDay(istZone).toEpochSecond();
        long prevMonthEnd = lastDayPrevMonth.plusDays(1).atStartOfDay(istZone).toEpochSecond() - 1;
        assertEquals(prevMonthStart, prevMonthRange.getLeft());
        assertEquals(prevMonthEnd, prevMonthRange.getRight());
    }


}