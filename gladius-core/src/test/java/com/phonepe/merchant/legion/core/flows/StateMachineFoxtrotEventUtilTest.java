package com.phonepe.merchant.legion.core.flows;

import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState;
import com.phonepe.merchant.gladius.models.tasks.request.CreateTaskInstanceRequest;
import com.phonepe.merchant.legion.core.eventingestion.models.TaskCreationFailureEvent;
import com.phonepe.merchant.legion.core.flows.models.LegionStateMachineContext;
import com.phonepe.platform.eventingestion.model.Event;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


public class StateMachineFoxtrotEventUtilTest {

    private static final String ERROR_MESSAGE = "Duplicate Task Creation";


    @Mock
    private static LegionStateMachineContext mockContext;
    @Mock
    private static CreateTaskInstanceRequest mockRequest;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        reset(mockContext, mockRequest);
    }

    @Test
    void toTaskStateTransitionFoxtrotEvent() {
        when(mockContext.getEntityId()).thenReturn("EID_TEST_001");
        when(mockContext.getFrom()).thenReturn(LegionTaskStateMachineState.INITIATED);
        when(mockContext.getTo()).thenReturn(LegionTaskStateMachineState.CREATED);

        when(mockContext.getAdditionalContextData(
                eq(CreateTaskInstanceRequest.class.getSimpleName()),
                eq(CreateTaskInstanceRequest.class)))
                .thenReturn(mockRequest);

        when(mockRequest.getEntityId()).thenReturn("EID_TEST_001");
        when(mockRequest.getTaskDefinitionId()).thenReturn("TASK_DEF_TCF");
        when(mockRequest.getCampaignId()).thenReturn("CAMPAIGN_TCF");

            Event<TaskCreationFailureEvent> result =
                    StateMachineFoxtrotEventUtil.toTaskCreationFailureFoxtrotEvent(mockContext, ERROR_MESSAGE);

            assertNotNull(result);
            assertEquals("TASK_CREATION_FAILURE_EVENT", result.getEventType());

            TaskCreationFailureEvent data = result.getEventData();
            assertNotNull(data);
            assertEquals("EID_TEST_001", data.getEntityId());
            assertEquals("INITIATED", data.getFromTaskState());
            assertEquals("CREATED", data.getToTaskState());

            verify(mockContext).getAdditionalContextData(any(), any());
        }

    @Test
    void toTaskStateTransitionFoxtrotEventNullFromState() {
        when(mockContext.getEntityId()).thenReturn("EID_TEST_001");

        when(mockContext.getAdditionalContextData(
                eq(CreateTaskInstanceRequest.class.getSimpleName()),
                eq(CreateTaskInstanceRequest.class)))
                .thenReturn(mockRequest);

        when(mockRequest.getEntityId()).thenReturn("EID_TEST_001");
        when(mockRequest.getTaskDefinitionId()).thenReturn("TASK_DEF_TCF");
        when(mockRequest.getCampaignId()).thenReturn("CAMPAIGN_TCF");

        Event<TaskCreationFailureEvent> result =
                StateMachineFoxtrotEventUtil.toTaskCreationFailureFoxtrotEvent(mockContext, ERROR_MESSAGE);

        assertNotNull(result);
        assertEquals("TASK_CREATION_FAILURE_EVENT", result.getEventType());

        TaskCreationFailureEvent data = result.getEventData();
        assertNotNull(data);
        assertEquals("EID_TEST_001", data.getEntityId());

        verify(mockContext).getAdditionalContextData(any(), any());
    }
    }


