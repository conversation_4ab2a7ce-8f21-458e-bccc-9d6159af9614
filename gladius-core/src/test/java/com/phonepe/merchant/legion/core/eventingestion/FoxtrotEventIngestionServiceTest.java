package com.phonepe.merchant.legion.core.eventingestion;

import com.phonepe.dataplatform.EventIngestorClient;
import com.phonepe.merchant.legion.core.AppConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class FoxtrotEventIngestionServiceTest {

    private EventIngestorClient mockClient;
    private EventIngestionProvider mockProvider;
    private FoxtrotEventIngestionService ingestionService;

    @BeforeEach
    void setUp() {
        mockClient = mock(EventIngestorClient.class);
        mockProvider = mock(EventIngestionProvider.class);

        when(mockProvider.getEventIngestorClient()).thenReturn(mockClient);

        AppConfig config = mock(AppConfig.class);
        when(config.isEventIngestionEnabled()).thenReturn(true);

        ingestionService = new FoxtrotEventIngestionService(mockProvider, config);
    }

    @Test
    void testIngestSelfAssignmentLimitBreached() throws Exception {
        // given
        String taskInstanceId = "task-123";
        String userId = "user-456";
        doNothing().when(mockClient).send(anyList());
        ingestionService.ingestSelfAssignmentLimitBreached(taskInstanceId, userId, 5, 7);

    }
}