package com.phonepe.merchant.legion.core.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.models.profile.enums.AgentType;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.models.common.Location;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.olympus.im.models.authn.UserType;
import com.phonepe.olympus.im.models.user.UserAuthDetails;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;

import static com.phonepe.gandalf.models.authn.UserType.SYSTEM;
import static com.phonepe.merchant.legion.core.exceptions.CoreErrorCode.USER_ID_NOT_FOUND;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class AuthUserDetailsTest {

    private UserDetails gandalfUserDetails;
    private ServiceUserPrincipal olympusUserPrincipal;
    private final UserAuthDetails olympusUserAuthDetails = mock(UserAuthDetails.class);
    private final com.phonepe.olympus.im.models.user.UserDetails olympusUserDetails =
            mock(com.phonepe.olympus.im.models.user.UserDetails.class);

    private static ObjectMapper mapper;

    @BeforeEach
    void setUp() {
        // Mock Gandalf UserDetails
        gandalfUserDetails = mock(UserDetails.class);
        when(gandalfUserDetails.getUserId()).thenReturn("gandalfUserId");
        when(gandalfUserDetails.getUserType()).thenReturn(SYSTEM);
        when(gandalfUserDetails.getName()).thenReturn("Gandalf User");
        when(gandalfUserDetails.getGroupName()).thenReturn("Gandalf Organization");
        when(gandalfUserDetails.getExternalReferenceId()).thenReturn("gandalfExternalId");

        // Mock Olympus UserDetails and UserAuthDetails
        when(olympusUserDetails.getUserId()).thenReturn("olympusUserId");
        when(olympusUserDetails.getUserType()).thenReturn(UserType.SYSTEM);
        when(olympusUserDetails.getName()).thenReturn("Olympus User");
        when(olympusUserDetails.getOrganisationId()).thenReturn("Olympus Organization");

        when(olympusUserAuthDetails.getUserDetails()).thenReturn(olympusUserDetails);
        when(olympusUserAuthDetails.getRoles()).thenReturn(List.of("legionUser", "LEGIONUser"));

        olympusUserPrincipal = mock(ServiceUserPrincipal.class);
        when(olympusUserPrincipal.getUserAuthDetails()).thenReturn(olympusUserAuthDetails);
        mapper = new ObjectMapper();
        SerDe.init(mapper);
    }

    @Test
    void testGetLegionUserId_WithGandalfUserDetails() {
        String userId = AuthUserDetails.getLegionUserId(null, gandalfUserDetails, null);
        assertNotNull(userId);
        assertEquals("gandalfExternalId", userId);
    }

    @Test
    void testGetLegionUserId_WithOlympusUserPrincipal() {
        String userId = AuthUserDetails.getLegionUserId(null, null, olympusUserPrincipal);
        assertNotNull(userId);
        assertEquals("olympusUserId", userId);
    }

    @Test
    void testGetLegionUserId_WithValidDetails() {
        String userId = AuthUserDetails.getLegionUserId(null, gandalfUserDetails, olympusUserPrincipal);
        assertEquals("olympusUserId", userId);
    }

    @Test
    void testGetLegionUserId_WithNullDetails_ShouldThrowException() {
        assertThrows(LegionException.class, () -> AuthUserDetails.getLegionUserId(null, null, null), USER_ID_NOT_FOUND.getMessage());
    }

    @Test
    void testGetLegionUserId_WithValidAgentProfile() {
        String userId = AuthUserDetails.getLegionUserId(AgentProfile.builder().agentId("agentId").agentType(AgentType.BDE).build(),
                null, null);
        assertEquals("agentId", userId);
    }

    @Test
    void testGetLocationFromClientContext_HappyCase() {
        String clientContext = "{\"device\":{\"location\":{\"latitude\":10.4,\"longitude\":12.5}}}";
        Location expectedResult = Location.builder().latitude(10.4).longitude(12.5).build();
        assertEquals(expectedResult, AuthUserDetails.getLocationFromClientContext(clientContext));
    }

    @Test
    void testGetLocationFromClientContext_ValidationFailureCases() {
        String clientContext = null;
        assertNull(AuthUserDetails.getLocationFromClientContext(clientContext));

        clientContext = "";
        assertNull(AuthUserDetails.getLocationFromClientContext(clientContext));

        clientContext = "{\"device\":{\"location\":{}}}";
        assertNull(AuthUserDetails.getLocationFromClientContext(clientContext));

        clientContext = "{\"device\":{\"location\":{\"latitude\":10.4}}}";
        assertNull(AuthUserDetails.getLocationFromClientContext(clientContext));

        clientContext = "{\"device\":{\"location\":{\"latitude\":{},\"longitude\":{}}}}";
        assertNull(AuthUserDetails.getLocationFromClientContext(clientContext));

        clientContext = "{\"device\":{\"location\":{\"latitude\":10.4,\"longitude\":{}}}}";
        assertNull(AuthUserDetails.getLocationFromClientContext(clientContext));
    }

    @Test
    void testGetLocationFromClientContext_ExceptionCase() {
        String clientContext = "{\"device\":{\"location\":{\"latitude\":,\"longitude\":{}}}}";
        assertNull(AuthUserDetails.getLocationFromClientContext(clientContext));
    }
}
