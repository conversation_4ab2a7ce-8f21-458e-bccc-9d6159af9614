package com.phonepe.merchant.legion.core.cache;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.codahale.metrics.MetricRegistry;
import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.core.exceptions.ErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.lang.reflect.Field;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;

public class AsyncCacheTest {

    @Mock
    private CacheName mockCacheName;

    @Mock
    private CacheConfig mockCacheConfig;

    @Mock
    private Fetcher<String, String> mockFetcher;

    @Mock
    private MetricRegistry mockMetricRegistry;

    private AsyncCache<String, String> asyncCache;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        when(mockCacheConfig.getMaxElements()).thenReturn(100);
        when(mockCacheConfig.getExpiryInSeconds()).thenReturn(60);
        when(mockCacheConfig.getRefreshInSeconds()).thenReturn(30);

        asyncCache = new AsyncCache<>(mockCacheName, mockCacheConfig, mockFetcher, mockMetricRegistry);
    }

    @Test
    public void testConstructor_NullFetcher_ShouldThrowException() {
        assertThrows(IllegalArgumentException.class, () -> {
            new AsyncCache<>(mockCacheName, mockCacheConfig, null, mockMetricRegistry);
        });
    }

    @Test
    public void testConstructor_ValidArguments_ShouldCreateInstance() {
        AsyncCache<String, String> cache = new AsyncCache<>(mockCacheName, mockCacheConfig, mockFetcher, mockMetricRegistry);
        assertNotNull(cache);
    }

    @Test
    public void testGetValidKeySuccess() throws Exception {
        AsyncCache<String, String> spyCache = spy(asyncCache);
        AsyncLoadingCache<String, String> mockAsyncLoadingCache = mock(AsyncLoadingCache.class);
        CompletableFuture<String> future = CompletableFuture.completedFuture("value");
        when(mockAsyncLoadingCache.get(anyString())).thenReturn(future);
        Field field = AsyncCache.class.getDeclaredField("cache");
        field.setAccessible(true);
        field.set(spyCache, mockAsyncLoadingCache);
        assertEquals("value", spyCache.get("key"));
    }

    @Test
    public void testGetValidKeyFailure() throws Exception {
        AsyncCache<String, String> spyCache = spy(asyncCache);
        AsyncLoadingCache<String, String> mockAsyncLoadingCache = mock(AsyncLoadingCache.class);
        CompletableFuture<String> future = new CompletableFuture<>();
        future.completeExceptionally(new Exception("Fetch error"));
        when(mockAsyncLoadingCache.get(anyString())).thenReturn(future);
        Field field = AsyncCache.class.getDeclaredField("cache");
        field.setAccessible(true);
        field.set(spyCache, mockAsyncLoadingCache);
        assertThrows(LegionException.class, () -> spyCache.get("key"));
    }

    @Test
    public void testGetValidKeyWithFetcherSuccess() throws Exception {
        AsyncCache<String, String> spyCache = spy(asyncCache);
        AsyncLoadingCache<String, String> mockAsyncLoadingCache = mock(AsyncLoadingCache.class);
        when(mockAsyncLoadingCache.get(anyString(), any(Function.class)))
                .thenAnswer(invocation -> CompletableFuture
                        .completedFuture(mockFetcher.fetch(invocation.getArgument(0))));
        // Use reflection to set the mocked cache
        Field field = AsyncCache.class.getDeclaredField("cache");
        field.setAccessible(true);
        field.set(spyCache, mockAsyncLoadingCache);

        // Mock the fetcher fetch method
        when(mockFetcher.fetch("key")).thenReturn("fetchedValue");
        // Assert the fetched value is returned
        assertEquals("fetchedValue", spyCache.get("key", mockFetcher));
        // Verify the fetcher is called
        verify(mockFetcher, times(1)).fetch("key");
    }

    @Test
    public void testGetWithFetcherFailure() throws Exception {
        AsyncCache<String, String> spyCache = spy(asyncCache);
        AsyncLoadingCache<String, String> mockAsyncLoadingCache = mock(AsyncLoadingCache.class);
        when(mockAsyncLoadingCache.get(anyString(), any(Function.class)))
                .thenThrow(LegionException.error(CoreErrorCode.INTERNAL_ERROR));
        Field field = AsyncCache.class.getDeclaredField("cache");
        field.setAccessible(true);
        field.set(spyCache, mockAsyncLoadingCache);
        assertThrows(LegionException.class, () -> spyCache.get("key", mockFetcher));
        when(mockAsyncLoadingCache.get(anyString(), any(Function.class)))
                .thenThrow(LegionException.error(CoreErrorCode.INTERNAL_ERROR));
        assertThrows(LegionException.class, () -> spyCache.get("key", mockFetcher));
    }

}
