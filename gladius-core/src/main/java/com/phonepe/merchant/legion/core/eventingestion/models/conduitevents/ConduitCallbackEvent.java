package com.phonepe.merchant.legion.core.eventingestion.models.conduitevents;

import com.phonepe.merchant.gladius.models.reporting.QueryType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> puri
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConduitCallbackEvent {

    String jobId;

    String docStoreId;

    String jobStatus;

    QueryType queryType;
}
