package com.phonepe.merchant.legion.core;

import com.phonepe.merchant.legion.core.models.FormConfig;
import com.phonepe.merchant.legion.core.models.FormMeta;
import com.phonepe.merchant.legion.core.models.FormType;
import com.phonepe.merchant.legion.models.profile.enums.AgentType;
import com.phonepe.models.merchants.BusinessUnit;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@NoArgsConstructor
@Slf4j

public class FormsDataLoader {
    private static Map<FormType, Map<String, FormConfig>> formsData;
    private static final String ALL_BU_ALL_AGENTS = "all_bu_all_agents";

    public static void fillUpFormsData(Map<FormType, Map<String, FormConfig>> formsData, FormType formType, String key, FormConfig formConfig) {
        if (formsData.containsKey(formType)) {
            formsData.get(formType).put(key, formConfig);
        } else {
            Map<String, FormConfig> map = new HashMap<>();
            map.put(key, formConfig);
            formsData.put(formType, map);
        }
    }

    public static void initialize(Map<FormType, List<FormMeta>> formConfigs) {
        formsData = new HashMap<>();

        formConfigs.forEach((formType, formMetas) ->
                formMetas.forEach(formMeta -> {

                    if (formMeta.getMerchantBusinessUnits() != null && formMeta.getAgentRoles() != null) { // if both filters are present
                        for (BusinessUnit businessUnit : formMeta.getMerchantBusinessUnits()) {
                            for (AgentType agentType : formMeta.getAgentRoles()) {
                                String key = businessUnit.toString() + ':' + agentType.toString();
                                fillUpFormsData(formsData, formType, key, formMeta.getFormConfig());
                            }
                        }
                    } else if (formMeta.getMerchantBusinessUnits() != null) { // if only BU filter is present
                        formMeta.getMerchantBusinessUnits().forEach(businessUnit ->
                            fillUpFormsData(formsData, formType, businessUnit.toString(), formMeta.getFormConfig()));
                    } else if (formMeta.getAgentRoles() != null) { //if agent type filter is present
                        formMeta.getAgentRoles().forEach(agentType -> fillUpFormsData(formsData, formType, agentType.toString(), formMeta.getFormConfig()));
                    } else { //if no filter is present
                        fillUpFormsData(formsData, formType, ALL_BU_ALL_AGENTS, formMeta.getFormConfig());
                    }
                }));
        log.info(formsData.toString());
    }

    public static FormConfig getFormConfig(FormType formType, String key) {
        if (!formsData.containsKey(formType)) {
            return null;
        }
        return formsData.get(formType).get(key);
    }

}
