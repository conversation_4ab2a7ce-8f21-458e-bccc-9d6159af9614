package com.phonepe.merchant.legion.core.eventingestion.models;

import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCompleteRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR> puri
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClockworkRetryEvent {

    TaskCompleteRequest taskCompleteRequest;

    String clockworkId;

    Date nextScheduledTime;
}
