package com.phonepe.merchant.legion.core.eventingestion.models.taskevents;

import com.phonepe.merchant.legion.models.profile.enums.AgentType;
import com.phonepe.models.merchants.tasks.EligibleEventBasedTaskType;
import com.phonepe.models.merchants.tasks.EventBasedTaskCreationClient;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EventBasedTaskCreationFailed {

    String campaignId;

    String taskDefinitionId;

    String entityId;

    String agentId;

    AgentType role;

    Boolean markAvailable;

    String errorMessage;

    EventBasedTaskCreationClient client;

    EligibleEventBasedTaskType taskType;

}
