package com.phonepe.merchant.legion.core.utils;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class LegionCoreConstants {
    public static final String KILLSWITCH_CLIENT = "gladius";
    public static final String ERROR_CONTEXT_KEY_CAUSE = "cause";
    public static final String TR_OPEN = "<tr>";
    public static final String TR_CLOSE = "</tr>";
    public static final String TD_OPEN = "<th><h2>";
    public static final String TD_CLOSE = "</h2></th>";
    public static final String HTML_START = "<!DOCTYPE html><html><body><h1><center>Survey result</center></h1><table style = 'width:100%'><tr><th><h2>Question</h2></th><th><h2>Answer</h2></th></tr>";
    public static final String HTML_END =  "</table></body></html>";
    public static final String ANCHOR_START =  "<a href='tel:";
    public static final String ANCHOR_END =  "</a>";
    public static final String PHONE_NUMBER_REGEX =  "^(\\+91[\\-\\s]?)?[0]?(91)?[6789]\\d{9}$";
    public static final String PREFIX = "prefix";
    public static final String TOKEN = "token";
    public static final String O_BEARER = "O-Bearer";
    public static final String BEARER = "Bearer";

    /* Gandalf Service Constants */
    public static final String AUTH_NAME = "authBearer";

    // paradox service constants
    public static final String MERCHANT_ID = "merchantId";
    public static final String TASK_ID = "taskId";
    public static final String INVALID_TASK_ERROR = "INVALID_TASK";
    public static final String ACTIVITY_TYPE = "activityType";
    public static final String EDC_REGISTRATION = "EDC_REGISTRATION";
    public static final String EDC_REVERSE_PICKUP = "EDC_REVERSE_PICKUP";
    public static final String STORE_ID = "storeId";
    public static final String X_REQUEST_ENV = "X-REQUEST-ENV";
    public static final String CLOCKWORK_SERVICE = "ClockWorkService";
    public static final String FOXTROT_SERVICE = "FoxtrotService";
    public static final String BRICKBAT_SERVICE = "BrickbatService";
    public static final String ATLAS_SERVICE = "AtlasService";
    public static final String SCOUT_SERVICE = "ScoutService";
    public static final String INTEL_SERVICE = "IntelService";
    public static final String MERCHANT_ONBOARDING_SERVICE = "MerchantOnboardingService";
    public static final String MERCHANT_SERVICE = "MerchantService";
    public static final String LEGION_SERVICE = "LegionService";
    public static final String PARADOX_SERVICE = "ParadoxService";
    public static final String ODIN_SERVICE = "OdinService";
    public static final String FORTUNA_SERVICE = "FortunaService";
    public static final String HERMOD_SERVICE = "HermodService";
    public static final String KILLSWITCH_SERVICE = "KillswitchService";
    public static final String TMS_SERVICE = "TmsService";
    public static final String CLOCKWORK_SERVICE_CONFIG = "clockWorkServiceConfig";
    public static final String LEGION_SERVICE_CONFIG = "legionServiceConfig";
    public static final String FOXTROT_SERVICE_CONFIG = "foxtrotServiceConfig";
    public static final String BRICKBAT_SERVICE_CONFIG = "brickbatServiceConfig";
    public static final String ATLAS_SERVICE_CONFIG = "atlasServiceConfig";
    public static final String SCOUT_SERVICE_CONFIG = "scoutServiceConfig";
    public static final String INTEL_SERVICE_CONFIG = "intelServiceConfig";
    public static final String MERCHANT_ONBOARDING_SERVICE_CONFIG = "merchantOnboardingServiceConfig";
    public static final String MERCHANT_SERVICE_CONFIG = "merchantServiceConfig";
    public static final String PARADOX_SERVICE_CONFIG = "paradoxServiceConfig";
    public static final String ODIN_SERVICE_CONFIG = "odinServiceConfig";
    public static final String FORTUNA_SERVICE_CONFIG = "fortunaServiceConfig";
    public static final String HERMOD_SERVICE_CONFIG = "hermodServiceConfig";
    public static final String KILLSWITCH_SERVICE_CONFIG = "killswitchServiceConfig";
    public static final String TMS_SERVICE_CONFIG = "tmsServiceConfig";
    public static final String GEMINI_SERVICE_CONFIG = "geminiServiceConfig";
    public static final String GEMINI_SERVICE = "GeminiService";
    public static final String ATTRIBUTE_INFO_CONFIG = "attributeInfoConfig";

    public static final String AUTHORIZATION = "Authorization";
    public static final String DEFAULT = "DEFAULT";
    public static final String TASK_TYPES = "taskTypes";
    public static final String WHITELISTED_DEFINITIONS = "whitelistedDefinitions";

    public static final String X_AUTHORIZED_FOR_ID = "X-AUTHORIZED-FOR-ID";
    public static final String UNKNOWN = "UNKNOWN";
}
