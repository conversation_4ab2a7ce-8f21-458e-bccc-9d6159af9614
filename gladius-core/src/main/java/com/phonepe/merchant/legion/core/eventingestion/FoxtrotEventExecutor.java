package com.phonepe.merchant.legion.core.eventingestion;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Preconditions;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.dataplatform.EventIngestorClient;
import com.phonepe.platform.eventingestion.model.Event;
import com.phonepe.merchant.legion.core.AppConfig;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;

@Slf4j
@Singleton
public class FoxtrotEventExecutor {

    private final EventIngestorClient eventIngestorClient;
    private final boolean eventIngestionEnabled;

    @Inject
    public FoxtrotEventExecutor(EventIngestionProvider eventProvider,
                                AppConfig config) {
        this.eventIngestorClient = eventProvider.getEventIngestorClient();
        this.eventIngestionEnabled = config.isEventIngestionEnabled();
    }

    @VisibleForTesting
    public FoxtrotEventExecutor(EventIngestionProvider eventProvider,
                                boolean eventIngestionEnabled) {
        this.eventIngestorClient = eventProvider.getEventIngestorClient();
        this.eventIngestionEnabled = eventIngestionEnabled;
    }
    public <T> void ingest(Event<T> event) {
        if (null != event) {
            log.info("ingesting event - {} ", event.getEventType());
            ingest(Collections.singletonList(event));
        }
    }

    private void ingest(List<Event> eventList) {
        try {
            if (eventIngestionEnabled) {
                Preconditions.checkNotNull(this.eventIngestorClient,
                        "Please call initialize() to setup the event ingestor client");

                if (eventList.isEmpty()) {
                    return;
                }

                eventIngestorClient.send(eventList);
            }
        } catch (Exception e) {
            log.warn("Exception while ingesting events:{}", eventList, e);
        }
    }
}