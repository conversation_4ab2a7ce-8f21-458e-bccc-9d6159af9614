package com.phonepe.merchant.legion.core.services;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.gladius.models.legion.v2.AgentProfileV2;
import com.phonepe.merchant.legion.core.config.LegionMigrationConfig;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.models.profile.response.AgentProfilesInSectorResponse;
import com.phonepe.merchant.legion.models.profile.response.AgentSectorResponse;
import io.appform.functionmetrics.MonitoredFunction;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Set;

/**
 * Adapter service that provides abstraction over Legion V1 and V2 APIs
 * Handles the migration phases by routing calls to appropriate service version
 */
@Slf4j
@Singleton
public class LegionServiceAdapter {

    private final LegionService legionServiceV1;
    private final LegionServiceV2 legionServiceV2;
    private final LegionMigrationConfig migrationConfig;

    @Inject
    public LegionServiceAdapter(LegionService legionServiceV1, 
                               LegionServiceV2 legionServiceV2,
                               LegionMigrationConfig migrationConfig) {
        this.legionServiceV1 = legionServiceV1;
        this.legionServiceV2 = legionServiceV2;
        this.migrationConfig = migrationConfig;
    }

    /**
     * Get agent profile - routes to V1 or V2 based on migration config
     */
    @MonitoredFunction
    public AgentProfile getAgentProfile(String agentId) {
        if (migrationConfig.isUseV2ForReads()) {
            try {
                AgentProfileV2 profileV2 = legionServiceV2.getAgentProfileV2(agentId);
                return convertV2ToV1Profile(profileV2);
            } catch (Exception e) {
                log.warn("Failed to get agent profile from V2 API, falling back to V1", e);
                if (migrationConfig.isFallbackToV1OnError()) {
                    return legionServiceV1.getAgentProfile(agentId);
                }
                throw e;
            }
        }
        return legionServiceV1.getAgentProfile(agentId);
    }

    /**
     * Get agent profile V2 - always uses V2 API
     */
    @MonitoredFunction
    public AgentProfileV2 getAgentProfileV2(String agentId) {
        return legionServiceV2.getAgentProfileV2(agentId);
    }

    /**
     * Get multiple agent profiles
     */
    @MonitoredFunction
    public List<AgentProfile> getAgentsMeta(Set<String> agentIds) {
        if (migrationConfig.isUseV2ForReads()) {
            try {
                List<AgentProfileV2> profilesV2 = legionServiceV2.getAgentsMetaV2(agentIds);
                return profilesV2.stream()
                        .map(this::convertV2ToV1Profile)
                        .toList();
            } catch (Exception e) {
                log.warn("Failed to get agent profiles from V2 API, falling back to V1", e);
                if (migrationConfig.isFallbackToV1OnError()) {
                    return legionServiceV1.getAgentsMeta(agentIds);
                }
                throw e;
            }
        }
        return legionServiceV1.getAgentsMeta(agentIds);
    }

    /**
     * Check sector accessibility
     */
    @MonitoredFunction
    public void isSectorAccessible(String sectorId, String agentId) {
        if (migrationConfig.isUseV2ForReads()) {
            try {
                legionServiceV2.isSectorAccessibleV2(sectorId, agentId);
                return;
            } catch (Exception e) {
                log.warn("Failed to check sector accessibility from V2 API, falling back to V1", e);
                if (migrationConfig.isFallbackToV1OnError()) {
                    legionServiceV1.isSectorAccessible(sectorId, agentId);
                    return;
                }
                throw e;
            }
        }
        legionServiceV1.isSectorAccessible(sectorId, agentId);
    }

    /**
     * Check sector accessibility (boolean return)
     */
    @MonitoredFunction
    public boolean isSectorAccessibleBoolean(String sectorId, String agentId) {
        if (migrationConfig.isUseV2ForReads()) {
            try {
                return legionServiceV2.isSectorAccessibleBooleanV2(sectorId, agentId);
            } catch (Exception e) {
                log.warn("Failed to check sector accessibility from V2 API, falling back to V1", e);
                if (migrationConfig.isFallbackToV1OnError()) {
                    return legionServiceV1.isSectorAccessibleBoolean(sectorId, agentId);
                }
                throw e;
            }
        }
        return legionServiceV1.isSectorAccessibleBoolean(sectorId, agentId);
    }

    /**
     * Get all accessible sectors for agent
     */
    @MonitoredFunction
    public List<String> getAllAccessibleSectorsOfAgent(String agentId) {
        if (migrationConfig.isUseV2ForReads()) {
            try {
                return legionServiceV2.getAllAccessibleSectorsV2(agentId);
            } catch (Exception e) {
                log.warn("Failed to get accessible sectors from V2 API, falling back to V1", e);
                if (migrationConfig.isFallbackToV1OnError()) {
                    return legionServiceV1.getAllAccessibleSectorsOfAgent(agentId);
                }
                throw e;
            }
        }
        return legionServiceV1.getAllAccessibleSectorsOfAgent(agentId);
    }

    /**
     * Get agent sectors
     */
    @MonitoredFunction
    public List<AgentSectorResponse> getAgentSectors(String agentId) {
        if (migrationConfig.isUseV2ForReads()) {
            try {
                return legionServiceV2.getAgentSectorsV2(agentId);
            } catch (Exception e) {
                log.warn("Failed to get agent sectors from V2 API, falling back to V1", e);
                if (migrationConfig.isFallbackToV1OnError()) {
                    return legionServiceV1.getAgentSectors(agentId);
                }
                throw e;
            }
        }
        return legionServiceV1.getAgentSectors(agentId);
    }

    /**
     * Get agent profiles in sector
     */
    @MonitoredFunction
    public AgentProfilesInSectorResponse getAgentProfilesInSector(List<String> sectorIds) {
        return getAgentProfilesInSector(sectorIds, null);
    }

    /**
     * Get agent profiles in sector with optional org filtering
     */
    @MonitoredFunction
    public AgentProfilesInSectorResponse getAgentProfilesInSector(List<String> sectorIds, String orgId) {
        if (migrationConfig.isUseV2ForReads()) {
            try {
                return legionServiceV2.getAgentProfilesInSectorV2(sectorIds, orgId);
            } catch (Exception e) {
                log.warn("Failed to get agent profiles in sector from V2 API, falling back to V1", e);
                if (migrationConfig.isFallbackToV1OnError()) {
                    return legionServiceV1.getAgentProfilesInSector(sectorIds);
                }
                throw e;
            }
        }
        return legionServiceV1.getAgentProfilesInSector(sectorIds);
    }

    /**
     * Convert V2 profile to V1 profile for backward compatibility
     */
    private AgentProfile convertV2ToV1Profile(AgentProfileV2 profileV2) {
        return AgentProfile.builder()
                .agentId(profileV2.getAgentId())
                .name(profileV2.getName())
                .phoneNumber(profileV2.getPhoneNumber())
                .emailId(profileV2.getEmailId())
                .agentType(profileV2.getAgentType())
                .businessUnit(profileV2.getBusinessUnit())
                .region(profileV2.getRegion())
                .status(profileV2.getStatus())
                .active(profileV2.isActive())
                .managerId(profileV2.getEffectiveManagerId()) // Use effective manager
                .lastActiveDate(profileV2.getLastActiveDate().getTime())
                .addresses(profileV2.getAddresses())
                .build();
    }
}
