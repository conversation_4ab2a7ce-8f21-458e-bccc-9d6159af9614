package com.phonepe.merchant.legion.core.eventingestion.models;

import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskCreationOnTransactionLocationEvent {

    private String entityId;

    private String campaignId;

    private String taskDefinitionId;

    private String createdBy;

    private EsLocationRequest transactionLocation;
}
