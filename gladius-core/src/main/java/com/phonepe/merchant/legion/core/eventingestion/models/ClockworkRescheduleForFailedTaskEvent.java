package com.phonepe.merchant.legion.core.eventingestion.models;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClockworkRescheduleForFailedTaskEvent {

    private String taskInstanceId;
    private Date recreationTime;
    private String clockworkJobId;
}
