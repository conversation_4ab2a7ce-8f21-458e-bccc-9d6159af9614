package com.phonepe.merchant.legion.core;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.atlas.platform.client.AtlasClientBuilder;
import com.phonepe.atlas.platform.client.atlasclient.Atlas;
import com.phonepe.merchant.legion.core.utils.AuthHeaderProviderUtil;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import io.dropwizard.lifecycle.Managed;
import lombok.Getter;

@Singleton
@Getter
public class AtlasClientProvider implements Managed {

    private Atlas atlasClient;
    private final ObjectMapper objectMapper;
    private final ServiceEndpointProviderFactory serviceEndpointProviderFactory;
    private final AppConfig appConfig;

    @Inject
    public AtlasClientProvider(ServiceEndpointProviderFactory serviceEndpointProviderFactory,
                               AppConfig config,
                               ObjectMapper objectMapper
    ) {
        this.serviceEndpointProviderFactory = serviceEndpointProviderFactory;
        this.appConfig = config;
        this.objectMapper = objectMapper;
    }

    @Override
    public void start() throws Exception {
        this.atlasClient = AtlasClientBuilder.build(appConfig.getAtlasClientConfig(),
                serviceEndpointProviderFactory,
                objectMapper,
                null,
                AuthHeaderProviderUtil::getSystemAuthHeader, null);
    }

    @Override
    public void stop() throws Exception {
        // this line is added to bypass sonar's below concern
        // Remove the "atlasClient" field and declare it as a local variable in the relevant methods.
        this.atlasClient = null;
    }
}
