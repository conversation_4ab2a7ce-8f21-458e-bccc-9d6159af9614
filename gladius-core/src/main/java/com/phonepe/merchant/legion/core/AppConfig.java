package com.phonepe.merchant.legion.core;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.hystrix.configurator.config.HystrixConfig;
import com.phonepe.atlas.platform.client.configs.AtlasClientConfig;
import com.phonepe.data.lucy.conduit.client.ConduitClientConfig;
import com.phonepe.dataplatform.EventIngestorClientConfig;
import com.phonepe.gandalf.models.client.GandalfClientConfig;
import com.phonepe.merchant.gladius.models.entitystore.ActorMessageType;
import com.phonepe.merchant.gladius.models.entitystore.SourceMeta;
import com.phonepe.merchant.gladius.models.reporting.DocstoreFolderConfig;
import com.phonepe.merchant.gladius.models.survey.ClientQcTaskConfig;
import com.phonepe.merchant.gladius.models.tasks.utils.LeadCreationConfig;
import com.phonepe.merchant.gladius.models.tasks.utils.Miscellaneous;
import com.phonepe.merchant.gladius.models.tasks.utils.UserTaskCreationConfig;
import com.phonepe.merchant.legion.client.config.LegionClientConfig;
import com.phonepe.merchant.legion.core.cache.CacheConfig;
import com.phonepe.merchant.legion.core.cache.CacheName;
import com.phonepe.merchant.legion.core.config.AttributeInfoConfig;
import com.phonepe.merchant.legion.core.config.AuditConfig;
import com.phonepe.merchant.legion.core.config.DiscoveryViewRestrictionConfig;
import com.phonepe.merchant.legion.core.entitystore.ESConfig;
import com.phonepe.merchant.legion.core.models.ChimeraLiteConfig;
import com.phonepe.merchant.legion.core.models.FormMeta;
import com.phonepe.merchant.legion.core.models.FormType;
import com.phonepe.merchant.legion.core.models.GladiusConfig;
import com.phonepe.merchant.legion.core.models.StoreCheckInConfig;
import com.phonepe.metrics.config.ReporterConfig;
import com.phonepe.models.merchants.tasks.EntityType;
import com.phonepe.olympus.im.client.config.OlympusIMClientConfig;
import com.phonepe.platform.http.v2.common.hub.RangerHubConfiguration;
import com.platform.validation.ValidationConfig;
import in.vectorpro.dropwizard.swagger.SwaggerBundleConfiguration;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.config.RMQConfig;
import io.appform.dropwizard.sharding.config.ShardedHibernateFactory;
import io.appform.ranger.discovery.bundle.ServiceDiscoveryConfiguration;
import io.dropwizard.Configuration;
import io.dropwizard.primer.model.PrimerBundleConfiguration;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Set;


@Data
@AllArgsConstructor
@NoArgsConstructor(force = true)
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AppConfig extends Configuration {

    private boolean loggingEnabled = false;

    @NotNull
    @Valid
    private ShardedHibernateFactory shards;

    @NotNull
    @Valid
    private ServiceDiscoveryConfiguration serviceDiscovery;

    @NotNull
    @Valid
    private SwaggerBundleConfiguration swagger;

    @NotNull
    @Valid
    private HystrixConfig hystrix;

    @NotNull
    @Valid
    private EventIngestorClientConfig eventIngestor;

    @NotNull
    @Valid
    private ReporterConfig reporterConfig;


    @NonNull
    @Valid
    private GandalfClientConfig gandalfConfig = new GandalfClientConfig();

    @NonNull
    @Valid
    private PrimerBundleConfiguration primerBundleConfiguration;

    @NotNull
    @Valid
    private ValidationConfig validationConfig;

    @Valid
    @NotNull
    public ESConfig esConfig;

    @NotNull
    @Valid
    private RMQConfig actorRmqConfig;

    @NotNull
    @Valid
    private Map<ActorMessageType, ActorConfig> actorConfigs;

    @NotNull
    @Valid
    private Map<EntityType, SourceMeta> entitySourceMetaMapping;

    @NotNull
    @Valid
    private Map<CacheName, CacheConfig> caches;

    @Valid
    private StoreCheckInConfig storeCheckInConfig;

    @NotNull
    @Valid
    private GladiusConfig gladiusConfig;

    @NotNull
    @Valid
    private ConduitClientConfig conduitClientConfig;

    private boolean disableWritesToEs;

    private boolean eventIngestionEnabled;

    private Map<FormType, List<FormMeta>> formConfigs;

    @NotNull
    @Valid
    private DocstoreFolderConfig docstoreFolderConfig;

    @NotNull
    @Valid
    private ClientQcTaskConfig clientQcTaskConfig;

    @NotNull
    @Valid
    private Miscellaneous miscellaneous;

    @NotNull
    @Valid
    private OlympusIMClientConfig olympusIMClientConfig;

    @NotNull
    @Valid
    private RangerHubConfiguration rangerHubConfiguration;

    @NotNull
    @Valid
    private UserTaskCreationConfig userTaskCreationConfigs;

    @NotNull
    @Valid
    private LeadCreationConfig leadCreationConfig;

    @NotNull
    @Valid
    private ChimeraLiteConfig chimeraLiteConfig;

    @NotNull
    @Valid
    private LegionClientConfig legionClientConfig;

    @NotNull
    @Valid
    private AtlasClientConfig atlasClientConfig;

    @NotNull
    private Integer commentCreationLimitPerDay;

    @Valid
    private AuditConfig auditConfig;

    @NotNull
    @Valid
    private Map<String, AttributeInfoConfig> attributeInfo;

    @NotNull
    @Valid
    private List<String> auditableTaskActionIds;

    @NotNull
    @Valid
    private DiscoveryViewRestrictionConfig discoveryViewRestrictionConfig;

    @NotNull
    @Valid
    private List<String> taskTypes;

    @NotNull
    @Valid
    private Set<String> whitelistedDefinitions;


    @NotNull
    private String esTaskIndexName;
}
