package com.phonepe.merchant.legion.core.flows;

import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventExecutor;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.flows.factories.LegionTransitionManager;
import com.phonepe.merchant.legion.core.flows.models.LegionStateMachineContext;
import com.phonepe.merchant.legion.core.flows.models.LegionTransition;
import io.github.fsm.StateMachine;
import io.github.fsm.models.entities.Context;
import io.github.fsm.models.entities.Transition;
import io.github.fsm.models.executors.ErrorAction;
import io.github.fsm.models.executors.EventAction;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.exception.ConstraintViolationException;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Slf4j
@AllArgsConstructor
public abstract class LegionStateMachineEngine<T extends LegionStateMachineContext> {

    private final StateMachine<Context> engine;
    private final FoxtrotEventExecutor eventExecutor;

    protected LegionStateMachineEngine(final StateMachine<Context> engine,
                                      final LegionTransitionManager legionTransitionManager,
                                      final FoxtrotEventExecutor eventExecutor) {
        this.engine = engine;
        this.eventExecutor = eventExecutor;
        try {
            if (engine != null) {
                this.engine.validate();
                this.bind(legionTransitionManager);
            }
        } catch (ConstraintViolationException exc){
            throw LegionException.propagate(CoreErrorCode.SQL_CONSTRAINT_EXCEPTION, exc);
        } catch (Exception e) {
            throw LegionException.propagate(CoreErrorCode.INTERNAL_ERROR, e);
        }
    }

    private void bind(LegionTransitionManager legionTransitionManager) {
        try {
            this.engine.anyTransition((EventAction<T>) baseStateMachineContext -> {
                log.info("Processing Legion transition for event {} moving from {} to {}",
                        baseStateMachineContext.getCausedEvent(),
                        baseStateMachineContext.getFrom(),
                        baseStateMachineContext.getTo());

                Optional<LegionTransition> processor =
                        legionTransitionManager.getProcessor(baseStateMachineContext);

                if (!processor.isPresent()) {
                    log.info("No Legion transition for event {} moving from {} to {} found. Gracefully ignoring",
                            baseStateMachineContext.getCausedEvent(), baseStateMachineContext.getFrom(),
                            baseStateMachineContext.getTo());
                    return;
                }
                processor.get().execute(baseStateMachineContext);

                log.info("Processed Legion transition for event {} moving from {} to {}",
                        baseStateMachineContext.getCausedEvent(), baseStateMachineContext.getFrom(),
                        baseStateMachineContext.getTo());
            });
            this.engine.addError((ErrorAction<T>) (e, context) -> {
                LegionFlowType legionFlowType = context.getType();
                String taskId = context.getTaskInstanceId();
                log.error(String.format("Error while executing state machine: %s transactionId: %s", legionFlowType, taskId), e.getMessage());
                if (e.getCause() instanceof LegionException cause) {
                    throw cause;
                }
                throw LegionException.propagate(CoreErrorCode.INTERNAL_ERROR, e);
            });
        } catch (LegionException e) {
            throw e;
        } catch (Exception e) {
            throw LegionException.propagate(CoreErrorCode.INTERNAL_ERROR, e);
        }
    }

    protected void fireGrace(LegionStateMachineContext context) {
        if (engine == null) {
            return;
        }
        try {
            final Optional<Transition> transition = engine
                    .getTransition(context.getFrom(), context.getCausedEvent());

            if (transition.isPresent()) {
                engine.fire(context.getCausedEvent(), context);
                eventExecutor.ingest(StateMachineFoxtrotEventUtil.toStateMachineTransitionFoxtrotEvent(context));
            }
        } catch (LegionException e) {
            throw e;
        } catch (Exception e) {
            throw LegionException.propagate(CoreErrorCode.INTERNAL_ERROR, e);
        }
    }

    protected void fire(LegionStateMachineContext context) {
        if (engine == null) {
            return;
        }
        try {
            final Optional<Transition> transition = engine
                    .getTransition(context.getFrom(), context.getCausedEvent());

            if (!transition.isPresent()) {
                Map<String, Object> error = new HashMap<>();
                error.put("from", context.getFrom());
                error.put("to", context.getTo());
                error.put("event", context.getCausedEvent());
                log.error("transition not found for taskInstance: {} with error {}", context.getTaskInstanceId(), error);
                throw LegionException.error(CoreErrorCode.TRANSITION_NOT_FOUND, error);
            }
            engine.fire(context.getCausedEvent(), context);
            eventExecutor.ingest(StateMachineFoxtrotEventUtil.toStateMachineTransitionFoxtrotEvent(context));
            if(context.getFrom() != context.getTo()){
                eventExecutor.ingest(StateMachineFoxtrotEventUtil.toTaskStateTransitionFoxtrotEvent(context));
            }
        } catch (LegionException e) {
            throw e;
        } catch (Exception e) {
            throw LegionException.propagate(CoreErrorCode.INTERNAL_ERROR, e);
        }
    }
}
