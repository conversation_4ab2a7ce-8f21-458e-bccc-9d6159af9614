package com.phonepe.merchant.legion.core.eventingestion.models.usecasespecificevents;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SuccessfulExternalEntityOnboardingEvent {

    private String taskInstanceId;
    private String merchantId;
    private String storeId;
    private String actionId;
    private String campaignId;
    private String entityId;
    private String onboardedBy;

}
