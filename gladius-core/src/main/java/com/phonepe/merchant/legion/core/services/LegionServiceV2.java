package com.phonepe.merchant.legion.core.services;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.gladius.models.legion.v2.AgentProfileV2;
import com.phonepe.merchant.gladius.models.legion.v2.OrgProfile;
import com.phonepe.merchant.legion.core.annotations.LegionServiceConfig;
import com.phonepe.merchant.legion.models.profile.response.AgentProfilesInSectorResponse;
import com.phonepe.merchant.legion.models.profile.response.AgentSectorResponse;
import com.phonepe.merchant.legion.models.profile.response.UserSectorResponse;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import io.appform.functionmetrics.MonitoredFunction;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;

import javax.ws.rs.core.UriBuilder;
import java.net.URI;
import java.util.List;
import java.util.Set;

import static okhttp3.RequestBody.create;
import static com.phonepe.platform.http.v2.common.HttpMethod.GET;
import static com.phonepe.platform.http.v2.common.HttpMethod.POST;

/**
 * Legion V2 Service for org-level API interactions
 * Handles the new org-aware APIs from Legion revamp
 */
@Slf4j
@Singleton
public class LegionServiceV2 extends BaseHttpService {

    @Inject
    public LegionServiceV2(@LegionServiceConfig HttpConfiguration httpConfiguration,
                          ServiceEndpointProviderFactory serviceEndpointProviderFactory) {
        super(httpConfiguration, serviceEndpointProviderFactory);
    }

    /**
     * Get agent profile with org-level information using V2 API
     */
    @MonitoredFunction
    public AgentProfileV2 getAgentProfileV2(String agentId) {
        Response response = call("getAgentProfileV2",
                "/v2/profile/" + agentId,
                null,
                GET,
                getSystemAuthHeader());

        GenericResponse<AgentProfileV2> agentProfileGenericResponse = readValue(response, 
                new TypeReference<GenericResponse<AgentProfileV2>>() {});
        return agentProfileGenericResponse.getData();
    }

    /**
     * Get multiple agent profiles with org-level information
     */
    @MonitoredFunction
    public List<AgentProfileV2> getAgentsMetaV2(Set<String> agentIds) {
        UriBuilder uriBuilder = UriBuilder.fromPath("/v2/profile/agents/meta");
        agentIds.forEach(agentId -> uriBuilder.queryParam("agentIds", agentId));

        Response response = call("getAgentsMetaV2",
                uriBuilder.build().toString(),
                null,
                GET,
                getSystemAuthHeader());

        GenericResponse<List<AgentProfileV2>> agentProfileGenericResponse = readValue(response, 
                new TypeReference<GenericResponse<List<AgentProfileV2>>>() {});
        return agentProfileGenericResponse.getData();
    }

    /**
     * Get organization profile by org ID
     */
    @MonitoredFunction
    public OrgProfile getOrgProfile(String orgId) {
        Response response = call("getOrgProfile",
                "/v2/org/" + orgId,
                null,
                GET,
                getSystemAuthHeader());

        GenericResponse<OrgProfile> orgProfileGenericResponse = readValue(response, 
                new TypeReference<GenericResponse<OrgProfile>>() {});
        return orgProfileGenericResponse.getData();
    }

    /**
     * Check if sector is accessible to agent using org-level validation
     */
    @MonitoredFunction
    public void isSectorAccessibleV2(String sectorId, String agentId) {
        call("isSectorAccessibleV2", 
             String.format("/v2/agent/isAccessible/%s/%s", sectorId, agentId),
             null, GET, getSystemAuthHeader());
    }

    /**
     * Check if sector is accessible to agent (boolean return)
     */
    @MonitoredFunction
    public boolean isSectorAccessibleBooleanV2(String sectorId, String agentId) {
        return callWithoutCheck("isSectorAccessibleBooleanV2", 
                String.format("/v2/agent/isAccessible/%s/%s", sectorId, agentId),
                null, GET, getSystemAuthHeader()).isSuccessful();
    }

    /**
     * Get all accessible sectors for an agent with org-level context
     */
    @MonitoredFunction
    public List<String> getAllAccessibleSectorsV2(String agentId) {
        UriBuilder uriBuild = UriBuilder.fromPath(String.format("/v2/agent/accessible/sectors/%s", agentId));
        URI uri = uriBuild.build();
        Response response = call("getAllAccessibleSectorsV2", uri.toString(),
                null, GET, getSystemAuthHeader());
        GenericResponse<UserSectorResponse> userSectorResponseGenericResponse = readValue(response, 
                new TypeReference<GenericResponse<UserSectorResponse>>() {});
        return userSectorResponseGenericResponse.getData().getSectors().stream()
                .map(sector -> sector.getSectorId()).toList();
    }

    /**
     * Get agent sectors with org-level information
     */
    @MonitoredFunction
    public List<AgentSectorResponse> getAgentSectorsV2(String agentId) {
        Response response = Call("getAgentSectorsV2", "/v2/profile/" + agentId + "/sectors", null,
                GET, null);
        GenericResponse<List<AgentSectorResponse>> storeProfileSectors =
                readValue(response, new TypeReference<GenericResponse<List<AgentSectorResponse>>>() {});
        return storeProfileSectors.getData();
    }

    /**
     * Get agent profiles in sector with org-level filtering
     */
    @MonitoredFunction
    public AgentProfilesInSectorResponse getAgentProfilesInSectorV2(List<String> sectorIds, String orgId) {
        UriBuilder uriBuilder = UriBuilder.fromPath("/v2/profile/agentProfilesInSector");
        if (orgId != null) {
            uriBuilder.queryParam("orgId", orgId);
        }
        
        // Create request body with sector IDs
        String requestBody = "{\"sectorIds\":" + sectorIds.toString() + "}";
        Response response = call("getAgentProfilesInSectorV2", uriBuilder.toString(), 
                create(requestBody, okhttp3.MediaType.parse("application/json")), POST, getSystemAuthHeader());
        
        return readValue(response, new TypeReference<GenericResponse<AgentProfilesInSectorResponse>>() {})
                .getData();
    }
}
