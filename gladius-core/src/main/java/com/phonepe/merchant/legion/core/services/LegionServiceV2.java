package com.phonepe.merchant.legion.core.services;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.gladius.models.legion.v2.AgentProfileV2;
import com.phonepe.merchant.gladius.models.legion.v2.OrgProfile;
import com.phonepe.merchant.legion.core.annotations.LegionServiceConfig;
import com.phonepe.merchant.legion.models.profile.response.AgentProfilesInSectorResponse;
import com.phonepe.merchant.legion.models.profile.response.AgentSectorResponse;
import com.phonepe.merchant.legion.models.profile.response.UserSectorResponse;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import io.appform.functionmetrics.MonitoredFunction;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;

import javax.ws.rs.core.UriBuilder;
import java.net.URI;
import java.util.List;
import java.util.Set;

import static okhttp3.RequestBody.create;
import static com.phonepe.platform.http.v2.common.HttpMethod.GET;
import static com.phonepe.platform.http.v2.common.HttpMethod.POST;

/**
 * Legion Enhanced Service for org-level API interactions
 * Handles the same Legion APIs but with enhanced org-level fields in responses
 * Note: Legion didn't create new V2 APIs, just added org fields to existing APIs
 */
@Slf4j
@Singleton
public class LegionServiceV2 extends BaseHttpService {

    @Inject
    public LegionServiceV2(@LegionServiceConfig HttpConfiguration httpConfiguration,
                          ServiceEndpointProviderFactory serviceEndpointProviderFactory) {
        super(httpConfiguration, serviceEndpointProviderFactory);
    }

    /**
     * Get agent profile with org-level information using enhanced API response
     * Same endpoint as V1 but response now includes org fields
     */
    @MonitoredFunction
    public AgentProfileV2 getAgentProfileV2(String agentId) {
        Response response = call("getAgentProfileV2",
                "/v1/profile/" + agentId,  // Same endpoint, enhanced response
                null,
                GET,
                getSystemAuthHeader());

        GenericResponse<AgentProfileV2> agentProfileGenericResponse = readValue(response,
                new TypeReference<GenericResponse<AgentProfileV2>>() {});
        return agentProfileGenericResponse.getData();
    }

    /**
     * Get multiple agent profiles with org-level information
     * Same endpoint as V1 but response now includes org fields
     */
    @MonitoredFunction
    public List<AgentProfileV2> getAgentsMetaV2(Set<String> agentIds) {
        UriBuilder uriBuilder = UriBuilder.fromPath("/v1/profile/agents/meta");  // Same endpoint
        agentIds.forEach(agentId -> uriBuilder.queryParam("agentIds", agentId));

        Response response = call("getAgentsMetaV2",
                uriBuilder.build().toString(),
                null,
                GET,
                getSystemAuthHeader());

        GenericResponse<List<AgentProfileV2>> agentProfileGenericResponse = readValue(response,
                new TypeReference<GenericResponse<List<AgentProfileV2>>>() {});
        return agentProfileGenericResponse.getData();
    }

    /**
     * Get organization profile by org ID
     * This might be a new endpoint for org-specific information
     */
    @MonitoredFunction
    public OrgProfile getOrgProfile(String orgId) {
        Response response = call("getOrgProfile",
                "/v1/org/" + orgId,  // Assuming this endpoint exists for org info
                null,
                GET,
                getSystemAuthHeader());

        GenericResponse<OrgProfile> orgProfileGenericResponse = readValue(response,
                new TypeReference<GenericResponse<OrgProfile>>() {});
        return orgProfileGenericResponse.getData();
    }

    /**
     * Check if sector is accessible to agent using enhanced validation
     * Same endpoint as V1 but may include org-level validation logic
     */
    @MonitoredFunction
    public void isSectorAccessibleV2(String sectorId, String agentId) {
        call("isSectorAccessibleV2",
             String.format("/v1/agent/isAccessible/%s/%s", sectorId, agentId),  // Same endpoint
             null, GET, getSystemAuthHeader());
    }

    /**
     * Check if sector is accessible to agent (boolean return)
     * Same endpoint as V1 but may include org-level validation logic
     */
    @MonitoredFunction
    public boolean isSectorAccessibleBooleanV2(String sectorId, String agentId) {
        return callWithoutCheck("isSectorAccessibleBooleanV2",
                String.format("/v1/agent/isAccessible/%s/%s", sectorId, agentId),  // Same endpoint
                null, GET, getSystemAuthHeader()).isSuccessful();
    }

    /**
     * Get all accessible sectors for an agent with org-level context
     * Same endpoint as V1 but response may include org-level sector information
     */
    @MonitoredFunction
    public List<String> getAllAccessibleSectorsV2(String agentId) {
        UriBuilder uriBuild = UriBuilder.fromPath(String.format("/v1/agent/accessible/sectors/%s", agentId));  // Same endpoint
        URI uri = uriBuild.build();
        Response response = call("getAllAccessibleSectorsV2", uri.toString(),
                null, GET, getSystemAuthHeader());
        GenericResponse<UserSectorResponse> userSectorResponseGenericResponse = readValue(response,
                new TypeReference<GenericResponse<UserSectorResponse>>() {});
        return userSectorResponseGenericResponse.getData().getSectors().stream()
                .map(sector -> sector.getSectorId()).toList();
    }

    /**
     * Get agent sectors with org-level information
     * Same endpoint as V1 but response may include org-level sector information
     */
    @MonitoredFunction
    public List<AgentSectorResponse> getAgentSectorsV2(String agentId) {
        Response response = call("getAgentSectorsV2", "/v1/profile/" + agentId + "/sectors/v2", null,  // Same endpoint
                GET, null);
        GenericResponse<List<AgentSectorResponse>> storeProfileSectors =
                readValue(response, new TypeReference<GenericResponse<List<AgentSectorResponse>>>() {});
        return storeProfileSectors.getData();
    }

    /**
     * Get agent profiles in sector with org-level filtering
     * Same endpoint as V1 but may support org-level filtering via query params
     */
    @MonitoredFunction
    public AgentProfilesInSectorResponse getAgentProfilesInSectorV2(List<String> sectorIds, String orgId) {
        UriBuilder uriBuilder = UriBuilder.fromPath("/v1/profile/agentProfilesInSectorFromCache");  // Same endpoint
        if (orgId != null) {
            uriBuilder.queryParam("orgId", orgId);  // Add org filtering if supported
        }

        // Create request body with sector IDs (same format as V1)
        String requestBody = "{\"sectorIds\":" + sectorIds.toString() + "}";
        Response response = call("getAgentProfilesInSectorV2", uriBuilder.toString(),
                create(requestBody, okhttp3.MediaType.parse("application/json")), POST, getSystemAuthHeader());

        return readValue(response, new TypeReference<GenericResponse<AgentProfilesInSectorResponse>>() {})
                .getData();
    }
}
