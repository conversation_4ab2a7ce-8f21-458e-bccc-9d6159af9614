package com.phonepe.merchant.legion.core.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Configuration class for Legion V1 to V2 migration
 * Controls the migration phases and feature toggles
 */
@Data
@NoArgsConstructor
public class LegionMigrationConfig {
    
    /**
     * Enable dual reads from V2 API
     * Phase 3: Dual Reads enabled
     */
    @JsonProperty("useV2ForReads")
    private boolean useV2ForReads = false;
    
    /**
     * Enable dual writes to V2 API
     * Phase 1: Dual Writes
     */
    @JsonProperty("useV2ForWrites")
    private boolean useV2ForWrites = false;
    
    /**
     * Fallback to V1 API on V2 errors
     * Safety mechanism during migration
     */
    @JsonProperty("fallbackToV1OnError")
    private boolean fallbackToV1OnError = true;
    
    /**
     * Enable org-level validation and business logic
     * Phase 4a: Org migration of users
     */
    @JsonProperty("enableOrgLevelLogic")
    private boolean enableOrgLevelLogic = false;
    
    /**
     * Enable V2 API endpoints
     * Phase 5: Writes migration to V2 contract
     */
    @JsonProperty("enableV2Endpoints")
    private boolean enableV2Endpoints = false;
    
    /**
     * Percentage of traffic to route to V2 APIs (0-100)
     * For gradual rollout
     */
    @JsonProperty("v2TrafficPercentage")
    private int v2TrafficPercentage = 0;
    
    /**
     * List of agent IDs to always use V2 APIs for (testing)
     */
    @JsonProperty("v2TestAgentIds")
    private java.util.Set<String> v2TestAgentIds = java.util.Set.of();
    
    /**
     * List of org IDs that have been migrated to V2
     */
    @JsonProperty("migratedOrgIds")
    private java.util.Set<String> migratedOrgIds = java.util.Set.of();
    
    /**
     * Enable org-level role validation
     */
    @JsonProperty("enableOrgRoleValidation")
    private boolean enableOrgRoleValidation = false;
    
    /**
     * Enable org-level sector validation
     */
    @JsonProperty("enableOrgSectorValidation")
    private boolean enableOrgSectorValidation = false;
    
    /**
     * Enable org-level hierarchy validation
     */
    @JsonProperty("enableOrgHierarchyValidation")
    private boolean enableOrgHierarchyValidation = false;
    
    /**
     * Check if agent should use V2 APIs based on configuration
     */
    public boolean shouldUseV2ForAgent(String agentId) {
        if (v2TestAgentIds.contains(agentId)) {
            return true;
        }
        
        if (v2TrafficPercentage == 0) {
            return false;
        }
        
        if (v2TrafficPercentage >= 100) {
            return true;
        }
        
        // Use hash-based routing for consistent behavior
        int hash = Math.abs(agentId.hashCode() % 100);
        return hash < v2TrafficPercentage;
    }
    
    /**
     * Check if org has been migrated to V2
     */
    public boolean isOrgMigrated(String orgId) {
        return migratedOrgIds.contains(orgId);
    }
    
    /**
     * Get current migration phase based on configuration
     */
    public MigrationPhase getCurrentPhase() {
        if (enableV2Endpoints) {
            return MigrationPhase.WRITES_MIGRATION_TO_V2;
        } else if (enableOrgLevelLogic) {
            return MigrationPhase.ORG_MIGRATION_OF_USERS;
        } else if (useV2ForReads) {
            return MigrationPhase.DUAL_READS;
        } else if (useV2ForWrites) {
            return MigrationPhase.DUAL_WRITES;
        } else {
            return MigrationPhase.V1_ONLY;
        }
    }
    
    public enum MigrationPhase {
        V1_ONLY,
        DUAL_WRITES,
        DUAL_READS,
        ORG_MIGRATION_OF_USERS,
        WRITES_MIGRATION_TO_V2
    }
}
