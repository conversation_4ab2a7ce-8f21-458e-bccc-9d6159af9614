package com.phonepe.merchant.legion.core.services;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.google.inject.name.Named;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.utils.HttpUtils;
import com.phonepe.merchants.odin.models.enums.Granularity;
import com.phonepe.merchants.odin.models.merchant.CompleteTransactionMetaDataRequest;
import com.phonepe.merchants.odin.models.merchant.CompleteTransactionMetaDataResponse;
import com.phonepe.merchants.odin.models.profileStore.AttributeReadRequest;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import com.phonepe.profile.ProfileType;
import com.phonepe.profile.responses.AttributeReadResponse;
import io.appform.ranger.discovery.bundle.ServiceDiscoveryConfiguration;
import okhttp3.Response;

import java.util.Set;

import static com.phonepe.merchant.legion.core.services.CommunicationService.CallTypes.POST;
import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.ODIN_SERVICE_CONFIG;

@Singleton
public class OdinService extends CommunicationService {

    @Inject
    public OdinService(@Named(value = ODIN_SERVICE_CONFIG) final HttpConfiguration httpConfiguration,
                       final ServiceEndpointProviderFactory serviceEndpointProviderFactory,
                       ObjectMapper mapper,
                       MetricRegistry metricRegistry,
                       FoxtrotEventIngestionService eventIngestionService,
                       ServiceDiscoveryConfiguration serviceDiscoveryConfiguration) {
        super(httpConfiguration,
                HttpUtils.makeOkHttpClient(httpConfiguration, metricRegistry),
                () -> serviceEndpointProviderFactory.provider(httpConfiguration.getClientId()),
                mapper,
                eventIngestionService,
                serviceDiscoveryConfiguration);
    }

    public CompleteTransactionMetaDataResponse getMerchantTransactionData(String merchantId) {
        CompleteTransactionMetaDataRequest request = CompleteTransactionMetaDataRequest.builder()
                .merchantId(merchantId)
                .granularity(Granularity.MONTHLY)
                .build();
        Response response = call("getMerchantTransactionData", "/v1/merchant/complete/transaction/metadata", request,
                POST, getSystemAuthHeader());

        return readValue(response, new TypeReference<CompleteTransactionMetaDataResponse>() {
        });
    }

    public AttributeReadResponse readProfileStoreAttribute(String key, Set<String> psKeyValue, ProfileType profileType,
                                                           String nameSpace) {
        AttributeReadRequest request = AttributeReadRequest.builder()
                .key(key)
                .psKeyValue(psKeyValue)
                .profileType(profileType)
                .nameSpace(nameSpace)
                .build();
        Response response = call("readProfileStoreAttribute", "/profileStore/ps/read", request,
                POST, getSystemAuthHeader());
        return readValue(response, new TypeReference<GenericResponse<AttributeReadResponse>>() {
        }).getData();
    }
}
