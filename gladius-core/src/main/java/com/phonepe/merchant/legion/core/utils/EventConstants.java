package com.phonepe.merchant.legion.core.utils;


public class EventConstants {

    private EventConstants() {
    }

    public enum TaskEvents {
        SCHEDULE_TASK_VERIFICATION,
        TASK_COMPLETION,
        TASK_VERIFICATION,
        TASK_VERIFICATION_STATUS,
        TASK_DEFINITION_CREATION,
        TASK_UPDATE,
        TASK_UPDATE_V2,
        C<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_RESCHEDULE,
        TASK_COMPLETION_LOCATION,
        <PERSON><PERSON><PERSON>IGN_CREATION,
        ATTRIBUTE_CREATION,
        ACCESS_RESOLVER_ERROR,
        TASK_ACCESS_RESOLVER_ERROR,
        BULK_SECTOR_UPDATE_FAILED,
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_RESCHEDULE_FOR_FAILED_TASK,
        FAILED_TASK_RECREATED,
        TASK_PREREQUISITE_FAILED,
        BULK_SECTOR_FETCH_ERROR,
        CLIENT_TASK_CREATED,
        CLIENT_TASK_REQUESTED,
        CLIENT_TASK_QUEUE_PUSH_SUCCESS,
        CLIENT_TASK_QUEUE_PUSH_FAILED,
        CLIENT_TASK_CREATION_FAILED,
        CLIENT_TASK_DELETED,
        CLIENT_TASK_VERIFIED,
        CLIENT_TASK_VERIFY_EVENT_RECEIVED,
        CLIENT_TASK_DELETION_FAILED,
        CLIENT_TASK_DELETE_EVENT_RECEIVED,
        CLIENT_TASK_VERIFICATION_FAILED,
        TASK_RESCHEDULED,
        TASK_META_UPDATE,
        FORCE_TASK_COMPLETION,
        USER_LEAD_TASK_CREATION_SUCCESS,
        LEAD_GENERATION_REQUEST_RECEIVED,
        COMMENT_CREATION_SUCCESS,
        AGENT_TASK_ELIGIBILITY_EVENT,
        SELF_ASSIGNMENT_LIMIT_BREACHED_EVENT
    }

    public enum ReportingEvents {
        CONDUIT_JOB_SUBMISSION_FAILURE,
        CONDUIT_CALLBACK_RECEIVED,
        CONDUIT_JOB_SUBMIT
    }

    public enum SurveyEvents {
        SURVEY_QC_TASK_CREATION_FAILED,
        QC_TASK_CREATION_SUCCESS,
        EVENT_BASED_TASK_CREATION_SUCCESS,
        EVENT_BASED_TASK_CREATION_FAILURE
    }

    public enum CoreEvents {
        DOWNSTREAM_SERVICE_FAILURE,
        DAO_ERROR
    }

    public enum UseCaseSpecificEvents {
        SUCCESSFUL_STORE_ONBOARDING
    }

    public enum TagEvents{
        SAVE_TAG,
        TOGGLE_TAG,
    }

    public enum DiscoveryEvents {
        POST_FILTER_SORTING_DIFFERENCE_EVENT,
        PRIORITY_SORTING_EVENT,
        PRIORITY_TASK_COUNT_EVENT
    }

    public enum HotspotEvents {
        HOTSPOT_ACTIVE,
        HOTSPOT_DEACTIVATED,
        HOTSPOT_SYNC_FAILED,
        HOTSPOT_CONFIG_ACTIVE,
        HOTSPOT_CONFIG_DEACTIVATED,
        HOTSPOT_CONFIG_CREATION_FAILED
    }

    public enum FormEvents {
        FEEDBACK_CREATION_OR_UPDATION_EVENT,
        FORM_CONFIG_CREATION_OR_UPDATION_EVENT,
        FEEDBACK_CREATION_OR_UPDATION_FAILURE_EVENT,
        FORM_CONFIG_CREATION_OR_UPDATION_FAILURE_EVENT,
        GET_BRICKBAT_FORM_AND_FEEDBACK_FAILURE_EVENT,
        GET_FLAT_MERCHANT_STORE_FEEDBACK_FAILURE_EVENT,
        CREATE_FEEDBACK_AND_AUDIT_FAILURE_EVENT
    }

    // EVENT CONSTANT
    public static final String EVENT_APP_NAME = "gladius";
    public static final String EVENT_SCHEMA_VERSION = "v1";

    //DB and ES Event related constants
    public static final String DB = "DB";
    public static final String ES = "ES";
    public static final String GET = "get";
    public static final String SAVE = "save";
    public static final String UPDATE = "update";
    public static final String SELECT = "select";
    public static final String AUDIT_SELECT = "auditSelect";
    public static final String SCATTER_GATHER = "scatterGather";
    public static final String FIND = "find";
    public static final String SEARCH = "search";
    public static final String SEARCH_WITH_GEOSORTING = "searchWithGeoSorting";
    public static final String SEARCH_WITH_PRIORITY_SORTING = "searchWithPrioritySorting";

    public static final String SEARCH_WITH_SORTING = "searchWithSorting";
    public static final String AGGREGATIONS = "aggregation";
    public static final String SCROLL_INIT = "scrollSearchInitiation";
    public static final String SCROLL_SEARCH = "scrollSearchResponse";

}
