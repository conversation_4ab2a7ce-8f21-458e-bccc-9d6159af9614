package com.phonepe.merchant.legion.core.eventingestion.models.taskevents;

import com.phonepe.merchant.gladius.models.tasks.request.TaskManualVerificationRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClientTaskVerificationFailedEvent {

    private TaskManualVerificationRequest taskManualVerificationRequest;
    private String errorMessage;
}
