package com.phonepe.merchant.legion.core.eventingestion.models.taskevents;

import com.phonepe.models.merchants.tasks.EventBasedTaskCreationClient;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EventBasedTaskCreationSuccess {

    String taskInstanceId;

    String campaignId;

    String actionId;

    String taskDefinitionId;

    String entityId;

    String actor;

    Boolean markAvailable;

    EventBasedTaskCreationClient client;

}
