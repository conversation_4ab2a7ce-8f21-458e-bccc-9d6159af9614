package com.phonepe.merchant.legion.core.eventingestion;

import com.google.inject.Inject;
import com.phonepe.merchant.gladius.models.hotspots.requests.HotspotConfigCreationRequest;
import com.phonepe.merchant.gladius.models.hotspots.requests.HotspotSyncRequest;
import com.phonepe.merchant.gladius.models.hotspots.storage.StoredHotspot;
import com.phonepe.merchant.gladius.models.hotspots.storage.StoredHotspotConfig;
import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskAttributeCreateRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskSchedulingPayload;
import com.phonepe.merchant.gladius.models.tasks.request.commands.LocationCommandRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCompleteRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.UserTaskCreationRequest;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.validation.ValidatorResponse;
import com.phonepe.merchant.legion.core.AppConfig;
import com.phonepe.merchant.legion.core.eventingestion.models.ClockworkRescheduleForFailedTaskEvent;
import com.phonepe.merchant.legion.core.eventingestion.models.ClockworkRetryEvent;
import com.phonepe.merchant.legion.core.eventingestion.models.DaoErrorEvent;
import com.phonepe.merchant.legion.core.eventingestion.models.DownstreamServiceFailureEvent;
import com.phonepe.merchant.legion.core.eventingestion.models.PostFilterSortingStrategyDifferenceEvent;
import com.phonepe.merchant.legion.core.eventingestion.models.PrioritySortingEvent;
import com.phonepe.merchant.legion.core.eventingestion.models.PriorityTaskCountEvent;
import com.phonepe.merchant.legion.core.eventingestion.models.SelfAssignmentLimitBreachedEvent;
import com.phonepe.merchant.legion.core.eventingestion.models.TaskCompletionDistanceMetricsEvent;
import com.phonepe.merchant.legion.core.eventingestion.models.TaskPrerequisiteFailedEvent;
import com.phonepe.merchant.legion.core.eventingestion.models.conduitevents.ConduitJobSubmitEvent;
import com.phonepe.merchant.legion.core.eventingestion.models.leadgen.UserLeadTaskCreationSuccessEvent;
import com.phonepe.merchant.legion.core.eventingestion.models.usecasespecificevents.SuccessfulExternalEntityOnboardingEvent;
import com.phonepe.merchant.legion.core.utils.EventConstants;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import static com.phonepe.merchant.legion.core.utils.CommonUtils.isNullOrEmpty;
import static com.phonepe.merchant.legion.core.utils.CoreFoxtrotEventUtils.getEventObject;
import static com.phonepe.merchant.legion.core.utils.EventConstants.CoreEvents.DAO_ERROR;
import static com.phonepe.merchant.legion.core.utils.EventConstants.CoreEvents.DOWNSTREAM_SERVICE_FAILURE;
import static com.phonepe.merchant.legion.core.utils.EventConstants.ReportingEvents.CONDUIT_JOB_SUBMIT;
import static com.phonepe.merchant.legion.core.utils.EventConstants.TaskEvents.ACCESS_RESOLVER_ERROR;
import static com.phonepe.merchant.legion.core.utils.EventConstants.TaskEvents.ATTRIBUTE_CREATION;
import static com.phonepe.merchant.legion.core.utils.EventConstants.TaskEvents.CLOCKWORK_RESCHEDULE_FOR_FAILED_TASK;
import static com.phonepe.merchant.legion.core.utils.EventConstants.TaskEvents.LEAD_GENERATION_REQUEST_RECEIVED;
import static com.phonepe.merchant.legion.core.utils.EventConstants.TaskEvents.SELF_ASSIGNMENT_LIMIT_BREACHED_EVENT;
import static com.phonepe.merchant.legion.core.utils.EventConstants.TaskEvents.TASK_COMPLETION_LOCATION;
import static com.phonepe.merchant.legion.core.utils.EventConstants.TaskEvents.TASK_PREREQUISITE_FAILED;
import static com.phonepe.merchant.legion.core.utils.EventConstants.TaskEvents.TASK_RESCHEDULED;
import static com.phonepe.merchant.legion.core.utils.EventConstants.TaskEvents.USER_LEAD_TASK_CREATION_SUCCESS;
import static com.phonepe.merchant.legion.core.utils.EventConstants.UseCaseSpecificEvents.SUCCESSFUL_STORE_ONBOARDING;

public class FoxtrotEventIngestionService extends FoxtrotEventExecutor {

    @Inject
    public FoxtrotEventIngestionService(EventIngestionProvider eventProvider, AppConfig config) {
        super(eventProvider, config);
    }

    public void ingestDaoErrorEvent(DaoErrorEvent event) {
        ingest(getEventObject(event,event.getCommandName(),DAO_ERROR));
    }

    public void ingestDownstreamServiceFailureEvent(DownstreamServiceFailureEvent event) {
        ingest(getEventObject(event, event.getServiceName(), DOWNSTREAM_SERVICE_FAILURE));
    }


    public void ingestClockworkRetryEvent(TaskCompleteRequest taskCompleteRequest, String clockworkJobId, Date nextScheduledTime){
        ClockworkRetryEvent clockworkRetryEvent =  ClockworkRetryEvent.builder()
                .taskCompleteRequest(taskCompleteRequest)
                .clockworkId(clockworkJobId)
                .nextScheduledTime(nextScheduledTime)
                .build();
        ingest(getEventObject(clockworkRetryEvent, taskCompleteRequest.getTaskInstanceId(), EventConstants.TaskEvents.CLOCKWORK_RESCHEDULE));


    }

    public void ingestPostFilterSortingStrategyDifferenceEvent(PostFilterSortingStrategyDifferenceEvent postFilterSortingStrategyDifferenceEvent) {
        ingest(getEventObject(postFilterSortingStrategyDifferenceEvent, UUID.randomUUID().toString(), EventConstants.DiscoveryEvents.POST_FILTER_SORTING_DIFFERENCE_EVENT));
    }

    public void ingestPrioritySortingEvent(PrioritySortingEvent prioritySortingEvent) {
        ingest(getEventObject(prioritySortingEvent, UUID.randomUUID().toString(), EventConstants.DiscoveryEvents.PRIORITY_SORTING_EVENT));
    }

    public void ingestPriorityCountEvent(PriorityTaskCountEvent priorityTaskCountEvent) {
        ingest(getEventObject(priorityTaskCountEvent, UUID.randomUUID().toString(), EventConstants.DiscoveryEvents.PRIORITY_TASK_COUNT_EVENT));
    }

    public void ingestConduitSubmitEvent(ConduitJobSubmitEvent event) {
        ingest(getEventObject(event, event.getJobResponse().getRequestId(), CONDUIT_JOB_SUBMIT));
    }

    public void ingestTaskCompletionLocationDifference(EsLocationRequest agentLocation, EsLocationRequest taskLocation, double distance,
                                                       String taskInstanceId){
        TaskCompletionDistanceMetricsEvent taskCompletionDistanceMetricsEvent = TaskCompletionDistanceMetricsEvent.builder()
                .agentLocation(agentLocation)
                .taskLocation(taskLocation)
                .distance(distance).build();
        ingest(getEventObject(taskCompletionDistanceMetricsEvent, taskInstanceId, TASK_COMPLETION_LOCATION));
    }

    public void ingestAttributeCreationEvent(TaskAttributeCreateRequest taskAttributeCreateRequest) {
        ingest(getEventObject(taskAttributeCreateRequest, taskAttributeCreateRequest.getAttributeValue(), ATTRIBUTE_CREATION));
    }

    public void ingestAccessResolverError(Exception e){
        ingest(getEventObject(e.toString(), UUID.randomUUID().toString(), ACCESS_RESOLVER_ERROR));
    }

    public void ingestClockworkRescheduleForFailedTask(ClockworkRescheduleForFailedTaskEvent event) {
        ingest(getEventObject(event, event.getTaskInstanceId(), CLOCKWORK_RESCHEDULE_FOR_FAILED_TASK));
    }

    public void ingestTaskPrerequisiteFailedEvent(LocationCommandRequest request, Set<ValidatorResponse> errorContext) {
        Map<String,Boolean> errorMap;
        if (isNullOrEmpty(errorContext)) {
            errorMap = null;
        } else {
            errorMap = new HashMap<>();
            for (ValidatorResponse validatorResponse : errorContext)
                errorMap.put(validatorResponse.getErrorCode().toString(), true);
        }
        TaskPrerequisiteFailedEvent event = TaskPrerequisiteFailedEvent.builder()
                .taskRequest(request)
                .command(request.getCommand().toString())
                .error(errorMap)
                .build();
        ingest(getEventObject(event, event.getTaskRequest().getTaskInstanceId(), TASK_PREREQUISITE_FAILED));
    }

    public void ingestSuccessfulExternalStoreOnboarding(StoredTaskInstance taskInstance, String merchantId, String storeId) {
        SuccessfulExternalEntityOnboardingEvent event = SuccessfulExternalEntityOnboardingEvent.builder()
                .actionId(taskInstance.getActionId())
                .campaignId(taskInstance.getCampaignId())
                .taskInstanceId(taskInstance.getTaskInstanceId())
                .entityId(taskInstance.getEntityId())
                .onboardedBy(taskInstance.getCurActor())
                .merchantId(merchantId)
                .storeId(storeId)
                .build();
        ingest(getEventObject(event, event.getTaskInstanceId(), SUCCESSFUL_STORE_ONBOARDING));
    }

    public void ingestTaskReschedulingEvent(TaskSchedulingPayload taskSchedulingPayload){
        ingest(getEventObject(taskSchedulingPayload, taskSchedulingPayload.getTaskInstanceId(), TASK_RESCHEDULED));
    }

    public void ingestUserLeadTaskCreationSuccessEvent(UserTaskCreationRequest request, StoredTaskInstance storedTaskInstance){
        UserLeadTaskCreationSuccessEvent userLeadTaskCreationSuccessEvent = UserLeadTaskCreationSuccessEvent.builder()
                .userTaskCreationRequest(request)
                .taskDefinitionId(storedTaskInstance.getTaskDefinitionId())
                .taskInstanceId(storedTaskInstance.getTaskInstanceId())
                .actionId(storedTaskInstance.getActionId())
                .build();
        ingest(getEventObject(userLeadTaskCreationSuccessEvent, storedTaskInstance.getTaskInstanceId(), USER_LEAD_TASK_CREATION_SUCCESS));
    }

    public void ingestLeadGenRequestEvent(UserTaskCreationRequest request){
        ingest(getEventObject(request, String.join("_", request.getMerchantId(), request.getStoreId()), LEAD_GENERATION_REQUEST_RECEIVED));
    }

    public void ingestHotspotStatusChangeEvent(StoredHotspot hotspot, EventConstants.HotspotEvents event) {
        ingest(getEventObject(hotspot.toFoxtrotEventData(), hotspot.getSectorId(), event));
    }

    public void ingestHotspotSyncFailedEvent(HotspotSyncRequest request) {
        ingest(getEventObject(request, request.getSectorId(), EventConstants.HotspotEvents.HOTSPOT_SYNC_FAILED));
    }

    public void ingestHotspotConfigCreationFailedEvent(HotspotConfigCreationRequest request) {
        ingest(getEventObject(request, request.getHotspotType(), EventConstants.HotspotEvents.HOTSPOT_CONFIG_CREATION_FAILED));
    }

    public void ingestHotspotConfigStatusChangeEvent(StoredHotspotConfig storedHotspotConfig, EventConstants.HotspotEvents event) {
        ingest(getEventObject(storedHotspotConfig.toFoxtrotEventData(), storedHotspotConfig.getHotspotType(), event));
    }

    public void ingestSelfAssignmentLimitBreached(String taskInstanceId, String userId, int actionSelfAssignmentLimit,
                                                  int agentAssignedTaskCount) {
        SelfAssignmentLimitBreachedEvent selfAssignmentLimitBreachedEvent = SelfAssignmentLimitBreachedEvent.builder()
                .userId(userId)
                .actionLevelSelfAssignmentLimit(actionSelfAssignmentLimit)
                .agentAssignedTaskCount(agentAssignedTaskCount)
                .build();

        ingest(getEventObject(selfAssignmentLimitBreachedEvent, taskInstanceId, SELF_ASSIGNMENT_LIMIT_BREACHED_EVENT));
    }

}
