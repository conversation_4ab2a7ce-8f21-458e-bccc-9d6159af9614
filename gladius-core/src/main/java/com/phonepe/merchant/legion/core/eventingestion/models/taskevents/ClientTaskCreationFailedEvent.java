package com.phonepe.merchant.legion.core.eventingestion.models.taskevents;

import com.phonepe.merchant.gladius.models.tasks.request.TaskInstanceMeta;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ClientTaskCreationFailedEvent {

    String taskDefinitionId;

    String assigneeId;

    String errorMessage;

    String createdBy;

    String entityId;

    Boolean markAvailable;

    TaskInstanceMeta taskInstanceMeta;

    String campaignId;

}
