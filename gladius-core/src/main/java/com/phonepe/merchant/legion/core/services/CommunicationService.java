package com.phonepe.merchant.legion.core.services;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.eventingestion.models.DownstreamServiceFailureEvent;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.utils.AuthHeaderProviderUtil;
import com.phonepe.merchant.legion.core.utils.LegionCoreConstants;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.models.userservice.common.GenericError;
import com.phonepe.platform.http.v2.common.Endpoint;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.common.OkHttpUtils;
import com.phonepe.platform.http.v2.common.ServiceEndpointProvider;
import io.appform.core.hystrix.CommandFactory;
import io.appform.ranger.discovery.bundle.ServiceDiscoveryConfiguration;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Credentials;
import okhttp3.Headers;
import okhttp3.HttpUrl;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Supplier;

import static javax.ws.rs.core.HttpHeaders.AUTHORIZATION;

@Slf4j
@Getter
@AllArgsConstructor
public abstract class CommunicationService {

    private final HttpConfiguration clientConfiguration;
    private final OkHttpClient client;
    private final Supplier<ServiceEndpointProvider> serviceEndpointProviderSupplier;
    private final ObjectMapper mapper;
    private final FoxtrotEventIngestionService eventIngestionService;
    private final ServiceDiscoveryConfiguration serviceDiscoveryConfiguration;
    private static final String MESSAGE = "message";
    private static final String JSON_MEDIA_TYPE = "application/json; charset=utf-8";
    protected static final String ERROR_LOG = "Error in call for commandName {} Response: {}";
    protected static final String COMMAND_NAME = "commandName";
    protected static final String RESPONSE = "response";
    protected static final String SERVICE_NAME = "service name";
    protected static final int STACK_TRACE_OFFSET_INDEX_3 = 3;
    protected static final int STACK_TRACE_OFFSET_INDEX_4 = 4;
    protected static final int STACK_TRACE_OFFSET_INDEX_5 = 5;


    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    static final class CallTypes {
        static final String POST = "post";
        static final String PUT = "put";
        static final String GET = "get";
        static final String DELETE = "delete";
        static final String PATCH = "patch";
    }

    protected boolean is5xx(int responseCode) {
        return responseCode / 100 == 5;
    }

    protected void handleResponseFailure(String commandName, String responseString, int code) {
        handleResponseFailure(commandName, responseString, code, STACK_TRACE_OFFSET_INDEX_4);
    }

    protected void handleResponseFailure(String commandName, String responseString, int code, int offsetInStackTrace) {
        log.error("Error in call for commandName: {}, Response: {}", commandName, responseString);
        ingestServiceFailureEvent(responseString, code, offsetInStackTrace);
        if (is5xx(code)) {
            throw LegionException.error(CoreErrorCode.COMMUNICATION_ERROR,
                    Map.of(
                            COMMAND_NAME, commandName,
                            SERVICE_NAME, this.getClass().getSimpleName(),
                            RESPONSE, responseString));
        }

        GenericError errorResponse = SerDe.readValue(responseString, GenericError.class);
        throw LegionException.error(
                CoreErrorCode.INTERNAL_ERROR,
                Map.of(MESSAGE, Objects.isNull(errorResponse) || Strings.isNullOrEmpty(errorResponse.getMessage()) ?
                        CoreErrorCode.INTERNAL_ERROR.getMessage() : errorResponse.getMessage())
        );
    }

    private void checkResponse(Response response, String commandName) {
        if (!response.isSuccessful()) {
            byte[] responseBody = body(response);
            String responseString = responseBody != null ? new String(responseBody) : null;
            handleResponseFailure(commandName, responseString, response.code(), STACK_TRACE_OFFSET_INDEX_5);
        }
    }

    protected <U> U readValue(Response response, TypeReference<U> responseType) {
        byte[] responseBody = body(response);
        if (Strings.isNullOrEmpty(new String(responseBody))) {
            response.body().close();
            return null;
        }
        response.body().close();
        return SerDe.readValue(getMapper(), responseBody, responseType);
    }

    private Headers getHeaders(Headers overrideHeaders) {

        if (overrideHeaders != null) {
            return overrideHeaders;
        }

        HttpConfiguration configuration = getClientConfiguration();
        if (Strings.isNullOrEmpty(configuration.getUsername()) ||
                Strings.isNullOrEmpty(configuration.getPassword())) {
            return Headers.of(new HashMap<>());
        }

        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(AUTHORIZATION, Credentials.basic(configuration.getUsername(), configuration.getPassword()));
        return Headers.of(headerMap);
    }

    Endpoint getEndPoint() {
        Preconditions.checkNotNull(getClient(), "Please call initialize() to setup the HTTP client");
        Optional<Endpoint> endpointOptional = serviceEndpointProviderSupplier.get().endpoint();
        if (!endpointOptional.isPresent()) {
            throw LegionException.error(
                    CoreErrorCode.INTERNAL_ERROR,
                    Collections.singletonMap(MESSAGE, "No endpoint for" + clientConfiguration.getServiceName() + " found"));
        }

        return endpointOptional.get();
    }

    protected Response callWithoutCheck(String commandName, String uriSuffix, Object requestBody, String callType, Headers headers) {

        Endpoint endpoint = getEndPoint();
        try {
            return CommandFactory.<Response>create(this.getClass().getSimpleName(), commandName)
                    .executor(() -> {
                                final HttpUrl url = clientConfiguration.isSecure() ? endpoint.secureUrl(uriSuffix) : endpoint.url(uriSuffix);
                                log.info("Calling service on url: {} to perform {}", url, commandName);
                                Request request = getRequest(url, requestBody, callType, headers, serviceDiscoveryConfiguration.getEnvironment());
                                return getClient().newCall(request).execute();
                            }
                    )
                    .execute();
        } catch (LegionException e) {
            throw e;
        } catch (Exception e) {
            throw LegionException.propagate(CoreErrorCode.INTERNAL_ERROR, e);
        }
    }

    protected Response call(String commandName, String uriSuffix, Object requestBody, String callType, Headers headers) {
        Response response = callWithoutCheck(commandName, uriSuffix, requestBody, callType, headers);
        checkResponse(response, commandName);
        return response;
    }

    private Request getRequest(final HttpUrl url, Object requestBody, String callType, Headers overrideHeaders, String requestEnv) {


        log.info("X-REQUEST-ENV header is {}", requestEnv);
        RequestBody body = null == requestBody ? RequestBody.create(MediaType.parse(JSON_MEDIA_TYPE), "{}")
                : RequestBody.create(
                MediaType.parse(JSON_MEDIA_TYPE),
                Objects.requireNonNull(SerDe.writeValueAsBytes(requestBody))
        );
        return getRequest(url, body, callType, overrideHeaders, requestEnv);
    }

    protected Request getRequest(HttpUrl url, RequestBody body, String callType, Headers overrideHeaders, String requestEnv) {
        RequestBody requestbody;
        switch (callType) {
            case CallTypes.DELETE:
                return new Request.Builder()
                        .url(url)
                        .headers(getHeaders(overrideHeaders))
                        .addHeader(LegionCoreConstants.X_REQUEST_ENV, requestEnv)
                        .delete()
                        .build();

            case CallTypes.POST:
                requestbody = null == body ? RequestBody.create(MediaType.parse(JSON_MEDIA_TYPE), "{}")
                        : body;
                return new Request.Builder()
                        .url(url)
                        .headers(getHeaders(overrideHeaders))
                        .addHeader(LegionCoreConstants.X_REQUEST_ENV, requestEnv)
                        .post(requestbody)
                        .build();

            case CallTypes.PUT:
                requestbody = null == body ? RequestBody.create(MediaType.parse(JSON_MEDIA_TYPE), "{}")
                        : body;
                return new Request.Builder()
                        .url(url)
                        .headers(getHeaders(overrideHeaders))
                        .addHeader(LegionCoreConstants.X_REQUEST_ENV, requestEnv)
                        .put(requestbody)
                        .build();

            case CallTypes.PATCH:
                requestbody = null == body ? RequestBody.create(MediaType.parse(JSON_MEDIA_TYPE), "{}")
                        : body;
                return new Request.Builder()
                        .url(url)
                        .headers(getHeaders(overrideHeaders))
                        .addHeader(LegionCoreConstants.X_REQUEST_ENV, requestEnv)
                        .patch(requestbody)
                        .build();
            default:
                return new Request.Builder()
                        .url(url)
                        .headers(getHeaders(overrideHeaders))
                        .addHeader(LegionCoreConstants.X_REQUEST_ENV, requestEnv)
                        .get()
                        .build();
        }
    }

    protected byte[] body(Response response) {
        try {
            return OkHttpUtils.bodyAsBytes(response);
        } catch (IOException e) {
            throw LegionException.propagate(CoreErrorCode.IO_ERROR, e);
        }
    }

    protected String bodyStr(Response response) {
        try {
            return OkHttpUtils.bodyAsString(response);
        } catch (IOException e) {
            throw LegionException.propagate(CoreErrorCode.IO_ERROR, e);
        }
    }

    protected void ingestServiceFailureEvent(String responseString, int code) {
        ingestServiceFailureEvent(responseString, code, STACK_TRACE_OFFSET_INDEX_3);
    }

    protected void ingestServiceFailureEvent(String responseString, int code, int offsetInStackTrace) {
        StackTraceElement stackTraceElement = Thread.currentThread().getStackTrace()[offsetInStackTrace];
        String[] path = stackTraceElement.getClassName().split("\\.");
        String serviceName = path[path.length - 1];
        eventIngestionService.ingestDownstreamServiceFailureEvent(DownstreamServiceFailureEvent.builder()
                .serviceName(serviceName)
                .commandName(stackTraceElement.getMethodName())
                .code(code)
                .reason(responseString)
                .build());
    }

    protected Headers getSystemAuthHeader() {
        return Headers.of(Map.of("Authorization",
                AuthHeaderProviderUtil.getSystemAuthHeader()));
    }

}
