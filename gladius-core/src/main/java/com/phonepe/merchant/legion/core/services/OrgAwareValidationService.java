//package com.phonepe.merchant.legion.core.services;
//
//import com.google.inject.Inject;
//import com.google.inject.Singleton;
//import com.phonepe.merchant.gladius.models.legion.v2.AgentProfileV2;
//import com.phonepe.merchant.gladius.models.legion.v2.OrgProfile;
//import com.phonepe.merchant.legion.core.config.LegionMigrationConfig;
//import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
//import io.appform.functionmetrics.MonitoredFunction;
//import lombok.extern.slf4j.Slf4j;
//
//import java.util.HashSet;
//import java.util.List;
//import java.util.Set;
//
///**
// * Service for org-aware validation logic
// * Handles validation that considers org-level hierarchy and roles
// */
//@Slf4j
//@Singleton
//public class OrgAwareValidationService {
//
//    private final LegionServiceAdapter legionServiceAdapter;
//    private final LegionServiceV2 legionServiceV2;
//    private final LegionMigrationConfig migrationConfig;
//
//    @Inject
//    public OrgAwareValidationService(LegionServiceAdapter legionServiceAdapter,
//                                   LegionServiceV2 legionServiceV2,
//                                   LegionMigrationConfig migrationConfig) {
//        this.legionServiceAdapter = legionServiceAdapter;
//        this.legionServiceV2 = legionServiceV2;
//        this.migrationConfig = migrationConfig;
//    }
//
//    /**
//     * Validate if requester is in hierarchy of user (org-aware)
//     */
//    @MonitoredFunction
//    public boolean validateRequesterInHierarchy(String userId, String requesterId) {
//        if (userId == null || requesterId == null || userId.equals(requesterId)) {
//            return true;
//        }
//
//        if (migrationConfig.isEnableOrgHierarchyValidation()) {
//            return validateOrgHierarchy(userId, requesterId);
//        } else {
//            return validateLegacyHierarchy(userId, requesterId);
//        }
//    }
//
//    /**
//     * Validate hierarchy using org-level data
//     */
//    private boolean validateOrgHierarchy(String userId, String requesterId) {
//        try {
//            AgentProfileV2 userProfile = legionServiceAdapter.getAgentProfileV2(userId);
//            AgentProfileV2 requesterProfile = legionServiceAdapter.getAgentProfileV2(requesterId);
//
//            // Check if both agents belong to same org
//            if (!isSameOrg(userProfile, requesterProfile)) {
//                log.warn("Agents {} and {} belong to different orgs", userId, requesterId);
//                return false;
//            }
//
//            // Traverse org hierarchy
//            Set<String> visitedManagers = new HashSet<>();
//            String currentManagerId = userProfile.getEffectiveManagerId();
//
//            while (currentManagerId != null && !visitedManagers.contains(currentManagerId)) {
//                visitedManagers.add(currentManagerId);
//
//                if (requesterId.equals(currentManagerId)) {
//                    return true;
//                }
//
//                AgentProfileV2 managerProfile = legionServiceAdapter.getAgentProfileV2(currentManagerId);
//                currentManagerId = managerProfile.getEffectiveManagerId();
//            }
//
//            return false;
//        } catch (Exception e) {
//            log.error("Error validating org hierarchy for user {} and requester {}", userId, requesterId, e);
//            // Fallback to legacy validation
//            return validateLegacyHierarchy(userId, requesterId);
//        }
//    }
//
//    /**
//     * Validate hierarchy using legacy agent-level data
//     */
//    private boolean validateLegacyHierarchy(String userId, String requesterId) {
//        try {
//            Set<String> visitedManagers = new HashSet<>();
//            AgentProfile agentProfile = legionServiceAdapter.getAgentProfile(userId);
//            legionServiceAdapter.getAgentProfile(requesterId); // Validate requester exists
//
//            while (agentProfile.getManagerId() != null && !visitedManagers.contains(agentProfile.getManagerId())) {
//                visitedManagers.add(agentProfile.getManagerId());
//
//                if (requesterId.equals(agentProfile.getManagerId())) {
//                    return true;
//                }
//
//                agentProfile = legionServiceAdapter.getAgentProfile(agentProfile.getManagerId());
//            }
//
//            return false;
//        } catch (Exception e) {
//            log.error("Error validating legacy hierarchy for user {} and requester {}", userId, requesterId, e);
//            return false;
//        }
//    }
//
//    /**
//     * Validate if agent role is allowed for a specific operation (org-aware)
//     */
//    @MonitoredFunction
//    public boolean validateAgentRole(String agentId, List<String> allowedRoles, List<String> blacklistedRoles) {
//        if (migrationConfig.isEnableOrgRoleValidation()) {
//            return validateOrgRole(agentId, allowedRoles, blacklistedRoles);
//        } else {
//            return validateLegacyRole(agentId, allowedRoles, blacklistedRoles);
//        }
//    }
//
//    /**
//     * Validate role using org-level data
//     */
//    private boolean validateOrgRole(String agentId, List<String> allowedRoles, List<String> blacklistedRoles) {
//        try {
//            AgentProfileV2 agentProfile = legionServiceAdapter.getAgentProfileV2(agentId);
//            String effectiveRole = agentProfile.getEffectiveRole();
//
//            if (effectiveRole == null) {
//                return false;
//            }
//
//            // Check blacklisted roles first
//            if (blacklistedRoles != null && blacklistedRoles.contains(effectiveRole)) {
//                return false;
//            }
//
//            // Check allowed roles
//            if (allowedRoles != null && !allowedRoles.isEmpty()) {
//                return allowedRoles.contains(effectiveRole);
//            }
//
//            return true;
//        } catch (Exception e) {
//            log.error("Error validating org role for agent {}", agentId, e);
//            return validateLegacyRole(agentId, allowedRoles, blacklistedRoles);
//        }
//    }
//
//    /**
//     * Validate role using legacy agent-level data
//     */
//    private boolean validateLegacyRole(String agentId, List<String> allowedRoles, List<String> blacklistedRoles) {
//        try {
//            AgentProfile agentProfile = legionServiceAdapter.getAgentProfile(agentId);
//            String agentRole = agentProfile.getAgentType().name();
//
//            // Check blacklisted roles first
//            if (blacklistedRoles != null && blacklistedRoles.contains(agentRole)) {
//                return false;
//            }
//
//            // Check allowed roles
//            if (allowedRoles != null && !allowedRoles.isEmpty()) {
//                return allowedRoles.contains(agentRole);
//            }
//
//            return true;
//        } catch (Exception e) {
//            log.error("Error validating legacy role for agent {}", agentId, e);
//            return false;
//        }
//    }
//
//    /**
//     * Check if two agents belong to the same organization
//     */
//    private boolean isSameOrg(AgentProfileV2 agent1, AgentProfileV2 agent2) {
//        if (agent1.getOrgId() == null || agent2.getOrgId() == null) {
//            // If org info is not available, consider them as same org for backward compatibility
//            return true;
//        }
//        return agent1.getOrgId().equals(agent2.getOrgId());
//    }
//
//    /**
//     * Get organization profile for an agent
//     */
//    @MonitoredFunction
//    public OrgProfile getAgentOrgProfile(String agentId) {
//        try {
//            AgentProfileV2 agentProfile = legionServiceAdapter.getAgentProfileV2(agentId);
//            if (agentProfile.getOrgId() != null) {
//                return legionServiceV2.getOrgProfile(agentProfile.getOrgId());
//            }
//            return null;
//        } catch (Exception e) {
//            log.error("Error getting org profile for agent {}", agentId, e);
//            return null;
//        }
//    }
//
//    /**
//     * Check if agent belongs to a specific organization
//     */
//    @MonitoredFunction
//    public boolean isAgentInOrg(String agentId, String orgId) {
//        try {
//            AgentProfileV2 agentProfile = legionServiceAdapter.getAgentProfileV2(agentId);
//            return orgId.equals(agentProfile.getOrgId());
//        } catch (Exception e) {
//            log.error("Error checking if agent {} belongs to org {}", agentId, orgId, e);
//            return false;
//        }
//    }
//}
