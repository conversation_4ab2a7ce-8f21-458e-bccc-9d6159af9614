package com.phonepe.merchant.legion.tasks.services.impl;

import com.phonepe.merchant.gladius.models.core.audit.Audit;
import com.phonepe.merchant.gladius.models.tasks.domain.TaskInstance;
import com.phonepe.merchant.gladius.models.tasks.enums.Sorter;
import com.phonepe.merchant.gladius.models.tasks.request.TaskInstanceMeta;
import com.phonepe.merchant.gladius.models.tasks.response.LatestTaskInfo;
import com.phonepe.merchant.gladius.models.tasks.response.TaskInstanceAuditResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaType;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredCommentsOnTask;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.converters.TaskInstanceAuditConverter;
import com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode;
import com.phonepe.merchant.legion.tasks.repository.CommentsOnTaskRepository;
import com.phonepe.merchant.legion.tasks.repository.TaskInstanceRepository;
import com.phonepe.merchant.legion.tasks.utils.TaskInstanceTransformationUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.MAX_RESULTS;
import static java.util.Optional.ofNullable;

@Slf4j
public abstract class BaseHistoryService {

    protected final TaskInstanceRepository taskInstanceRepository;
    protected final CommentsOnTaskRepository commentsOnTaskRepository;
    protected final TaskInstanceAuditConverter taskInstanceAuditConverter;
    protected final LegionService legionService;

    protected BaseHistoryService(TaskInstanceRepository taskInstanceRepository,
                                 CommentsOnTaskRepository commentsOnTaskRepository,
                                 TaskInstanceAuditConverter taskInstanceAuditConverter,
                                 LegionService legionService) {
        this.taskInstanceRepository = taskInstanceRepository;
        this.commentsOnTaskRepository = commentsOnTaskRepository;
        this.taskInstanceAuditConverter = taskInstanceAuditConverter;
        this.legionService = legionService;
    }

    protected List<Audit<StoredTaskInstance>> fetchTaskAudits(String taskInstanceId) {
        return taskInstanceRepository.audit(taskInstanceId);
    }

    protected List<StoredCommentsOnTask> fetchStoredComments(String taskInstanceId) {
        return commentsOnTaskRepository.getFromTaskInstanceId(taskInstanceId, Sorter.CREATED_AT, 0, MAX_RESULTS);
    }

    protected TaskInstance fetchLatestTask(String taskInstanceId, List<Audit<StoredTaskInstance>> audits) {
        TaskInstance latestTaskState = TaskInstanceTransformationUtils.toTaskInstance(
                audits.isEmpty() ? taskInstanceRepository.get(taskInstanceId).orElse(null)
                        : audits.get(audits.size() - 1).getData()
        );
        if (Objects.isNull(latestTaskState)) {
            throw LegionException.error(LegionTaskErrorCode.INVALID_TASK_INSTANCE_ID);
        }
        return latestTaskState;
    }

    protected List<TaskInstanceAuditResponse> processAudits(String taskInstanceId, List<Audit<StoredTaskInstance>> audits) {
        return IntStream.range(0, audits.size() - 1)
                .mapToObj(i -> {
                    TaskInstance current = TaskInstanceTransformationUtils.toTaskInstance(audits.get(i).getData());
                    TaskInstance next = TaskInstanceTransformationUtils.toTaskInstance(audits.get(i + 1).getData());
                    String updatedBy = audits.get(i + 1).getData().getUpdatedBy();
                    Date updatedAt = audits.get(i + 1).getUpdatedAt();

                    return taskInstanceAuditConverter.convert(taskInstanceId, current, next, updatedBy, updatedAt);
                })
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }

    protected List<TaskInstanceAuditResponse> processComments(String taskInstanceId, List<StoredCommentsOnTask> comments) {
        return comments.stream()
                .map(comment -> taskInstanceAuditConverter.convert(taskInstanceId,null, comment, comment.getCreatedBy(), comment.getUpdatedAt()))
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }

    protected Map<String, AgentProfile> fetchAgentProfiles(List<TaskInstanceAuditResponse> history, TaskInstance latestTaskState) {
        Set<String> agentIds = new HashSet<>();

        // Collect agent IDs from audits
        history.stream()
                .map(TaskInstanceAuditResponse::getUpdatedById)
                .filter(Objects::nonNull)
                .forEach(agentIds::add);

        // Add current actor
        ofNullable(latestTaskState.getCurActor())
                .ifPresent(agentIds::add);

        return agentIds.stream()
                .collect(HashMap::new, (m, id) -> {
                    try {
                        m.put(id, legionService.getAgentProfile(id));
                    } catch (LegionException e) {
                        if (CoreErrorCode.INTERNAL_ERROR.equals(e.getErrorCode())) {
                            log.error("Agent {} doesn't exist", id);
                            m.put(id, null);
                            return;
                        }
                        throw e;
                    }
                }, HashMap::putAll);
    }

    public List<TaskInstanceAuditResponse> fetchTaskInstanceAuditList(String taskInstanceId,
                                                                      List<Audit<StoredTaskInstance>> audits,
                                                                      List<StoredCommentsOnTask> storedComments,
                                                                      int start, int pageSize, Sorter sorter,
                                                                      boolean paginationRequired) {
        List<TaskInstanceAuditResponse> taskInstanceAuditResponses = buildHistory(taskInstanceId, sorter, audits, storedComments);
        taskInstanceAuditResponses = paginationRequired ? paginatedHistory(taskInstanceAuditResponses, start, pageSize) : taskInstanceAuditResponses;
        return taskInstanceAuditResponses;
    }

    public List<TaskInstanceAuditResponse> paginatedHistory(List<TaskInstanceAuditResponse> history, int start, int pageSize) {
        return history.stream()
                .skip(start)
                .limit(pageSize)
                .toList();
    }


        private List<TaskInstanceAuditResponse> buildHistory(String taskInstanceId, Sorter sorter, List<Audit<StoredTaskInstance>> audits,
                                                             List<StoredCommentsOnTask> storedComments
    ) {
            List<TaskInstanceAuditResponse> auditDTOs = processAudits(taskInstanceId, audits);
            List<TaskInstanceAuditResponse> commentDTOs = processComments(taskInstanceId, storedComments);
        return Stream.concat(auditDTOs.stream(), commentDTOs.stream())
                .filter(Objects::nonNull)
                .sorted(TaskInstanceAuditResponse.getComparator(sorter))
                .toList();
    }

    public void populateAuditResponse(List<TaskInstanceAuditResponse> history,
                                      Map<String, AgentProfile> agentProfileMap) {
        history.forEach(h -> {
            AgentProfile profile = agentProfileMap.get(h.getUpdatedById());
            ofNullable(profile).ifPresent(p -> {
                h.setUpdatedByName(profile.getName());
                h.setUpdatedByRole(profile.getAgentType());
            });
        });

    }

    public String getLeadIntent(Map<TaskMetaType, Object> taskMeta) {
        return ofNullable(taskMeta.get(TaskMetaType.LEAD_INTENT))
                .map(Object::toString)
                .orElse(null);
    }

    public String getRemarks(Map<TaskMetaType, Object> taskMeta) {
        return ofNullable(taskMeta.get(TaskMetaType.REMARK))
                .map(Object::toString)
                .orElse(null);
    }

    public LatestTaskInfo populateLatestTaskDetailsInfo(TaskInstance latestTaskState,
                                                         Map<String, AgentProfile> agentProfileMap) {
        AgentProfile currActor = agentProfileMap.get(latestTaskState.getCurActor());
        Map<TaskMetaType, Object> taskMeta = ofNullable(latestTaskState.getInstanceMeta())
                .map(TaskInstanceMeta::getTaskMetaAsMap)
                .orElse(new HashMap<>());
        return LatestTaskInfo.builder()
                .taskInstanceId(latestTaskState.getTaskInstanceId())
                .rescheduledAt(latestTaskState.getRescheduledAt())
                .leadIntent(getLeadIntent(taskMeta))
                .remarks(getRemarks(taskMeta))
                .active(latestTaskState.getActive())
                .currentAgentName(ofNullable(currActor).map(AgentProfile::getName).orElse(null))
                .currentAgentId(ofNullable(currActor).map(AgentProfile::getAgentId).orElse(latestTaskState.getCurActor()))
                .currentAgentRole(ofNullable(currActor).map(AgentProfile::getAgentType).orElse(null))
                .build();

    }
}

