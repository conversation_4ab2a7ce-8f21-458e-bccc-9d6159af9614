package com.phonepe.merchant.legion.tasks.actions.validators;

import com.google.inject.Inject;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskAssignRequest;
import com.phonepe.merchant.gladius.models.tasks.validation.ActionValidator;
import com.phonepe.merchant.gladius.models.tasks.validation.ValidatorResponse;
import com.phonepe.merchant.gladius.models.tasks.validation.configs.AgentTypeValidatorConfig;
import com.phonepe.merchant.gladius.models.tasks.validation.configs.ValidatorConfig;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.core.services.OrgAwareValidationService;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.actions.annotations.ActionValidatorMarker;
import com.phonepe.models.merchants.tasks.EntityType;


import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.AGENT_TYPE_VALIDATOR;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.SELF_ASSIGN_NOT_ALLOWED;

@ActionValidatorMarker(name = AGENT_TYPE_VALIDATOR)
public class AgentTypeValidator implements ActionValidator<TaskAssignRequest> {
    private final LegionService legionService;
    private final OrgAwareValidationService orgAwareValidationService;

    @Inject
    public AgentTypeValidator(LegionService legionService, OrgAwareValidationService orgAwareValidationService) {
        this.legionService = legionService;
        this.orgAwareValidationService = orgAwareValidationService;
    }

    @Override
    public void validate(EntityType entityType, ValidatorConfig validatorConfig) {
        // This validator does not depend on entity type
    }

    @Override
    public ValidatorResponse validate(TaskAssignRequest taskAssignRequest, ValidatorConfig validatorConfig) {
        AgentTypeValidatorConfig agentTypeValidatorConfig = (AgentTypeValidatorConfig) validatorConfig;
        String agentId = taskAssignRequest.getAssignedTo();

        // Convert AgentType set to String list for org-aware validation
        java.util.List<String> blacklistedRoles = agentTypeValidatorConfig.getBlacklistedAgentTypes()
                .stream()
                .map(Enum::name)
                .toList();

        // Use org-aware validation which handles both V1 and V2 logic
        boolean isValid = orgAwareValidationService.validateAgentRole(agentId, null, blacklistedRoles);

        if (isValid) {
            return ValidatorResponse.builder()
                    .validated(true)
                    .build();
        }

        return ValidatorResponse.builder()
                .validated(false)
                .errorCode(SELF_ASSIGN_NOT_ALLOWED)
                .errorMessage("Agent is not allowed to perform this action")
                .build();
    }

}
