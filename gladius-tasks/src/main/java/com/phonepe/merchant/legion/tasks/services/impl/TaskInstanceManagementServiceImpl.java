package com.phonepe.merchant.legion.tasks.services.impl;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.gladius.models.entitystore.Entity;
import com.phonepe.merchant.gladius.models.entitystore.EntityStoreRequest;
import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineEvent;
import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState;
import com.phonepe.merchant.gladius.models.tasks.domain.TaskInstance;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.enums.TaskSchedulingType;
import com.phonepe.merchant.gladius.models.tasks.request.ClientTaskDeleteRequest;
import com.phonepe.merchant.gladius.models.tasks.request.CreateCommentRequest;
import com.phonepe.merchant.gladius.models.tasks.request.CreateTaskInstanceRequest;
import com.phonepe.merchant.gladius.models.tasks.request.EntityTaskListingRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskCompletionByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskCompletionByTaskTypeRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskExpireRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskInstanceMeta;
import com.phonepe.merchant.gladius.models.tasks.request.TaskManualVerificationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskMetaUpdateRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskSchedulingPayload;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskAssignRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCommandRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCompleteRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCreateRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskMarkAvailableRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskStartRequest;
import com.phonepe.merchant.gladius.models.tasks.response.Campaign;
import com.phonepe.merchant.gladius.models.tasks.response.TaskActionInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskAttributeInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskDefinitionInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaInformation;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskSearchResponse;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskDefinition;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskTransition;
import com.phonepe.merchant.gladius.models.tasks.utils.Miscellaneous;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventExecutor;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.repository.impl.ChimeraRepositoryImpl;
import com.phonepe.merchant.legion.core.services.AtlasService;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.core.services.OdinService;
import com.phonepe.merchant.legion.core.utils.DateUtils;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.merchant.legion.tasks.entitystore.EntityStore;
import com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode;
import com.phonepe.merchant.legion.tasks.flows.models.ActionsEligibleForTransactionLocationTasks;
import com.phonepe.merchant.legion.tasks.repository.TaskESRepository;
import com.phonepe.merchant.legion.tasks.repository.TaskInstanceRepository;
import com.phonepe.merchant.legion.tasks.repository.TaskTransitionRepository;
import com.phonepe.merchant.legion.tasks.search.query.search.TaskTypeFilterQuery;
import com.phonepe.merchant.legion.tasks.services.CommentsService;
import com.phonepe.merchant.legion.tasks.services.TaskAttributeService;
import com.phonepe.merchant.legion.tasks.services.TaskDiscoveryService;
import com.phonepe.merchant.legion.tasks.services.TaskInstanceManagementService;
import com.phonepe.merchant.legion.tasks.services.TaskManagementService;
import com.phonepe.merchant.legion.tasks.services.ValidationService;
import com.phonepe.merchant.legion.tasks.utils.MerchantTransactionMetaUtils;
import com.phonepe.merchant.legion.tasks.utils.TaskInstanceTransformationUtils;
import com.phonepe.merchant.legion.tasks.utils.ValidationUtils;
import com.phonepe.models.merchants.tasks.EntityType;
import io.appform.functionmetrics.MonitoredFunction;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.UnaryOperator;

import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.ELIGIBLE_ACTIONS_FOR_TRANSACTION_LOCATION_TASKS;
import static com.phonepe.merchant.legion.core.exceptions.CoreErrorCode.NOT_FOUND;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.TASK_META_UPDATE_FAILED;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.INITIAL_PAGE_NO;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.MAXIMUM_PAGE_SIZE;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.MESSAGE;
import static com.phonepe.merchant.legion.tasks.utils.TaskFoxtrotEventUtils.toForceTaskCompletionEvent;
import static com.phonepe.merchant.legion.tasks.utils.TaskFoxtrotEventUtils.toTaskMetaUpdateEvent;
import static com.phonepe.merchant.legion.tasks.utils.TaskInstanceTransformationUtils.setDueDateAndRescheduleDate;
import static com.phonepe.merchant.legion.tasks.utils.TaskInstanceTransformationUtils.toTaskInstanceMeta;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class TaskInstanceManagementServiceImpl implements TaskInstanceManagementService {

    private final EntityStore entityStore;
    private final TaskESRepository taskEsRepository;
    private final TaskTransitionRepository taskTransitionRepository;
    private final TaskInstanceRepository taskInstanceRepository;
    private final TaskManagementService taskManagementService;
    private final FoxtrotEventIngestionService foxtrotEventIngestionService;
    private final TaskAttributeService taskAttributeService;
    private final Miscellaneous miscellaneous;
    private final FoxtrotEventExecutor eventExecutor;
    private final TaskTypeFilterQuery taskTypeFilterQuery;
    private final TaskDiscoveryService taskDiscoveryService;
    private final LegionService legionService;

    private final OdinService odinService;
    private final CommentsService commentsService;
    private final ValidationService validationService;
    private final AtlasService atlasService;
    private final ChimeraRepositoryImpl chimeraRepository;


    @Override
    @MonitoredFunction
    public StoredTaskInstance create(TaskDefinitionInstance taskDefinitionInstance,
                                     CreateTaskInstanceRequest request,
                                     TaskActionInstance taskActionInstance,
                                     Campaign campaign,
                                     Set<String> storeTags) {
        if (taskActionInstance.getEntityType() == EntityType.STORE) {
            List<TaskMetaInformation> merchantAttributes = MerchantTransactionMetaUtils.getMerchantMetaForTask(odinService.getMerchantTransactionData(request.getEntityId().split("_")[0]));
            if (request.getTaskInstanceMeta() != null && request.getTaskInstanceMeta().getTaskMetaList() != null) {
                request.getTaskInstanceMeta().getTaskMetaList().addAll(merchantAttributes);
            }
            else if (request.getTaskInstanceMeta() != null) {
                request.getTaskInstanceMeta().setTaskMetaList(merchantAttributes);
            } else {
                request.setTaskInstanceMeta(TaskInstanceMeta.builder()
                        .taskMetaList(merchantAttributes)
                        .build());
            }
        }
        Entity entity = fetchById(request, taskActionInstance);
        DiscoveryTaskInstance discoveryTaskInstance = TaskInstanceTransformationUtils
                .toDiscoveryTaskInstance(taskDefinitionInstance, taskActionInstance, entity, campaign, request, generateTaskInstanceId(request.getEntityId()));
        StoredTaskInstance storedTaskInstance =
                TaskInstanceTransformationUtils.toStoredTaskInstance(taskDefinitionInstance, discoveryTaskInstance, request);
        StoredTaskInstance savedStoredTaskInstance = taskInstanceRepository.save(storedTaskInstance);
        StoredTaskTransition creationMeta = taskTransitionRepository.save(
                TaskInstanceTransformationUtils.toCreationTransition(savedStoredTaskInstance, request)
        );
        discoveryTaskInstance.setTags(storeTags);
        taskEsRepository.create(discoveryTaskInstance.getTaskInstanceId(), discoveryTaskInstance);
        return creationMeta.getTaskInstance();
    }

    private String generateTaskInstanceId(String entityId){
        return taskInstanceRepository.generateTaskInstanceId(entityId);
    }
    public Map<String, Set<String>> getTaskAttributes(StoredTaskDefinition storedTaskDefinition) {
        Map<String, Set<String>> attributes = new HashMap<>();
        storedTaskDefinition.getActiveAttributes().forEach(attributeValue -> {
            TaskAttributeInstance attribute = taskAttributeService.getFromDB(attributeValue.getAttributeValue());
            @NotNull String attributeType = attribute.getAttributeType().getName();
            attributes.computeIfAbsent(attributeType, key -> new HashSet<>());
            attributes.get(attributeType).add(attributeValue.getAttributeValue());
        });
        return attributes;
    }

    private Entity fetchById(CreateTaskInstanceRequest request, TaskActionInstance storedTaskAction) {
        try {
            Optional<Entity> entityStoreById = entityStore.getById(EntityStoreRequest.builder()
                    .referenceId(request.getEntityId())
                    .entityType(storedTaskAction.getEntityType())
                    .build());
            if (!entityStoreById.isPresent() || !entityStoreById.get().getType().equals(storedTaskAction.getEntityType())) {
                throw LegionException.error(LegionTaskErrorCode.INVALID_ENTITY_ID);
            }
            Entity entity = entityStoreById.get();
            if (request.getTransactionLocation() != null && isTransactionLocationUpdateRequired(storedTaskAction.getActionId())) {
                updateEntityLocationWithSectors(entity, request.getTransactionLocation());
            }
            return entity;
        } catch (LegionException e) {
            if (e.getErrorCode() == NOT_FOUND) {
                throw LegionException.error(LegionTaskErrorCode.INVALID_ENTITY_ID);
            }
            throw e;
        }
    }

    private boolean isTransactionLocationUpdateRequired(String actionId) {
        ActionsEligibleForTransactionLocationTasks config = chimeraRepository.getChimeraConfig(
                ELIGIBLE_ACTIONS_FOR_TRANSACTION_LOCATION_TASKS,
                ActionsEligibleForTransactionLocationTasks.class
        );
        return config.getEligibleActionIds().contains(actionId);
    }

    private void updateEntityLocationWithSectors(Entity entity, EsLocationRequest transactionLocation) {
        List<String> sectors = atlasService.getSectorIdByLatLong(
                transactionLocation.getLat(),
                transactionLocation.getLon()
        );
        entity.setLocation(transactionLocation);
        entity.setPolygonIds(sectors);
    }


    @Override
    @MonitoredFunction
    public StoredTaskInstance markAvailable(StoredTaskInstance storedTaskInstance,
                                            TaskMarkAvailableRequest request) {
        StoredTaskTransition availableMeta = taskTransitionRepository.save(
                TaskInstanceTransformationUtils.toMarkAvailableTransition(storedTaskInstance, request)
        );
        taskEsRepository.syncWithDB(storedTaskInstance.getTaskInstanceId(),false);
        return availableMeta.getTaskInstance();
    }

    @Override
    @MonitoredFunction
    public StoredTaskInstance boundedAssign(StoredTaskInstance storedTaskInstance,
                                            TaskAssignRequest taskAssignRequest) {
        taskTransitionRepository.save(
                TaskInstanceTransformationUtils.toBoundedAssignmentTransition(storedTaskInstance, taskAssignRequest)
        );
        taskEsRepository.syncWithDB(storedTaskInstance.getTaskInstanceId(),false);
        return storedTaskInstance;
    }

    @Override
    public StoredTaskInstance selfAssign(StoredTaskInstance storedTaskInstance, TaskAssignRequest taskAssignRequest) {
        StoredTaskTransition save = taskTransitionRepository.save(
                TaskInstanceTransformationUtils.toSelfAssignmentTransition(storedTaskInstance, taskAssignRequest)
        );
        taskEsRepository.syncWithDB(storedTaskInstance.getTaskInstanceId(),true);
        return save.getTaskInstance();
    }

    @Override
    public void update(String taskInstanceId, UnaryOperator<StoredTaskInstance> updateOperation, boolean immediateRefresh) {
        taskInstanceRepository.update(taskInstanceId, updateOperation);
        taskEsRepository.syncWithDB(taskInstanceId,immediateRefresh);
    }

    @Override
    public void delete(String taskInstanceId) {
        taskInstanceRepository.update(taskInstanceId, TaskInstanceTransformationUtils.delete());
        taskEsRepository.syncWithDB(taskInstanceId,false);
    }

    @Override
    public TaskInstance getById(TaskByIdRequest taskByIdRequest) {
        return TaskInstanceTransformationUtils.toTaskInstance(
                taskInstanceRepository.get(taskByIdRequest.getTaskInstanceId())
                        .orElseThrow(() -> LegionException.error(NOT_FOUND, Map.of(MESSAGE, "Invalid task instance id")))
        );
    }

    @Override
    public TaskInstance getById(String taskInstanceId) {
        return getById(TaskByIdRequest.builder()
                .taskInstanceId(taskInstanceId).build());
    }

    private StoredTaskInstance updateTaskStatus(StoredTaskTransition storedTaskTransition) {
        return taskTransitionRepository.save(storedTaskTransition).getTaskInstance();
    }

    @Override
    public void markCompleted(@org.jetbrains.annotations.NotNull StoredTaskInstance storedTaskInstance, TaskCompleteRequest taskVerificationRequest) {
        updateTaskStatus(StoredTaskTransition.builder()
                .taskInstanceId(storedTaskInstance.getTaskInstanceId())
                .transitionId(storedTaskInstance.lastTransition().getTransitionId() + 1)
                .partitionId(storedTaskInstance.getPartitionId())
                .taskInstance(storedTaskInstance)
                .context(SerDe.writeValueAsBytes(taskVerificationRequest))
                .toState(LegionTaskStateMachineState.COMPLETED)
                .fromState(storedTaskInstance.getCurState())
                .event(LegionTaskStateMachineEvent.VERIFICATION_INIT)
                .actor(taskVerificationRequest.getCompletedBy())
                .build());
        taskEsRepository.syncWithDB(storedTaskInstance.getTaskInstanceId(),true);
    }

    @Override
    public StoredTaskInstance markStarted(StoredTaskInstance storedTaskInstance, TaskStartRequest request) {
        StoredTaskInstance instance = updateTaskStatus(StoredTaskTransition.builder()
                .taskInstanceId(storedTaskInstance.getTaskInstanceId())
                .transitionId(storedTaskInstance.lastTransition().getTransitionId() + 1)
                .partitionId(storedTaskInstance.getPartitionId())
                .taskInstance(storedTaskInstance)
                .context(SerDe.writeValueAsBytes(request))
                .toState(LegionTaskStateMachineState.STARTED)
                .fromState(storedTaskInstance.getCurState())
                .event(LegionTaskStateMachineEvent.MARK_STARTED)
                .actor(request.getStartedBy())
                .build());
        taskEsRepository.syncWithDB(storedTaskInstance.getTaskInstanceId(), true);
        return instance;
    }

    @Override
    public void markVerified(StoredTaskInstance storedTaskInstance, TaskManualVerificationRequest request) {
        updateTaskStatus(StoredTaskTransition.builder()
                .toState(request.getTaskState())
                .fromState(storedTaskInstance.getCurState())
                .event(LegionTaskStateMachineEvent.MANUAL_VERIFICATION)
                .taskInstance(storedTaskInstance)
                .actor(request.getVerifiedBy())
                .transitionId(storedTaskInstance.lastTransition().getTransitionId() + 1)
                .taskInstanceId(storedTaskInstance.getTaskInstanceId())
                .context(SerDe.writeValueAsBytes(request))
                .partitionId(storedTaskInstance.getPartitionId())
                .build());
        taskEsRepository.syncWithDB(storedTaskInstance.getTaskInstanceId(),false);
    }

    @Override
    @MonitoredFunction
    public StoredTaskInstance markExpired(StoredTaskInstance storedTaskInstance,
                                          TaskExpireRequest request) {
        StoredTaskTransition availableMeta = taskTransitionRepository.save(
                TaskInstanceTransformationUtils.toMarkExpiredTransition(storedTaskInstance, request)
        );
        taskEsRepository.syncWithDB(storedTaskInstance.getTaskInstanceId(),false);
        return availableMeta.getTaskInstance();
    }

    @Override
    @MonitoredFunction
    public StoredTaskInstance markDeleted(StoredTaskInstance storedTaskInstance,
                                          ClientTaskDeleteRequest request) {
        StoredTaskTransition deletedMeta = taskTransitionRepository.save(
                        TaskInstanceTransformationUtils.toMarkDeletedTransition(storedTaskInstance, request)
                );
        taskEsRepository.syncWithDB(storedTaskInstance.getTaskInstanceId(), false);
        return deletedMeta.getTaskInstance();
    }

    @Override
    public StoredTaskInstance createAndAssignTask(TaskCreateRequest taskCreateRequest, String actor) {
        StoredTaskInstance createdTask = taskManagementService.command(taskCreateRequest, taskCreateRequest.getTaskInstance().getCreatedBy());
        TaskCommandRequest taskAssignRequest = TaskAssignRequest.builder()
                .assignedTo(actor)
                .taskInstanceId(createdTask.getTaskInstanceId())
                .build();
        taskManagementService.command(taskAssignRequest, actor);
        return taskInstanceRepository.get(createdTask.getTaskInstanceId())
                .orElseThrow(() -> LegionException.error(NOT_FOUND));
    }

    @Override
    public boolean rescheduleTask(TaskSchedulingPayload taskSchedulingPayload, String actor) {
        Optional<StoredTaskInstance> taskInstance = taskInstanceRepository.get(taskSchedulingPayload.getTaskInstanceId());
        if (taskInstance.isPresent()) {
            DiscoveryTaskInstance discoveryTaskInstance = taskEsRepository.get(taskSchedulingPayload.getTaskInstanceId());
            taskSchedulingPayload.getScheduleType().accept(new TaskSchedulingType.TaskSchedulingVisitor() {

                @Override
                public void visitScheduleVisitor() {
                    ValidationUtils.validateTaskScheduling(discoveryTaskInstance, taskSchedulingPayload.getRescheduleAt());
                }

                @Override
                public void visitCancellationVisitor() {
                    ValidationUtils.validateTaskScheduling(discoveryTaskInstance);
                }
            });

            Date reschedulingDate = new Date(null != taskSchedulingPayload.getRescheduleAt() ? taskSchedulingPayload.getRescheduleAt() : 0);
            Date dueDate = taskInstance.get().getDueDate();
            taskInstanceRepository.update(taskSchedulingPayload.getTaskInstanceId(), instance -> {
                instance.setRescheduledAt(reschedulingDate);
                instance.setUpdatedBy(actor);
                instance.setDueDate(
                        reschedulingDate.after(dueDate)
                                ? DateUtils.addDaysToDate(reschedulingDate, miscellaneous.getRescheduleOffsetInDays())
                                : instance.getDueDate());
                return instance;
            });

            taskEsRepository.syncWithDB(taskSchedulingPayload.getTaskInstanceId(), false);
            foxtrotEventIngestionService.ingestTaskReschedulingEvent(taskSchedulingPayload);

            log.info("Task with taskInstanceId : {} has been rescheduled at: {}", taskSchedulingPayload.getTaskInstanceId(), reschedulingDate);
            return true;
        } else
            throw LegionException.error(LegionTaskErrorCode.INVALID_TASK_INSTANCE_ID);

    }

    @Override
    @MonitoredFunction
    public void updateTaskInstanceMeta(TaskMetaUpdateRequest request) {
        try {
            DiscoveryTaskInstance discoveryTaskInstance = taskEsRepository.get(request.getTaskInstanceId());
            taskInstanceRepository.update(request.getTaskInstanceId(), instance -> {
                instance.setInstanceMeta(toTaskInstanceMeta(request, instance.getInstanceMeta()));
                instance.setUpdatedBy(request.getCreatedBy());
                return setDueDateAndRescheduleDate(request, discoveryTaskInstance, instance, miscellaneous);
            });

            taskEsRepository.syncWithDB(request.getTaskInstanceId(), false);
            DiscoveryTaskInstance discoveryTaskInstanceUpdated = taskEsRepository.get(request.getTaskInstanceId());
            eventExecutor.ingest(toTaskMetaUpdateEvent(request, discoveryTaskInstanceUpdated));
            log.info("task meta data: {} has been updated for taskInstanceId: {}",
                    request.getUserTaskCreationMeta(),
                    request.getTaskInstanceId());
        } catch (LegionException e) {
            log.error("task meta update failed for taskInstanceId: {}", request.getTaskInstanceId(), e);
            throw LegionException.error(TASK_META_UPDATE_FAILED);
        }
    }

    private void selfAssignTask(StoredTaskInstance storedTaskInstance, String actorId) {
        selfAssign(storedTaskInstance, TaskAssignRequest.builder()
                .taskInstanceId(storedTaskInstance.getTaskInstanceId())
                .assignedTo(actorId)
                .build());
    }
    private void markTaskStarted(Optional<StoredTaskInstance> optionalStoredTaskInstance, String actorId) {
        optionalStoredTaskInstance.ifPresent(instance-> taskManagementService.command((TaskStartRequest.builder()
                .taskInstanceId(instance.getTaskInstanceId())
                .startedBy(actorId)
                .isForced(true)
                .build()), actorId));
    }

    private void markTaskCompleted(Optional<StoredTaskInstance> optionalStoredTaskInstance, String actorId) {

        optionalStoredTaskInstance.ifPresent(instance -> taskManagementService.command(TaskCompleteRequest.builder()
                .completedBy(actorId)
                .taskInstanceId(instance.getTaskInstanceId())
                .isForced(true)
                .build(), actorId)
        );
    }

    private void forceMarkTaskComplete(Optional<StoredTaskInstance> optionalStoredTaskInstance, String actorId){

        if (optionalStoredTaskInstance.isPresent()) {
            StoredTaskInstance storedTaskInstance = optionalStoredTaskInstance.get();
            ValidationUtils.validateForceCompletionRequest(storedTaskInstance, actorId, legionService);
            String taskInstanceId = storedTaskInstance.getTaskInstanceId();

            switch (storedTaskInstance.getCurState()) {
                case AVAILABLE:
                    selfAssignTask(storedTaskInstance, actorId);
                    optionalStoredTaskInstance = taskInstanceRepository.get(taskInstanceId);
                    markTaskStarted(optionalStoredTaskInstance, actorId);
                    break;
                case SELF_ASSIGNED,
                        BOUNDED_ASSIGNED:
                    markTaskStarted(optionalStoredTaskInstance, actorId);
                    break;
                default:
                    break;
            }
            optionalStoredTaskInstance = taskInstanceRepository.get(taskInstanceId);
            markTaskCompleted(optionalStoredTaskInstance, actorId);
            eventExecutor.ingest(toForceTaskCompletionEvent(storedTaskInstance));
            return;
        }
        throw LegionException.error(LegionTaskErrorCode.INVALID_TASK_INSTANCE_ID);
    }

    @Override
    @MonitoredFunction
    public void completeTaskById(TaskCompletionByIdRequest taskCompletionByIdRequest) {
        try {
            Optional<StoredTaskInstance> optionalStoredTaskInstance = taskInstanceRepository.get(taskCompletionByIdRequest.getTaskInstanceId());
            forceMarkTaskComplete(optionalStoredTaskInstance, taskCompletionByIdRequest.getActorId());
        }
        catch (LegionException e){
            throw e;
        }
        catch (Exception e) {
            throw LegionException.error(CoreErrorCode.INTERNAL_ERROR);
        }
    }

    @Override
    @MonitoredFunction
    public void completeAllTasksByType(TaskCompletionByTaskTypeRequest taskCompletionByTaskTypeRequest) {
        validationService.validateEntity(taskCompletionByTaskTypeRequest.getEntityId(), taskCompletionByTaskTypeRequest.getEntityType());
        try {
            BoolQueryBuilder boolQueryBuilder = taskTypeFilterQuery.taskTypeToFilterQuery(taskCompletionByTaskTypeRequest.getTaskType());

            if (boolQueryBuilder != null) {
                TaskSearchResponse tasks = taskDiscoveryService.getAllActiveTasksForCompletion(EntityTaskListingRequest.builder()
                        .pageNo(INITIAL_PAGE_NO)
                        .pageSize(MAXIMUM_PAGE_SIZE)
                        .entityId(taskCompletionByTaskTypeRequest.getEntityId()).build(), boolQueryBuilder);

                        tasks.getTaskList().stream()
                        .map(TaskMetaResponse::getTaskInstanceId)
                        .map(taskInstanceRepository::get)
                        .forEach(storedTaskInstance -> forceMarkTaskComplete(
                                storedTaskInstance,
                                taskCompletionByTaskTypeRequest.getCompletedBy()
                        ));
            }
        } catch (Exception e) {
            throw LegionException.error(CoreErrorCode.INTERNAL_ERROR);
        }
    }


    @Override
    public String createCommentOnTask(String actor, String taskInstanceId, String content) {
          return commentsService.createComment(CreateCommentRequest.builder()
                    .taskInstanceId(taskInstanceId)
                    .content(content)
                    .build(), actor).getCommentId();
    }

}
