package com.phonepe.merchant.legion.tasks.flows;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskActionFetchByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskAssignRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskActionInstance;
import com.phonepe.merchant.gladius.models.tasks.utils.Miscellaneous;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.repository.impl.ChimeraRepositoryImpl;
import com.phonepe.merchant.legion.core.services.AtlasService;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.core.services.LegionServiceAdapter;
import com.phonepe.merchant.legion.core.services.OrgAwareValidationService;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode;
import com.phonepe.merchant.legion.tasks.flows.models.ActionsEligibleForSelfAssignTransition;
import com.phonepe.merchant.legion.tasks.flows.models.SelfAssignmentTransitionRules;
import com.phonepe.merchant.legion.tasks.flows.models.SelfOrderTaskActions;
import com.phonepe.merchant.legion.tasks.services.impl.TaskActionServiceImpl;
import com.phonepe.merchant.legion.tasks.utils.DistanceCalculatorUtils;
import io.appform.functionmetrics.MonitoredFunction;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.IS_SELF_ASSIGNED_ALLOWED_CHIMERA_KEY;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.SELF_ORDER_TASK_ACTIONS;
import static com.phonepe.merchant.legion.core.utils.CommonUtils.isNullOrEmpty;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.BAD_REQUEST;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.BUSINESS_UNIT_MISMATCH;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.TASK_SECTOR_NOT_ASSIGNED;


@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class Validations {

    private final LegionService legionService;
    private final LegionServiceAdapter legionServiceAdapter;
    private final AtlasService atlasService;
    private final ChimeraRepositoryImpl chimeraLiteRepository;
    private final TaskActionServiceImpl taskActionService;
    private final Miscellaneous miscellaneous;
    private final FoxtrotEventIngestionService foxtrotEventIngestionService;
    private final OrgAwareValidationService orgAwareValidationService;


    public void validateTaskAssignee(AgentProfile agentProfile) {
        if (agentProfile == null || !agentProfile.isActive()) {
            throw LegionException.error(BAD_REQUEST,
                    Map.of("message", "Task cannot be assigned to invalid agent id"));
        }

    }

    @MonitoredFunction
    public void validateTaskSectorWithUser(String agentId, List<String> taskSectors) {
        AgentProfile agentProfile = legionService.getAgentProfile(agentId);

        validateTaskAssignee(agentProfile);

        if (!isNullOrEmpty(taskSectors) && !isAssignable(agentId, taskSectors)) {
            throw LegionException.error(TASK_SECTOR_NOT_ASSIGNED);
        }
    }

    @MonitoredFunction
    public void validateTaskSectorAndBuWithUser(TaskAssignRequest taskAssignRequest, DiscoveryTaskInstance discoveryTaskInstance,
                                                SelfAssignmentTransitionRules rules) {
        String agentId = taskAssignRequest.getAssignedTo();
        EsLocationRequest taskLocation = discoveryTaskInstance.getLocation();
        Set<String> tags = discoveryTaskInstance.getTags();
        AgentProfile agentProfile = legionServiceAdapter.getAgentProfile(agentId);

        // Use org-aware business unit and role validation
        if (!isNullOrEmpty(tags)) {
            String effectiveBusinessUnit = getEffectiveBusinessUnit(agentProfile, agentId);
            String effectiveRole = getEffectiveRole(agentProfile, agentId);

            if (!(tags.contains(effectiveBusinessUnit) || tags.contains(effectiveRole))) {
                throw LegionException.error(BUSINESS_UNIT_MISMATCH);
            }
        }

        validateTaskAssignee(agentProfile);
        List<String> taskSectors = atlasService.getSectorIds(taskLocation);

        if (isLocationValidationRequired(rules)) {
            validateSelfAssignDistance(taskAssignRequest, discoveryTaskInstance);
            return;
        }
        if (!isNullOrEmpty(taskSectors) && !isAssignable(agentId, taskSectors)) {
            throw LegionException.error(TASK_SECTOR_NOT_ASSIGNED);
        }
    }

    private void validateSelfAssignDistance(TaskAssignRequest taskAssignRequest, DiscoveryTaskInstance discoveryTaskInstance) {
        EsLocationRequest actorLocation = taskAssignRequest.getActorCurrentLocation();
        EsLocationRequest taskLocation = discoveryTaskInstance.getLocation();
        double distance = DistanceCalculatorUtils.getDistanceFromLocationInKiloMeters(actorLocation.getLat(), actorLocation.getLon(),
                taskLocation.getLat(), taskLocation.getLon());
        if (distance > miscellaneous.getMaxAllowedDistanceForSelfAssign()) {
            throw LegionException.error(LegionTaskErrorCode.DISTANCE_TOO_FAR_TO_SELF_ASSIGN);
        }
    }

    public SelfAssignmentTransitionRules getSelfAssignmentRules(String actionId) {
        return Optional.ofNullable(
                        chimeraLiteRepository.getChimeraConfig(
                                IS_SELF_ASSIGNED_ALLOWED_CHIMERA_KEY,
                                ActionsEligibleForSelfAssignTransition.class
                        )
                ).map(ActionsEligibleForSelfAssignTransition::getEligibleActionsForSelfAssignment)
                .map(actionsMap -> actionsMap.get(actionId))
                .orElse(null);
    }

    public boolean isActionEligibleForSelfAssignment(LegionTaskStateMachineState state, SelfAssignmentTransitionRules rules) {
        return rules != null && rules.accept(state);
    }

    public boolean isLocationValidationRequired(SelfAssignmentTransitionRules rules) {
        return rules != null && rules.isLocationValidationRequired();
    }

    public void validateSelfAssignmentLimit(DiscoveryTaskInstance taskInstance, String userId, int assignedTaskCount) {
        TaskActionInstance taskActionInstance = taskActionService.getFromCache(TaskActionFetchByIdRequest.builder()
                .taskActionId(taskInstance.getActionId())
                .build());
        if (taskActionInstance != null && taskActionInstance.getMetaData() != null) {
            int selfAssignmentLimit = taskActionInstance.getMetaData().getActionLevelSelfAssignmentLimit();
            if (assignedTaskCount >= selfAssignmentLimit) {
                foxtrotEventIngestionService.ingestSelfAssignmentLimitBreached(taskInstance.getTaskInstanceId(), taskInstance.getActionId(),
                        userId, selfAssignmentLimit, assignedTaskCount);
                throw LegionException.error(LegionTaskErrorCode.SELF_ASSIGN_LIMIT_BREACHED);
            }
        }
    }

    public boolean isAssignable(String agentId, List<String> taskSectors) {
        for (String sector : taskSectors) {
            if (legionServiceAdapter.isSectorAccessibleBoolean(sector, agentId)) {
                return true;
            }
        }
        return false;
    }

    public boolean isSelfOrderTaskAction(String actionId) {
        SelfOrderTaskActions selfOrderTaskActions = chimeraLiteRepository.getChimeraConfig(SELF_ORDER_TASK_ACTIONS,
                SelfOrderTaskActions.class);
        return selfOrderTaskActions.getActionIds().contains(actionId);
    }

    /**
     * Get effective business unit - prioritizes org-level data if available
     */
    private String getEffectiveBusinessUnit(AgentProfile agentProfile, String agentId) {
        try {
            // Try to get org-level business unit
            var agentProfileV2 = legionServiceAdapter.getAgentProfileV2(agentId);
            String orgBusinessUnit = agentProfileV2.getEffectiveBusinessUnit();
            if (orgBusinessUnit != null) {
                return orgBusinessUnit;
            }
        } catch (Exception e) {
            // Fall back to agent-level business unit
        }
        return agentProfile.getBusinessUnit() != null ? agentProfile.getBusinessUnit().name() : null;
    }

    /**
     * Get effective role - prioritizes org-level data if available
     */
    private String getEffectiveRole(AgentProfile agentProfile, String agentId) {
        try {
            // Try to get org-level role
            var agentProfileV2 = legionServiceAdapter.getAgentProfileV2(agentId);
            String orgRole = agentProfileV2.getEffectiveRole();
            if (orgRole != null) {
                return orgRole;
            }
        } catch (Exception e) {
            // Fall back to agent-level role
        }
        return agentProfile.getAgentType() != null ? agentProfile.getAgentType().name() : null;
    }
}
