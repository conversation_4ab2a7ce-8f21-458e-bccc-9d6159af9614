package com.phonepe.merchant.legion.tasks.utils;


import com.phonepe.discovery.models.core.request.query.filter.Filter;
import com.phonepe.discovery.models.core.request.query.filter.general.InFilter;
import com.phonepe.discovery.models.core.request.query.filter.number.GreaterEqualsNumberFilter;
import com.phonepe.discovery.models.core.request.query.filter.number.LesserEqualsNumberFilter;
import com.phonepe.discovery.models.core.request.query.filter.predicate.ORFilter;
import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.enums.AttributeType;
import com.phonepe.merchant.gladius.models.tasks.enums.Sorter;
import com.phonepe.merchant.gladius.models.tasks.filters.StatFilter;
import com.phonepe.merchant.gladius.models.tasks.response.EntityStatsResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaAttribute;
import com.phonepe.merchant.gladius.models.tasks.response.TaskStateStatsByAction;
import com.phonepe.merchant.gladius.models.tasks.response.UserStatsAndFilters;
import com.phonepe.merchant.gladius.models.tasks.utils.TaskUtils;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.query.EsFilterBuilder;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode;
import com.phonepe.merchant.legion.tasks.search.filters.TaskStateWiseDurationVisitor;
import com.phonepe.merchant.legion.tasks.search.query.SectorGeofenceQueryBuilder;
import com.phonepe.models.merchants.tasks.EntityType;
import edu.emory.mathcs.backport.java.util.Collections;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.common.geo.GeoPoint;
import org.elasticsearch.common.unit.DistanceUnit;
import org.elasticsearch.geometry.LinearRing;
import org.elasticsearch.geometry.Polygon;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.GeoBoundingBoxQueryBuilder;
import org.elasticsearch.index.query.GeoPolygonQueryBuilder;
import org.elasticsearch.index.query.GeoShapeQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermsQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedLongTerms;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.metrics.Sum;
import org.elasticsearch.search.aggregations.metrics.ValueCount;
import org.elasticsearch.search.sort.GeoDistanceSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.joda.time.LocalDate;

import java.io.IOException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.AVAILABLE;
import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.BOUNDED_ASSIGNED;
import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.COMPLETED;
import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.SELF_ASSIGNED;
import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.STARTED;
import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.VERIFICATION_FAILED;
import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.VERIFICATION_SUCCESS;
import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ATTRIBUTES;
import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.DUE_DATE;
import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.LOCATION;
import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.POLYGON_IDS;
import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ROLES_NOT_ALLOWED_FIELD;
import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ROLES_NOT_ALLOWED_FIELD_KEYWORD;
import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.TASK_STATE;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.POINTS_UNIT;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.TASKS_UNIT;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.ACTIONID;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.CURRENT;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.END;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.LEAD_INTENT_AGGREGATION;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.LEAD_INTENT_FIELD;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.START;

public class TaskEsUtils {

    private static final String POINTS = "points";
    public static final String STATUS = "status";
    private static final String STATUS_COUNT = "status_count";
    private static final String ACTION_AGGREGATION = "action_aggs";
    public static final String ENTITY_TYPE = "entityType";
    private static final int MAX_FILTERS_FETCHED = 100;
    public static final String OBJECTIVES_KEYWORD = "attributes.objective";
    private static final String STATE_AGGREGATION_COUNT = "state_aggregation_count";
    private static final List<LegionTaskStateMachineState> taskStatesForStats = Arrays.asList(SELF_ASSIGNED, BOUNDED_ASSIGNED, STARTED, COMPLETED, AVAILABLE);
    private static  SectorGeofenceQueryBuilder sectorGeofenceQueryBuilder;

    public static void init(SectorGeofenceQueryBuilder sectorGeofenceQueryBuilder) {
        TaskEsUtils.sectorGeofenceQueryBuilder = sectorGeofenceQueryBuilder;
    }

    public static void addGeoDistanceFilter(BoolQueryBuilder query, EsLocationRequest location, double maxGeoSortDistance) {
        query.filter(QueryBuilders.geoDistanceQuery(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.LOCATION)
                .point(location.getLat(), location.getLon())
                .distance(maxGeoSortDistance, DistanceUnit.KILOMETERS));
    }

    private static BoolQueryBuilder applyTagDoesNotExistsFilter() {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        return boolQueryBuilder.mustNot(QueryBuilders.existsQuery("tags"));
    }

    private static BoolQueryBuilder applyAttributeDoesNotExistsFilter(){
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        return boolQueryBuilder.mustNot(QueryBuilders.existsQuery(ROLES_NOT_ALLOWED_FIELD));
    }

    private static BoolQueryBuilder excludeDisabledAttributeFiler(AttributeType attributeType){
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        return boolQueryBuilder.mustNot(QueryBuilders.termsQuery("attributes." + attributeType.getName() + ".keyword", attributeType.getName().toUpperCase()));
    }

    private static BoolQueryBuilder applyAllowedActionsFilter(List<String> allowedActions){
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        return boolQueryBuilder.must(QueryBuilders.termsQuery(ACTIONID, allowedActions));
    }

    private static TermsQueryBuilder getTagTermQuery(Set<String> tags) {
        return QueryBuilders.termsQuery("tags", tags);
    }

    private static BoolQueryBuilder getRoleBasedTermQuery(String role) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        return boolQueryBuilder.mustNot(QueryBuilders.termsQuery(ROLES_NOT_ALLOWED_FIELD_KEYWORD, role));
    }

    public static BoolQueryBuilder getTagFilter(Set<String> tags) {
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        queryBuilder.should(applyTagDoesNotExistsFilter());
        queryBuilder.should(getTagTermQuery(tags));
        return queryBuilder;
    }

    public static BoolQueryBuilder getRoleFilter(String role) {
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        queryBuilder.should(applyAttributeDoesNotExistsFilter());
        queryBuilder.should(getRoleBasedTermQuery(role));
        return queryBuilder;
    }

    public static BoolQueryBuilder  getDisabledAttributeFilter(AttributeType attributeType) {
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        queryBuilder.should(excludeDisabledAttributeFiler(attributeType));
        return queryBuilder;
    }

    public static BoolQueryBuilder getAllowedActionsFilter(List<String> allowedActions) {
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        queryBuilder.should(applyAllowedActionsFilter(allowedActions));
        return queryBuilder;
    }

    public static BoolQueryBuilder getBoolQuery(List<Filter> filters) {
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        if (filters != null) {
            EsFilterBuilder esFilterBuilder = new EsFilterBuilder();
            filters.stream().filter(filter -> !POLYGON_IDS.equals(filter.getFieldName()))
                    .forEach(filter -> query.must(filter.accept(esFilterBuilder)));
            filters.stream().filter(filter -> POLYGON_IDS.equals(filter.getFieldName())).forEach(filter -> {
                QueryBuilder q = filter.accept(sectorGeofenceQueryBuilder);
                if (q == null) {
                    throw LegionException.error(LegionTaskErrorCode.BAD_REQUEST, Map.of("message", "Invalid sector filter provided"));
                }
                query.must(q);
            });
        }
        return query;
    }

    public static List<DiscoveryTaskInstance> getTasksFromSearchHits(Iterator iterator) {
        List<DiscoveryTaskInstance> elasticSearchRequests = new ArrayList();

        while(iterator.hasNext()) {
            SearchHit searchHit = (SearchHit) iterator.next();
            DiscoveryTaskInstance elasticSearchRequest = SerDe.readValue(searchHit.getSourceAsString(), DiscoveryTaskInstance.class);
            if(elasticSearchRequest != null) {
               elasticSearchRequests.add(elasticSearchRequest);
            }
        }
        return elasticSearchRequests;
    }

    public static GeoDistanceSortBuilder getGeoSort(EsLocationRequest location) {
        if(location == null) {
            location = EsLocationRequest.builder().lat(0.0).lon(0.0).build();
        }
        return SortBuilders.geoDistanceSort(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.LOCATION, location.getLat(), location.getLon())
                .order(SortOrder.ASC)
                .unit(DistanceUnit.KILOMETERS);
    }

    public static AggregationBuilder getUserStatsAggregation() {
        return AggregationBuilders.terms(STATUS)
                .executionHint("map")
                .field(TASK_STATE)
                .subAggregation(AggregationBuilders.count(STATUS_COUNT)
                        .field(TASK_STATE))
                .subAggregation(AggregationBuilders.sum(POINTS)
                        .field(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.POINTS));
    }

    public static AggregationBuilder getEntityStatsAggregation() {
        return AggregationBuilders.terms(ENTITY_TYPE)
                .executionHint("map")
                .field(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ENTITY_TYPE)
                .subAggregation(AggregationBuilders.count(STATUS_COUNT)
                        .field(TASK_STATE))
                .subAggregation(AggregationBuilders.sum(POINTS)
                        .field(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.POINTS));
    }

    public static StatFilter getTaskStateStatFilter(String displayText, List<LegionTaskStateMachineState> states, Map<LegionTaskStateMachineState, Long> map) {
        List<String> stringStates = new ArrayList<>();
        long count = 0;
        for (LegionTaskStateMachineState state : states) {
            stringStates.add(state.getText());
            count += map.getOrDefault(state, 0l);
        }
        return StatFilter.builder()
                .displayText(displayText)
                .fieldName(TASK_STATE)
                .keys(stringStates)
                .value(count)
                .clickable(true)
                .build();
    }

    public static UserStatsAndFilters getUserStatsFromAgg(Aggregations aggregations) {
        Map<LegionTaskStateMachineState, Long> map = new EnumMap<>(LegionTaskStateMachineState.class);
        Terms entityIdTerms = (Terms) aggregations.getAsMap().get(STATUS);
        Double points = 0.0;
        for (Terms.Bucket bucket : entityIdTerms.getBuckets()) {
            LegionTaskStateMachineState state = LegionTaskStateMachineState.valueOf(bucket.getKeyAsString());
            map.put(state, ((ValueCount) bucket.getAggregations().getAsMap().get(STATUS_COUNT)).getValue());
            points += ((Sum) bucket.getAggregations().getAsMap().get(POINTS)).getValue();
        }

        List<StatFilter> stats = new ArrayList<>();
        stats.add(getTaskStateStatFilter("Approved", java.util.Arrays.asList(VERIFICATION_SUCCESS), map));
        stats.add(getTaskStateStatFilter("In Review", java.util.Arrays.asList(COMPLETED), map));
        stats.add(getTaskStateStatFilter("Rejected", java.util.Arrays.asList(VERIFICATION_FAILED), map));
        stats.add(getTaskStateStatFilter("Pending", java.util.Arrays.asList(BOUNDED_ASSIGNED, SELF_ASSIGNED, STARTED), map));
        stats.add(StatFilter.builder()
                .displayText("Points")
                .value(points)
                .clickable(false)
                .build());

        return UserStatsAndFilters.builder()
                .stats(stats)
                .build();
    }

    public static EntityStatsResponse getEntityStatsFromAgg(Aggregations aggregations) {
        Terms entityIdTerms = (Terms) aggregations.getAsMap().get(ENTITY_TYPE);
        Double availablePoints = 0.0;
        Long availableTaskCount = 0l;
        EntityType entityType = null;
        for (Terms.Bucket bucket : entityIdTerms.getBuckets()) {
            entityType = EntityType.valueOf(bucket.getKeyAsString());
            availableTaskCount = ((ValueCount) bucket.getAggregations().getAsMap().get(STATUS_COUNT)).getValue();
            availablePoints = ((Sum) bucket.getAggregations().getAsMap().get(POINTS)).getValue();
        }
        List<TaskMetaAttribute> attributes = new ArrayList<>();
        attributes.add(TaskMetaAttribute.builder()
                .displayText(TASKS_UNIT)
                .value(availableTaskCount)
                .build());
        attributes.add(TaskMetaAttribute.builder()
                .displayText(POINTS_UNIT)
                .value(availablePoints)
                .build());
        return EntityStatsResponse.builder()
                .tasksPresent(availableTaskCount != 0)
                .attributes(attributes)
                .entityType(entityType)
                .build();
    }

    public static Filter getCurrentMonthFilter() {
        long currentMonthEpoch = LocalDate.now().minusMonths(1).toDateTimeAtCurrentTime().getMillis();
        return new GreaterEqualsNumberFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.UPDATED_AT, currentMonthEpoch);
    }

    public static Filter getLast60DaysFilter(String dateFieldName) {
        long currentMonthEpoch = LocalDate.now().minusDays(60).toDateTimeAtStartOfDay().getMillis();
        return new GreaterEqualsNumberFilter(dateFieldName, currentMonthEpoch);
    }

    public static Filter getPrevMonthsFilter(String dateFieldName, int noOfMonths) {
        long prevMonthEpoch = LocalDate.now().minusMonths(noOfMonths).withDayOfMonth(1).toDateTimeAtStartOfDay().getMillis();
        return new GreaterEqualsNumberFilter(dateFieldName, prevMonthEpoch);
    }

    public static Filter getDueDateFilter() {
        long currentEpoch = Instant.now().toEpochMilli();
        return new GreaterEqualsNumberFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.DUE_DATE, currentEpoch);
    }

    public static Filter getDueDateFilter(Instant instant) {
        long currentEpoch = Objects.nonNull(instant) ? instant.toEpochMilli() : Instant.now().toEpochMilli();
        return new GreaterEqualsNumberFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.DUE_DATE, currentEpoch);
    }

    public static Filter getStartDateFilter() {
        long currentEpoch = Instant.now().toEpochMilli();
        return new LesserEqualsNumberFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.START_DATE, currentEpoch);
    }

    public static Filter getStartDateFilter(Instant instant) {
        long currentEpoch = Objects.nonNull(instant) ? instant.toEpochMilli() : Instant.now().toEpochMilli();
        return new LesserEqualsNumberFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.START_DATE, currentEpoch);
    }

    public static Filter getLastThreeMonthsFilter(String dateFieldName) {
        long prevSixMonthEpoch = LocalDate.now().minusMonths(3).toDateTimeAtStartOfDay().getMillis();
        return new GreaterEqualsNumberFilter(dateFieldName, prevSixMonthEpoch);
    }

    public static Filter getUpdatedAtFilter(long updatedAt) {
        return new GreaterEqualsNumberFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.UPDATED_AT, updatedAt);
    }
    public static Filter getTaskStatesFilter(){
        return new InFilter<>(TASK_STATE, taskStatesForStats.stream().map(String::valueOf).collect(Collectors.toList()));
    }


    public static Filter getTaskStatusFilter(List<LegionTaskStateMachineState> states) {
        List<Filter> filters = new ArrayList<>();
        states.forEach(state -> filters.add(getDurationQuery(state)));
        return new ORFilter(filters);
    }

    public static Filter getTaskStatusTermsFilter(List<String> states) {
        List<Filter> filters = new ArrayList<>();
        filters.add(new InFilter<>(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.TASK_STATE, states));
        return new ORFilter(filters);
    }

    private static Filter getDurationQuery(LegionTaskStateMachineState state) {
        return state.accept(state, new TaskStateWiseDurationVisitor());
    }

    private static Filter getTaskStateQuery(LegionTaskStateMachineState state) {
        return state.accept(state, new TaskStateWiseDurationVisitor());
    }

    public static boolean validatePageNumberAndSize(long taskCount, int pageNumber, int pageSize) {
        if (taskCount == 0) {
            return pageNumber <= 1;
        } else if (taskCount % pageSize == 0) {
            return pageNumber <= taskCount / pageSize;
        } else {
            return pageNumber <= (taskCount / pageSize + 1);
        }
    }

    private static long getTaskCount(SearchResponse searchResponse) {
        long taskCount = 0;
        TotalHits totalHits = searchResponse.getHits().getTotalHits();
        if (totalHits != null) {
            taskCount = totalHits.value;
        }
        return taskCount;
    }

    public static List<DiscoveryTaskInstance> toDiscoveryTaskInstanceList(int pageNo, int pageSize, EsLocationRequest esLocationRequest, SearchResponse searchResponse, Sorter sorter) {
        long taskCount = getTaskCount(searchResponse);
        if (!validatePageNumberAndSize(taskCount, pageNo, pageSize)) {
            throw LegionException.error(LegionTaskErrorCode.BAD_REQUEST,
                    Map.of("message", "Invalid page size and page number combination"));
        }
        SearchHit[] hits = searchResponse.getHits().getHits();
        List<DiscoveryTaskInstance> tasks = new ArrayList<>();
        for (SearchHit hit : hits) {
            DiscoveryTaskInstance task = SerDe.readValue(hit.getSourceAsString(), DiscoveryTaskInstance.class);
            if(sorter == Sorter.LOCATION && esLocationRequest != null && task.getLocation() != null) {
                Double distance = DistanceCalculatorUtils.getDistanceFromLatLonInMeters(esLocationRequest.getLat(), esLocationRequest.getLon(), task.getLocation().getLat(), task.getLocation().getLon())/1000.000;
                task.setDistance(TaskUtils.setPrecision(distance, 2));
            }
            tasks.add(task);
        }
        return tasks;
    }

    public static Map<String, Long> getThisMonthEpochTime() {
        Map<String, Long> thisMonthStartEnd = new HashMap<>();
        Calendar calendar = Calendar.getInstance();
        thisMonthStartEnd.put(CURRENT, calendar.getTimeInMillis());
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), 1, 0, 0, 0);
        thisMonthStartEnd.put(START, calendar.getTimeInMillis());
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DATE), 23, 59, 59);
        thisMonthStartEnd.put(END, calendar.getTimeInMillis());
        return thisMonthStartEnd;
    }

    public static AggregationBuilder getTaskActionAggregation() {
        return AggregationBuilders.terms(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ACTION_ID)
                .executionHint("map")
                .field(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ACTION_ID)
                .size(MAX_FILTERS_FETCHED);
    }

    public static AggregationBuilder getTaskObjectiveAggregation() {
        return AggregationBuilders.terms(ATTRIBUTES)
                .executionHint("map")
                .field(OBJECTIVES_KEYWORD)
                .size(MAX_FILTERS_FETCHED);
    }

    public static AggregationBuilder getLeadIntentAggregation() {
            return AggregationBuilders.terms(LEAD_INTENT_AGGREGATION)
                    .executionHint("map")
                    .field(LEAD_INTENT_FIELD)
                    .size(MAX_FILTERS_FETCHED);

    }

    public static List<String> getFilterValuesFromAggregation(ParsedStringTerms parsedStringTerms) {
        List<String> taskFiltersList = new ArrayList<>();
        if (null != parsedStringTerms) {
            parsedStringTerms.getBuckets().forEach(bucket -> taskFiltersList.add(bucket.getKeyAsString().toUpperCase()));
        }
        return taskFiltersList;
    }

    public static List<String> getLeadFilterFromAggregation(ParsedStringTerms parsedStringTerms) {
        if (parsedStringTerms != null) {
            return parsedStringTerms.getBuckets().stream()
                    .map(Terms.Bucket::getKeyAsString)
                    .toList();
        }
        return Collections.emptyList();
    }

    public static AggregationBuilder getPointAggregation() {
        return AggregationBuilders.terms(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.POINTS)
                .executionHint("map")
                .field(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.POINTS)
                .size(MAX_FILTERS_FETCHED);
    }

    public static List<String> getActionFilterFromMultiAgg(ParsedStringTerms parsedStringTerms) {
        if (Objects.isNull(parsedStringTerms)) {
            return Collections.emptyList();
        }
        return parsedStringTerms.getBuckets().stream().map(Terms.Bucket::getKeyAsString).toList();
    }

    public static List<Long> getPointFilterFromMultiAgg(ParsedLongTerms parsedLongTerms) {
        if (Objects.isNull(parsedLongTerms)) {
            return Collections.emptyList();
        }
        return parsedLongTerms.getBuckets().stream().map(bucket -> bucket.getKeyAsNumber().longValue()).toList();
    }

    /****
     * @deprecated use
     */
    @Deprecated
    @SuppressWarnings("java:S6355")
    public static BoolQueryBuilder getGeoJsonQueryForTasks(List<List<Double>> coordinates) {
        GeoPolygonQueryBuilder geoPolygonQuery = TaskEsUtils.geoPolygonQuery(coordinates);
        BoolQueryBuilder query = TaskEsUtils.getDueDataQuery();
        return query.must(geoPolygonQuery);
    }

    /****
     * @deprecated use
     */
    @Deprecated
    @SuppressWarnings("java:S6355")
    public static GeoPolygonQueryBuilder geoPolygonQuery(List<List<Double>> coordinates) {
        List<GeoPoint> geoPoints = coordinates.stream().map(coordinate -> {
            Double longitude = coordinate.get(0);
            Double latitude = coordinate.get(1);
            return new GeoPoint(latitude, longitude);
        }).toList();
        return QueryBuilders.geoPolygonQuery(LOCATION, geoPoints);
    }

    public static GeoShapeQueryBuilder geoShapePolygonQuery(List<List<Double>> coordinates) throws IOException {
        return QueryBuilders.geoShapeQuery(LOCATION, getPolygonFromCoordinates(coordinates));
    }

    public static Polygon getPolygonFromCoordinates(List<List<Double>> coordinates) {
        int size = coordinates.size();
        double[] x = new double[size];
        double[] y = new double[size];

        for (int i = 0; i < size; i++) {
            List<Double> coord = coordinates.get(i);
            x[i] = coord.get(0); // longitude
            y[i] = coord.get(1); // latitude
        }
        LinearRing shell = new LinearRing(x, y);
        return new Polygon(shell);
    }

    public static GeoBoundingBoxQueryBuilder getGeoBoundingBoxQuery(List<List<Double>> coordinates) {
        if (coordinates == null || coordinates.isEmpty()) {
            return null;
        }
        double minLon = coordinates.stream().mapToDouble(coord -> coord.get(0)).min().orElseThrow();
        double maxLon = coordinates.stream().mapToDouble(coord -> coord.get(0)).max().orElseThrow();
        double minLat = coordinates.stream().mapToDouble(coord -> coord.get(1)).min().orElseThrow();
        double maxLat = coordinates.stream().mapToDouble(coord -> coord.get(1)).max().orElseThrow();

        return QueryBuilders.geoBoundingBoxQuery(LOCATION)
                .setCorners(new GeoPoint(maxLat, minLon), new GeoPoint(minLat, maxLon));
    }

    private static BoolQueryBuilder getDueDataQuery() {
        List<Filter> filters = new ArrayList<>();
        filters.add(new GreaterEqualsNumberFilter(DUE_DATE, System.currentTimeMillis()));
        return getBoolQuery(filters);
    }

    public static AggregationBuilder getActionTaskStatusAgg() {
        return AggregationBuilders.terms(ACTION_AGGREGATION)
                .executionHint("map")
                .field(ACTIONID)
                .size(1000)
                .subAggregation(AggregationBuilders.terms(STATE_AGGREGATION_COUNT)
                        .executionHint("map")
                        .field(TASK_STATE));
    }


    public static List<TaskStateStatsByAction> getTasksCountFromAgg(Aggregations aggregations) {
        List<TaskStateStatsByAction> taskStateStatsByActionList = new ArrayList<>();
        Terms entityIdTerms = (Terms) aggregations.getAsMap().get(ACTION_AGGREGATION);
        if (entityIdTerms == null) {
            return taskStateStatsByActionList;
        }
        return entityIdTerms.getBuckets().stream()
                .map(bucket -> {
                    TaskStateStatsByAction taskStateStatsByAction = new TaskStateStatsByAction();
                    taskStateStatsByAction.setActionId(bucket.getKeyAsString());
                    taskStateStatsByAction.setTotalCount(bucket.getDocCount());

                    Terms stateTerms = (Terms) bucket.getAggregations().getAsMap().get(STATE_AGGREGATION_COUNT);
                    Map<String, Long> availableTaskCount =
                            stateTerms.getBuckets().stream()
                                    .collect(Collectors.toMap(Terms.Bucket::getKeyAsString, Terms.Bucket::getDocCount));

                    taskStateStatsByAction.setTaskStateCountMap(availableTaskCount);
                    return taskStateStatsByAction;
                })
                .collect(Collectors.toList());
    }

}
