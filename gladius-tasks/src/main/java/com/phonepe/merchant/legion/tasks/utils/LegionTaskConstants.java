package com.phonepe.merchant.legion.tasks.utils;


public class LegionTaskConstants {

  private LegionTaskConstants() {}

  //ES related constants
  public static final String TASK_INSTANCE_ID = "taskInstanceId";
  public static final String TRANSITION_ID = "transitionId";
  public static final String CREATED_AT = "createdAt";
  public static final String CREATED_BY = "createdBy";
  public static final String CREATED_AT_ES = "created_at";

  //ES bulk update constants
  public static final int TIME_OUT = 7000;
  public static final int ES_CHUNK_SIZE = 800;
  public static final int NUM_THREADS = 20;

  //Front end related constants
  public static final String STEP_KEY_NAME = "step";

  //Search related constants
  public static final int MAX_PAGE_SIZE = 15;
  public static final int MAXIMUM_PAGE_SIZE = 100;
  public static final int INITIAL_PAGE_NO = 1;

  //Task ID related constants
  public static final String TASK_DEFINITION_ID_PREFIX = "TD";
  public static final String TASK_INSTANCE_ID_PREFIX = "TI";

  //General Constants
  public static final Long DAY_IN_MS = 1000L*60*60*24;
  public static final long HOUR_IN_MS = 1000L*60*60;
  public static final String MESSAGE = "message";
  public static final String SYSTEM = "SYSTEM"; // denotes system user
  public static final String START = "start";
  public static final String END = "end";
  public static final String CURRENT = "current";

  //Task Definition related constants
  public static final String ACTION_ID = "actionId";

  //Task Action related constants
  public static final String DUE_COLLECTED = "DUE_COLLECTED";
  public static final String DEVICE_REVERSED_PICKED_UP = "DEVICE_REVERSED_PICKED_UP";

  //Brickbat form filter related constant
  public static final String ALL_BU_ALL_AGENTS = "all_bu_all_agents";
  public static final String DETAILS = "details";

  public static final String TASK_DEFINITION_ID = "taskDefinitionId";
  public static final String ATTRIBUTE_VALUE = "attributeValue";
  public static final String ACTIVE = "active";

  public static final String PRIORITY_TASK_COUNT = "priorityTaskCount";
  public static final String NON_PRIORITY_TASK_COUNT = "nonPriorityTaskCount";

  public static final String OBJECTIVE = "Objective";

  public static final String OBJECTIVES = "objectives";
  public static final String TASK_DEFINITION_ID_DB = "task_definition_id";

  public static final String ACTIONID = "action_id";

  public static final String TASK_TYPE = "Task Type";
  public static final String KYC_REQUIRED_AND_COMPLETED_KEYWORD = "attributes.kyc_required_and_completed.keyword";
  public static final String KYC_REQUIRED_AND_COMPLETED = "KYC_REQUIRED_AND_COMPLETED";
  public static final String SECTOR_ID = "sectorId";
  public static final String STATUS = "status";
  public static final String HOTSPOT_TYPE = "hotspotType";
  public static final int MAX_RESULTS = 10000;
  public static final String STORED_HOTSPOT_ID_PREFIX = "HS";
  public static final String TOTAL_COUNT = "total_count";
  public static final String TOTAL_POINTS = "total_points";
  public static final int STORED_HOTSPOT_ID_LENGTH = 8;
  public static final String DISPLAY_NAME = "displayName";
  public static final String WHITELISTED_SECTORS = "whitelistedSectors";
  public static final String HOTSPOT_CONFIGS = "gladius_hotspots_filtercraft_config";
  public static final String HOTSPOT_CONFIG_DEFAULT_KEY = "HOTSPOT_CONFIG_DEFAULT_KEY";
  public static final String CUR_ACTOR = "curActor";
  public static final String CUR_STATE = "curState";




  public static final String DATE_FORMAT = "dd/MM/yyyy";

  // Restriction Treatment Group Constant
  public static final String DEFAULT_TREATMENT_GROUP = "default";
  public static final String HOTSPOT_VIEW_TREATMENT_GROUP = "hotspot_view";

  public static final String EXCLUSIVE_LENDING_TAG = "EXCLUSIVE_LENDING";
  public static final String DEFAULT_KEY = "DEFAULT_KEY";

  public static final String FEEDBACK_REQUEST_CONTEXT_FORM_TYPE = "formType";

  public static final String FEEDBACK_REQUEST_CONTEXT_ASSET_ID = "assetId";
  public static final String LEAD_INTENT_AGGREGATION = "lead_intent_agg";
  public static final String LEAD_INTENT_FIELD = "task_metadata.taskMetaAsMap.LEAD_INTENT.keyword";
  public static final String LEAD_INTENT = "leadIntent";

}
