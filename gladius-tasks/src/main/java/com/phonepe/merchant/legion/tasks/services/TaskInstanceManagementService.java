package com.phonepe.merchant.legion.tasks.services;

import com.phonepe.merchant.gladius.models.tasks.domain.TaskInstance;
import com.phonepe.merchant.gladius.models.tasks.request.ClientTaskDeleteRequest;
import com.phonepe.merchant.gladius.models.tasks.request.CreateTaskInstanceRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskCompletionByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskCompletionByTaskTypeRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskExpireRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskManualVerificationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskMetaUpdateRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskSchedulingPayload;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskAssignRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCompleteRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCreateRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskMarkAvailableRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskStartRequest;
import com.phonepe.merchant.gladius.models.tasks.response.Campaign;
import com.phonepe.merchant.gladius.models.tasks.response.TaskActionInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskDefinitionInstance;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskDefinition;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import io.appform.functionmetrics.MonitoredFunction;

import java.util.Map;
import java.util.Set;
import java.util.function.UnaryOperator;

public interface TaskInstanceManagementService {

    StoredTaskInstance create(TaskDefinitionInstance taskDefinitionInstance,
                              CreateTaskInstanceRequest createTaskInstanceRequest,
                              TaskActionInstance taskActionInstance,
                              Campaign campaign,
                              Set<String> storeTagList);

    Map<String, Set<String>> getTaskAttributes(StoredTaskDefinition storedTaskDefinition);

    StoredTaskInstance markAvailable(StoredTaskInstance storedTaskInstance, TaskMarkAvailableRequest request);

    void update(String taskInstanceId, UnaryOperator<StoredTaskInstance> updateOperation, boolean immediateRefresh);

    void delete(String taskInstanceId);

    TaskInstance getById(TaskByIdRequest taskByIdRequest);

    TaskInstance getById(String taskInstanceId);

    StoredTaskInstance boundedAssign(StoredTaskInstance storedTaskInstance, TaskAssignRequest taskAssignRequest);

    StoredTaskInstance selfAssign(StoredTaskInstance storedTaskInstance, TaskAssignRequest taskAssignRequest);

    void markCompleted(StoredTaskInstance storedTaskInstance, TaskCompleteRequest taskVerificationRequest);

    StoredTaskInstance markStarted(StoredTaskInstance storedTaskInstance, TaskStartRequest request);

    void markVerified(StoredTaskInstance storedTaskInstance, TaskManualVerificationRequest request);

    StoredTaskInstance markExpired(StoredTaskInstance storedTaskInstance, TaskExpireRequest request);

    @MonitoredFunction
    StoredTaskInstance markDeleted(StoredTaskInstance storedTaskInstance, ClientTaskDeleteRequest request);

    StoredTaskInstance createAndAssignTask(TaskCreateRequest taskCreateRequest, String actor);

    boolean rescheduleTask(TaskSchedulingPayload taskSchedulingPayload, String actor);

    void updateTaskInstanceMeta(TaskMetaUpdateRequest request);

    void completeTaskById(final TaskCompletionByIdRequest taskCompletionByIdRequest);

    void completeAllTasksByType(final TaskCompletionByTaskTypeRequest manualTaskCompletionRequest);

    String createCommentOnTask(String actor, String taskInstanceId, String content);

}
