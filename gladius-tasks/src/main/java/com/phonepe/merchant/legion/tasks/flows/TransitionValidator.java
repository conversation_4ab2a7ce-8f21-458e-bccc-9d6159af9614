package com.phonepe.merchant.legion.tasks.flows;

import com.google.inject.Inject;
import com.phonepe.discovery.models.core.request.query.filter.Filter;
import com.phonepe.discovery.models.core.request.query.filter.general.EqualsFilter;
import com.phonepe.discovery.models.core.request.query.filter.general.InFilter;
import com.phonepe.discovery.models.core.request.query.filter.number.GreaterEqualsNumberFilter;
import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.CreateTaskInstanceRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskAssignRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCompleteRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskStartRequest;
import com.phonepe.merchant.gladius.models.tasks.response.Campaign;
import com.phonepe.merchant.gladius.models.tasks.response.ExpiryPeriod;
import com.phonepe.merchant.gladius.models.tasks.response.TaskActionInstance;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.repository.ESRepository;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.core.utils.EsUtil;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.actions.processor.ActionValidationProcessor;
import com.phonepe.merchant.legion.tasks.actions.processor.ActionVerificationProcessor;
import com.phonepe.merchant.legion.tasks.annotations.AssignmentActionValidatorProcessor;
import com.phonepe.merchant.legion.tasks.annotations.CompletionActionValidationProcessor;
import com.phonepe.merchant.legion.tasks.annotations.StartActionValidationProcessor;
import com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode;
import com.phonepe.merchant.legion.tasks.flows.models.SelfAssignmentTransitionRules;
import com.phonepe.merchant.legion.tasks.utils.DistanceCalculatorUtils;
import com.phonepe.models.merchants.tasks.EntityType;
import io.appform.functionmetrics.MonitoredFunction;
import org.elasticsearch.index.query.BoolQueryBuilder;

import java.util.ArrayList;
import java.util.Date;
import java.util.EnumSet;
import java.util.List;
import java.util.Map;

import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.AVAILABLE;
import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.BOUNDED_ASSIGNED;
import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.CREATED;
import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.DELETED;
import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.SELF_ASSIGNED;
import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.STARTED;
import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ACTION_ID;
import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ACTIVE;
import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.DUE_DATE;
import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ENTITY_ID;
import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.TASK_STATE;
import static com.phonepe.merchant.legion.core.utils.EsUtil.TASK_INDEX;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.DISTANCE_TOO_FAR;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.SELF_ASSIGN_NOT_ALLOWED;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.MAX_RESULTS;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.TASK_INSTANCE_ID;
import static com.phonepe.merchant.legion.tasks.utils.TaskEsUtils.getBoolQuery;

public class TransitionValidator {
    private static final int ALLOWED_DISTANCE = 250; // in meters

    private final LegionService legionService;
    private final ESRepository esRepository;
    private final FoxtrotEventIngestionService foxtrotEventIngestionService;
    private final ActionValidationProcessor<TaskStartRequest> startActionValidationProcessor;
    private final ActionValidationProcessor<TaskCompleteRequest> completionActionValidationProcessor;
    private final ActionValidationProcessor<TaskAssignRequest> assignmentActionValidationProcessor;
    private final ActionVerificationProcessor actionVerificationProcessor;
    private final Validations validations;

    @Inject
    @SuppressWarnings("squid:S00107")
    public TransitionValidator(LegionService legionService,
                               ESRepository esRepository,
                               FoxtrotEventIngestionService foxtrotEventIngestionService,
                               @StartActionValidationProcessor ActionValidationProcessor<TaskStartRequest> startActionValidationProcessor,
                               @CompletionActionValidationProcessor ActionValidationProcessor<TaskCompleteRequest> completionActionValidationProcessor,
                               @AssignmentActionValidatorProcessor ActionValidationProcessor<TaskAssignRequest> assignmentActionValidationProcessor,
                               ActionVerificationProcessor actionVerificationProcessor,
                               Validations validations) {
        this.legionService = legionService;
        this.esRepository = esRepository;
        this.foxtrotEventIngestionService = foxtrotEventIngestionService;
        this.startActionValidationProcessor = startActionValidationProcessor;
        this.completionActionValidationProcessor = completionActionValidationProcessor;
        this.actionVerificationProcessor = actionVerificationProcessor;
        this.validations = validations;
        this.assignmentActionValidationProcessor = assignmentActionValidationProcessor;
    }

    @MonitoredFunction
    public void validateSelfAssignment(StoredTaskInstance storedTaskInstance, TaskAssignRequest taskAssignRequest) {
        DiscoveryTaskInstance discoveryTaskInstance = esRepository.get(taskAssignRequest.getTaskInstanceId(), TASK_INDEX, DiscoveryTaskInstance.class);
        LegionTaskStateMachineState state = storedTaskInstance.getCurState();
        SelfAssignmentTransitionRules selfAssignmentTransitionRules = validations.getSelfAssignmentRules(discoveryTaskInstance.getActionId());
        if (isSelfAssignedOrStarted(state) && !validations.isActionEligibleForSelfAssignment(state, selfAssignmentTransitionRules)) {
            throw LegionException.error(SELF_ASSIGN_NOT_ALLOWED);
        }
        assignmentActionValidationProcessor.validate(taskAssignRequest);
        validations.validateTaskSectorAndBuWithUser(taskAssignRequest, discoveryTaskInstance, selfAssignmentTransitionRules);
        int agentAssignedTaskCount = getAssignedTaskCountForAgent(taskAssignRequest.getAssignedTo(), discoveryTaskInstance.getActionId(), true);
        validations.validateSelfAssignmentLimit(discoveryTaskInstance, taskAssignRequest.getAssignedTo(), agentAssignedTaskCount);
    }

    private boolean isSelfAssignedOrStarted(LegionTaskStateMachineState state) {
        return EnumSet.of(SELF_ASSIGNED, STARTED).contains(state);
    }

    @MonitoredFunction
    public void validateBoundedAssignment(TaskAssignRequest taskAssignRequest) {
        AgentProfile agentProfile = legionService.getAgentProfile(taskAssignRequest.getAssignedTo());
        validations.validateTaskAssignee(agentProfile);
    }

    private void validateSameActor(String curActor, String requestActor) {
        if (!curActor.equals(requestActor)) {
            throw LegionException.error(LegionTaskErrorCode.INVALID_ACTOR);
        }
    }

    public void validateStart(TaskStartRequest request) {
        if(!request.isForced()) {
            startActionValidationProcessor.validate(request);
        }
        validateTaskNotExpired(request.getStoredTaskInstance());
        validateSameActor(request.getStoredTaskInstance().getCurActor(), request.getStartedBy());
    }

    private void validateTaskNotExpired(StoredTaskInstance storedTaskInstance) {
        DiscoveryTaskInstance discoveryTaskInstance = esRepository.get(storedTaskInstance.getTaskInstanceId(), EsUtil.TASK_INDEX, DiscoveryTaskInstance.class);
        if (discoveryTaskInstance.getDueDate() < System.currentTimeMillis()) {
            throw LegionException.error(LegionTaskErrorCode.TASK_EXPIRED);
        }
    }

    public void validateCompletion(TaskCompleteRequest taskCompleteRequest) {
        if(!taskCompleteRequest.isForced()) {
            completionActionValidationProcessor.validate(taskCompleteRequest);
        }
        StoredTaskInstance storedTaskInstance = taskCompleteRequest.getStoredTaskInstance();
        validateTaskNotExpired(storedTaskInstance);
        validateSameActor(storedTaskInstance.getCurActor(), taskCompleteRequest.getCompletedBy());
    }

    @MonitoredFunction
    public void validateLocationOfActivity(StoredTaskInstance storedTaskInstance, EsLocationRequest agentLocation) {
        DiscoveryTaskInstance discoveryTaskInstance = esRepository.get(storedTaskInstance.getTaskInstanceId(), EsUtil.TASK_INDEX, DiscoveryTaskInstance.class);
        if (agentLocation != null && discoveryTaskInstance.getLocation() != null) {
            double distance = DistanceCalculatorUtils.getDistanceFromLatLonInMeters(agentLocation.getLat(), agentLocation.getLon(),
                    discoveryTaskInstance.getLocation().getLat(), discoveryTaskInstance.getLocation().getLon());
            if (distance > ALLOWED_DISTANCE) {
                foxtrotEventIngestionService.ingestTaskCompletionLocationDifference(agentLocation, discoveryTaskInstance.getLocation(), distance, storedTaskInstance.getTaskInstanceId());
                throw LegionException.error(DISTANCE_TOO_FAR);
            }
        }
    }

    @MonitoredFunction
    public void validateTaskInstanceCreationRequest(TaskActionInstance actionInstance, CreateTaskInstanceRequest instanceRequest, Campaign campaign){
        actionVerificationProcessor.validateTaskCreation(instanceRequest, actionInstance);
        if (List.of(EntityType.STORE, EntityType.MERCHANT, EntityType.VPA).contains(actionInstance.getEntityType())) {
            List<DiscoveryTaskInstance> tasks = getDuplicateTasks(actionInstance.getEntityType(), instanceRequest.getEntityId(), actionInstance.getActionId(), campaign);
            //this is done to check if no deleted tasks are present in the result due to ES replication lag
            tasks = tasks.stream()
                    .filter(task -> !checkIfTaskDeletedInES(task.getTaskInstanceId()))
                    .toList();
            if (!tasks.isEmpty()) {
                throw LegionException.error(LegionTaskErrorCode.DUPLICATE_TASK_CREATION_REQUEST, Map.of(TASK_INSTANCE_ID, tasks.get(0).getTaskInstanceId()));
            }
        }
    }

    public List<DiscoveryTaskInstance> getDuplicateTasks(EntityType entityType, String entityId, String actionId, Campaign campaign) {
        List<Filter> filters = new ArrayList<>();
        filters.add(new InFilter<>(TASK_STATE, List.of(CREATED.getText(), AVAILABLE.getText(), BOUNDED_ASSIGNED.getText(), SELF_ASSIGNED.getText(), STARTED.getText())));
        filters.add(new EqualsFilter(ENTITY_ID, entityId));
        filters.add(new EqualsFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ENTITY_TYPE, entityType.name()));
        filters.add(new EqualsFilter(ACTIVE, true));
        filters.add(new EqualsFilter(ACTION_ID, actionId));
        filters.add(new GreaterEqualsNumberFilter(DUE_DATE, Math.max(System.currentTimeMillis(), campaign.getStartDate().getTime())));
        BoolQueryBuilder boolQueryBuilder = getBoolQuery(filters);
        return esRepository.search(TASK_INDEX, boolQueryBuilder, 0, 1, DiscoveryTaskInstance.class);
    }

    @MonitoredFunction
    public int getAssignedTaskCountForAgent(String curActor, String actionId, boolean active) {
        List<Filter> filters = new ArrayList<>();
        filters.add(new EqualsFilter(ACTIVE, active));
        filters.add(new EqualsFilter(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.ASSIGNED_TO, curActor));
        filters.add(new InFilter<>(TASK_STATE, List.of(SELF_ASSIGNED.getText(), STARTED.getText())));
        filters.add(new EqualsFilter(ACTION_ID, actionId));
        filters.add(new GreaterEqualsNumberFilter(DUE_DATE, System.currentTimeMillis()));
        BoolQueryBuilder boolQueryBuilder = getBoolQuery(filters);
        List<DiscoveryTaskInstance> tasks = esRepository.search(TASK_INDEX, boolQueryBuilder, 0, MAX_RESULTS, DiscoveryTaskInstance.class);
        return tasks.size();
    }

    public void validateCampaign(Campaign campaign) {
        Date today = new Date();
        Date endDate = null;
        if (campaign.getExpiryPeriod() == ExpiryPeriod.TIMESTAMP) {
            endDate = new Date(campaign.getExpiryValue());
        }
        if (endDate != null && today.after(endDate)) {
            throw LegionException.error(LegionTaskErrorCode.CAMPAIGN_EXPIRED);
        }
    }

    public void validateTaskIsExpired(StoredTaskInstance storedTaskInstance) {
        DiscoveryTaskInstance discoveryTaskInstance = esRepository.get(storedTaskInstance.getTaskInstanceId(), EsUtil.TASK_INDEX, DiscoveryTaskInstance.class);
        if (discoveryTaskInstance.getDueDate() > System.currentTimeMillis()) {
            throw LegionException.error(LegionTaskErrorCode.TASK_NOT_EXPIRED);
        }
    }

    public boolean checkIfTaskDeletedInES(String taskInstanceId) {
        DiscoveryTaskInstance discoveryTaskInstance = esRepository.get(taskInstanceId, EsUtil.TASK_INDEX, DiscoveryTaskInstance.class);
        return discoveryTaskInstance.getTaskState().equals(DELETED);
    }
}
