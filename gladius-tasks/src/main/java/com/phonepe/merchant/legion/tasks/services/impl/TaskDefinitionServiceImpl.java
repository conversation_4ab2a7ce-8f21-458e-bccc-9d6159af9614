package com.phonepe.merchant.legion.tasks.services.impl;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.gladius.models.configs.chimeraconfig.LeadIntents;
import com.phonepe.merchant.gladius.models.tags.StoreTag;
import com.phonepe.merchant.gladius.models.tasks.domain.TaskDefinitionAttributes;
import com.phonepe.merchant.gladius.models.tasks.request.ActionToRemarkConfig;
import com.phonepe.merchant.gladius.models.tasks.request.Category;
import com.phonepe.merchant.gladius.models.tasks.request.IntentWithRemarks;
import com.phonepe.merchant.gladius.models.tasks.request.TaskActionFetchByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskDefinitionCreateRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskDefinitionFetchByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskActionInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskAttributeInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskDefinitionInstance;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredDefinitionAttributeMappings;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskDefinition;
import com.phonepe.merchant.gladius.models.tasks.utils.LeadConfig;
import com.phonepe.merchant.gladius.models.tasks.utils.LeadCreationConfig;
import com.phonepe.merchant.gladius.models.tasks.utils.LeadTaskConfig;
import com.phonepe.merchant.gladius.models.tasks.utils.LeadUpdationConfig;
import com.phonepe.merchant.gladius.models.tasks.utils.TaskUtils;
import com.phonepe.merchant.legion.core.cache.CacheName;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventExecutor;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.merchant.legion.tasks.cache.LeadIntentCache;
import com.phonepe.merchant.legion.tasks.cache.TaskAttributeCache;
import com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode;
import com.phonepe.merchant.legion.tasks.repository.TaskDefinitionRepository;
import com.phonepe.merchant.legion.tasks.services.TagService;
import com.phonepe.merchant.legion.tasks.services.TaskActionService;
import com.phonepe.merchant.legion.tasks.services.TaskDefinitionService;
import com.phonepe.merchant.legion.tasks.services.ValidationService;
import com.phonepe.merchant.legion.tasks.utils.CacheUtils;
import com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants;
import com.phonepe.merchant.legion.tasks.utils.TagUtils;
import com.phonepe.merchant.legion.tasks.utils.TaskDefinitionTransformationUtils;
import com.phonepe.merchant.legion.tasks.utils.TaskFoxtrotEventUtils;
import io.appform.functionmetrics.MonitoredFunction;
import lombok.RequiredArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.INVALID_INTENT_REQUEST;
import static com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode.INVALID_TASK_DEFINITION_ID;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.DEFAULT_KEY;
import static com.phonepe.merchant.legion.tasks.utils.TagUtils.toTagRequest;
import static com.phonepe.merchant.legion.tasks.utils.ValidationUtils.validateTaskDefinition;

@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class TaskDefinitionServiceImpl implements TaskDefinitionService {

    private final TaskDefinitionRepository taskDefinitionRepository;
    private final TaskActionService taskActionService;
    private final FoxtrotEventExecutor eventExecutor;
    private final TagService tagService;
    private final TaskAttributeCache taskAttributeCache;
    private final CacheUtils cacheUtils;
    private final LeadIntentCache leadIntentCache;
    private final ValidationService validationService;
    private final LeadCreationConfig leadCreationConfig;

    @Override
    public TaskDefinitionInstance save(TaskDefinitionCreateRequest request) {
        validateTaskDefinitionId(request);
        validateDefinitionContext(request.getDefinitionAttributes());
        TaskActionInstance taskActionInstance = taskActionService.getFromDB(TaskActionFetchByIdRequest.builder()
                .taskActionId(request.getActionId())
                .build());
        validateAttributes(request.getAttributes());
        TaskDefinitionInstance instance =
                TaskDefinitionTransformationUtils.toTaskDefinitionInstance(request, taskActionInstance);
        validateTaskDefinition(instance);
        StoredTaskDefinition storedTaskDefinition = TaskDefinitionTransformationUtils.toStoredTask(instance);
        StoredTaskDefinition savedStoredTaskDefinition = taskDefinitionRepository.save(storedTaskDefinition);

        TaskDefinitionInstance taskDefinitionInstance =
                TaskDefinitionTransformationUtils.toTaskDefinitionInstance(savedStoredTaskDefinition);
        if (request.getTags() != null) {
            List<StoreTag> storeTagList = request.getTags().stream().map(tags -> TagUtils.buildDefinitionStoreTag(tags, taskDefinitionInstance)).toList();
            storeTagList.forEach(tagRequest -> tagService.saveTag(toTagRequest(tagRequest)));
        }
        eventExecutor.ingest(TaskFoxtrotEventUtils.toTaskDefinitionInstanceFoxtrotEvent(taskDefinitionInstance));
        return taskDefinitionInstance;
    }

    private void validateDefinitionContext(TaskDefinitionAttributes context) {
        if (context != null && context.getTaskType() != null) {
            validationService.validateTaskType(context.getTaskType());
        }
        LeadIntents intents = leadIntentCache.get(DEFAULT_KEY);
        if (context != null && context.getLeadConfig() != null){
            validateLeadUpdation(context.getLeadConfig(), intents);
            validateLeadCreation(context.getLeadConfig(), intents);
        }
    }

    private void validateLeadUpdation(LeadConfig leadConfig, LeadIntents allowedIntents) {
        if (leadConfig.getLeadUpdation() == null) {
            return;
        }

        List<String> leadUpdationIntents = leadConfig.getLeadUpdation().stream()
                .flatMap(req -> req.getConfig().stream().map(IntentWithRemarks::getIntent))
                .toList();
        validateIntents(leadUpdationIntents, allowedIntents);
    }

    private void validateLeadCreation(LeadConfig leadConfig,  LeadIntents allowedIntents) {
        if (leadConfig.getLeadCreation() == null) {
            return;
        }

        List<String> leadCreationIntents = leadConfig.getLeadCreation().stream()
                .flatMap(req -> req.getConfig().stream().map(IntentWithRemarks::getIntent))
                .toList();
        validateIntents(leadCreationIntents, allowedIntents);
    }

    private void validateIntents(List<String> intentsToValidate, LeadIntents allowedIntents) {
        for (String intent : intentsToValidate) {
            if (!allowedIntents.getIntents().contains(intent)) {
                throw LegionException.error(INVALID_INTENT_REQUEST);
            }
        }
    }

    private void validateTaskDefinitionId(TaskDefinitionCreateRequest request) {
        Optional<StoredTaskDefinition> definition = taskDefinitionRepository.get(request.getTaskDefinitionId());
        if (definition.isPresent()){
            throw LegionException.error(INVALID_TASK_DEFINITION_ID);
        }
    }

    @Override
    @MonitoredFunction
    public TaskDefinitionInstance getFromDb(TaskDefinitionFetchByIdRequest request) {
        StoredTaskDefinition storedTaskDefinition =
                taskDefinitionRepository.get(request.getTaskDefinitionId())
                        .orElseThrow(() -> LegionException.error(CoreErrorCode.NOT_FOUND, Map.of("message", "Invalid definition Id")));

        Map<String, Set<String>> attributes = new HashMap<>();
        addAttributes(storedTaskDefinition, attributes);
        return TaskDefinitionTransformationUtils.toTaskDefinitionInstance(storedTaskDefinition, attributes);
    }

    @Override
    @MonitoredFunction
    public TaskDefinitionInstance getFromCache(TaskDefinitionFetchByIdRequest request) {
        return (TaskDefinitionInstance) cacheUtils.getValue(request.getTaskDefinitionId(), CacheName.TASK_DEFINITION);
    }

    private void addAttributes(StoredTaskDefinition storedTaskDefinition, Map<String, Set<String>> attributes) {
        storedTaskDefinition.getActiveAttributes().forEach(attributeValue -> {
            TaskAttributeInstance attribute = taskAttributeCache.get(attributeValue.getAttributeValue());
            @NotNull String attributeType = attribute.getAttributeType().getName();
            if (!attributes.containsKey(attributeType)) {
                attributes.put(attributeType, new HashSet<>());
            }
            attributes.get(attributeType).add(attributeValue.getAttributeValue());
        });
    }

    @Override
    public TaskDefinitionInstance update(TaskDefinitionInstance request) {
        validateTaskDefinition(request, taskActionService);
        validateDefinitionContext(request.getDefinitionAttributes());
        taskDefinitionRepository.update(request.getTaskDefinitionId(), storedTaskDefinition -> {
            mutate(storedTaskDefinition, request);
            return storedTaskDefinition;
        });
        return getFromDb(TaskDefinitionFetchByIdRequest.builder()
                .taskDefinitionId(request.getTaskDefinitionId())
                .build());
    }

    @Override
    public List<TaskDefinitionInstance> fetchByActionId(String actionId) {
        List<StoredTaskDefinition> storedTaskDefinitions =
                taskDefinitionRepository.fetchByField(LegionTaskConstants.ACTION_ID, actionId);
        List<TaskDefinitionInstance> taskDefinitionInstances = new ArrayList<>();
        storedTaskDefinitions.forEach(storedTaskDefinition -> {
            Map<String, Set<String>> attributes = new HashMap<>();
            addAttributes(storedTaskDefinition, attributes);
            taskDefinitionInstances.add(TaskDefinitionTransformationUtils.toTaskDefinitionInstance(storedTaskDefinition, attributes));
        });
        return taskDefinitionInstances;
    }

    private void mutate(StoredTaskDefinition storedTaskDefinition, TaskDefinitionInstance request) {
        List<StoredDefinitionAttributeMappings> mergedAttributes = TaskUtils.mergeAttributes(
                storedTaskDefinition.getAttributes(), TaskUtils.getAttributes(request, storedTaskDefinition));
        TaskDefinitionAttributes definitionAttributes = Optional.ofNullable(request.getDefinitionAttributes())
                .orElseGet(() -> TaskDefinitionAttributes.builder().category(Category.OTHERS).build());
        definitionAttributes.setCategory(Optional.ofNullable(definitionAttributes.getCategory()).orElse(Category.OTHERS));
        storedTaskDefinition.setName(request.getName());
        storedTaskDefinition.setUpdatedBy(request.getUpdatedBy());
        storedTaskDefinition.setTaskDefinitionMeta(SerDe.writeValueAsBytes(request.getTaskDefinitionMeta()));
        storedTaskDefinition.setDefinitionAttributes(SerDe.writeValueAsBytes(definitionAttributes));
        storedTaskDefinition.setPoints(request.getPoints());
        storedTaskDefinition.setTaskOperationsMeta(SerDe.writeValueAsBytes(request.getTaskOperationsMeta()));
        storedTaskDefinition.setAttributes(mergedAttributes);
        storedTaskDefinition.setDefinitionAttributes(SerDe.writeValueAsBytes(request.getDefinitionAttributes()));
    }

    private void validateAttributes(Map<String, Set<String>> attributes) {
        if (Objects.isNull(attributes)) {
            return;
        }
        attributes.forEach((key, value) -> value.forEach(taskAttributeCache::get));
    }

    @Override
    public LeadCreationConfig getLeadCreationConfig() {
        List<LeadTaskConfig> allowedConfigs = leadCreationConfig.getLeadTaskConfigs().stream()
                .map(taskConfig -> {
                    TaskDefinitionInstance taskDefinitionInstance = getFromCache(TaskDefinitionFetchByIdRequest.builder()
                            .taskDefinitionId(taskConfig.getDefinitionId())
                            .build());
                    if (taskDefinitionInstance.isLeadCreationAllowed()) {
                        List<ActionToRemarkConfig> leadCreation =
                                taskDefinitionInstance.getDefinitionAttributes().getLeadConfig().getLeadCreation();
                        taskConfig.setLeadCreationIntents(leadCreation.get(0).getConfig());
                        return taskConfig;
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .toList();

        return LeadCreationConfig.builder()
                .leadTaskConfigs(allowedConfigs)
                .build();
    }

    @Override
    public LeadUpdationConfig getLeadUpdationConfig(String taskDefinitionId) {
        TaskDefinitionInstance taskDefinitionInstance = getFromCache(TaskDefinitionFetchByIdRequest.builder().taskDefinitionId(taskDefinitionId).build());
        if (taskDefinitionInstance.isLeadUpdationAllowed()) {
            List<ActionToRemarkConfig> leadUpdation = taskDefinitionInstance.getDefinitionAttributes().getLeadConfig().getLeadUpdation();
            return LeadUpdationConfig.builder().leadUpdationIntents(leadUpdation.get(0).getConfig()).build();
        }
        throw LegionException.error(LegionTaskErrorCode.LEAD_INTENTS_NOT_AVAILABLE);
    }
}
