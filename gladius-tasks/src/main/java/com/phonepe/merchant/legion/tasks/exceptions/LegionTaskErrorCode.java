package com.phonepe.merchant.legion.tasks.exceptions;

import com.phonepe.merchant.legion.core.exceptions.ErrorCode;
import lombok.Getter;

import javax.ws.rs.core.Response;

@Getter
public enum LegionTaskErrorCode implements ErrorCode {
    FORBIDDEN_COMMAND(Response.Status.BAD_REQUEST, "This command cannot be called through command api"),
    INVALID_TASK_DEFINITION_ID(Response.Status.BAD_REQUEST, "Invalid task definition id"),
    INVALID_TASK_ACTION_ID(Response.Status.BAD_REQUEST, "Invalid task action id"),
    INVALID_TASK_VERIFIER(Response.Status.BAD_REQUEST, "Invalid verifier"),
    INVALID_TASK_VALIDATOR(Response.Status.BAD_REQUEST, "Invalid validator"),
    INTERNAL_ERROR(Response.Status.INTERNAL_SERVER_ERROR, "something went wrong"),
    INVALID_INDEX(Response.Status.BAD_REQUEST, "Invalid ES index/mapping type"),
    INVALID_ACTOR(Response.Status.BAD_REQUEST, "This actor is not allowed to mark this task complete"),
    INVALID_START_ACTOR(Response.Status.BAD_REQUEST, "This actor is not allowed to start this task"),
    INVALID_ENTITY_ID(Response.Status.BAD_REQUEST, "Invalid entity id"),
    INVALID_TASK_TYPE(Response.Status.BAD_REQUEST, "Invalid task type"),
    BAD_REQUEST(Response.Status.BAD_REQUEST, "Bad Request"),
    TASK_SECTOR_NOT_ASSIGNED(Response.Status.BAD_REQUEST, "Sector of this Task is not assigned to you"),
    INVALID_AGENT_ID(Response.Status.BAD_REQUEST, "Bad Request"),
    DISTANCE_TOO_FAR(Response.Status.BAD_REQUEST, "You are too far from the location"),
    DISTANCE_TOO_FAR_TO_ASSIGN(Response.Status.BAD_REQUEST, "You are not allowed to self assign this task because you are away from the merchant"),
    DISTANCE_TOO_FAR_TO_START(Response.Status.BAD_REQUEST, "You are too far from the location"),
    PENDING_IMAGE_FOUND(Response.Status.BAD_REQUEST, "You need to either approve or reject the photo before marking the task as complete"),
    DUPLICATE_TASK_CREATION_REQUEST(Response.Status.BAD_REQUEST, "Duplicate task creation request"),
    CAMPAIGN_ID_ALREADY_EXISTS(Response.Status.BAD_REQUEST, "Campaign Id already exists"),
    CAMPAIGN_EXPIRED(Response.Status.BAD_REQUEST, "Campaign has expired"),
    NO_TAG_FOUND(Response.Status.BAD_REQUEST, "Tag does not exists"),
    TASK_RECREATION_FAILURE(Response.Status.BAD_REQUEST, "task recreation not allowed for this instance"),
    TASK_START_VALIDATION_FAILURE(Response.Status.BAD_REQUEST, "Pre requisites for starting the task are not complete"),
    TASK_VALIDATION_FAILURE(Response.Status.BAD_REQUEST, "Some actions necessary for completing the task are incomplete. Please make sure you have completed all the steps before submitting the task"),
    VALIDATION_DOWNSTREAM_SERVICE_FAILURE(Response.Status.BAD_REQUEST, "Some actions necessary for completing the task cannot be validated because of a server error. If you have completed all the steps, please try to submit the task again after some time"),
    SECTOR_MISMATCH(Response.Status.BAD_REQUEST, "Sector mismatch between task and agent"),
    SECTOR_MISSING(Response.Status.BAD_REQUEST, "Sector is missing for this agent, please get it updated by your manager"),
    INVALID_CLIENT_TASK_CREATION_REQUEST(Response.Status.BAD_REQUEST, "Task Creation Request is not valid"),
    TASK_CTA_NOT_USED(Response.Status.BAD_REQUEST, "Kindly use the task's CTAs to perform the appropriate action"),
    INCOMPLETE_ONBOARDING(Response.Status.BAD_REQUEST, "Store has not been completely onboarded"),
    UNAUTHORISED_DATA_REQUEST(Response.Status.BAD_REQUEST, "You are not authorised to access this data"),
    MANAGER_NOT_FOUND(Response.Status.BAD_REQUEST, "Manager of agent/FL not found"),
    TASK_EXPIRED(Response.Status.BAD_REQUEST, "You can not perform this action as the task has been expired"),
    INVALID_TASK_INSTANCE_ID(Response.Status.BAD_REQUEST, "Invalid task instance id"),
    INVALID_STATE(Response.Status.BAD_REQUEST, "Invalid task instance State for the action"),
    TIME_GREATER_THAN_EXPIRY(Response.Status.BAD_REQUEST, "Rescehduled at can't be greater than expiry of tasks"),
    TIME_SMALLER_THAN_CURRENT_TIME(Response.Status.BAD_REQUEST, "Rescheduled date can not be less than the current time"),
    EMPTY_SECTOR(Response.Status.BAD_REQUEST, "Empty Sectors"),
    ALREADY_UPDATED_SECTOR(Response.Status.BAD_REQUEST, "ALREADY UPDATED Sectors"),
    SECTOR_UPDATE_FAILURE(Response.Status.BAD_REQUEST, "SECTOR UPDATE FAILURE"),
    USER_ID_NOT_FOUND(Response.Status.BAD_REQUEST, "User id not found"),
    TASK_NOT_EXPIRED(Response.Status.BAD_REQUEST, "Task is not expired"),
    FORM_NOT_AVAILABLE(Response.Status.BAD_REQUEST, "INVALID TASK TYPE"),
    SECTOR_NOT_POLYGON(Response.Status.BAD_REQUEST, "Input Sector is not a Polygon ShapeType"),
    WORKFLOW_ID_MISSING(Response.Status.BAD_REQUEST, "Workflow Id is missing for the task"),
    SECTOR_PROCESSING_ERROR(Response.Status.BAD_REQUEST,"Error occurred while processing sector"),
    DISCOVERABLE_TASK_ALREADY_PRESENT(Response.Status.BAD_REQUEST, "Lead is already present for this store and is discoverable"),
    ASSIGNED_TASK_ALREADY_PRESENT(Response.Status.BAD_REQUEST, "Lead is already present and assigned to someone"),
    INVALID_INTENT(Response.Status.BAD_REQUEST, "You can not complete the task with this Intent"),
    INVALID_INTENT_REQUEST(Response.Status.BAD_REQUEST, "Intent in the input is not whitelisted"),
    TASK_META_UPDATE_FAILED(Response.Status.BAD_REQUEST, "Something went wrong while trying to update task meta"),
    RESCHEDULE_DATE_EXCEEDS_LIMIT(Response.Status.BAD_REQUEST, "Follow up date is exceeding the max allowed limit"),
    TASK_CREATION_NOT_ALLOWED(Response.Status.BAD_REQUEST, "Please update the lead if lending task is available"),
    VISIBILITY_CONFIG_ISSUE(Response.Status.BAD_REQUEST, "DEFINITION and ACTION has conflicting config"),
    ACTION_VISIBILITY_CONFIG_ISSUE(Response.Status.BAD_REQUEST, "Issue with Visiblity Config"),
    VIEW_NOT_ENABLED(Response.Status.BAD_REQUEST, "view not enabled"),
    BUSINESS_UNIT_MISMATCH(Response.Status.BAD_REQUEST, "You are not allowed to self assign the task of other Business Unit"),
    CLIENT_TASK_QUEUE_PUSH_FAILED(Response.Status.INTERNAL_SERVER_ERROR, "Exception while pushing into task creation Queue"),
    ASSIGNMENT_NOT_ALLOWED(Response.Status.BAD_REQUEST, "You are not allowed to self assign this task"),
    TASK_NOT_IN_ACCESSIBLE_SECTOR(Response.Status.BAD_REQUEST, "Task is not in accessible sector"),
    HOTSPOT_NOT_FOUND(Response.Status.NOT_FOUND, "Hotspot not found"),
    HOTSPOT_CONFIG_NOT_FOUND(Response.Status.NOT_FOUND, "Hotspot Config not found"),
    HOTSPOT_CONFIG_LOAD_FAILURE(Response.Status.INTERNAL_SERVER_ERROR, "Hotspot Config load failed"),
    DEVICE_NOT_IN_DELIVERED_STATE(Response.Status.BAD_REQUEST, "Device is not in delivered state"),
    SELF_ASSIGN_NOT_ALLOWED(Response.Status.BAD_REQUEST, "You are not allowed to self assign this task"),
    INVALID_HOTSPOT_TYPE(Response.Status.BAD_REQUEST, "Invalid Hotspot Type"),
    FAILED_TO_FETCH_HOTSPOT_CONFIG(Response.Status.INTERNAL_SERVER_ERROR, "Failed to load hotspot config from cache"),
    SECTOR_ALREADY_WHITELISTED(Response.Status.INTERNAL_SERVER_ERROR, "One or more sectors are already whitelisted in this category"),
    INVALID_HOTSPOT_CONFIG_CREATION_REQUEST(Response.Status.NOT_FOUND, "Hotspot Config creation request is invalid"),
    FORM_FOR_FORM_TYPE_NOT_AVAILABLE(Response.Status.BAD_REQUEST, "Form for provided form type is not available"),
    FEEDBACK_SAVE_FAILED(Response.Status.INTERNAL_SERVER_ERROR, "Exception while saving or updating feedback"),
    FORM_CONFIG_SAVE_FAILED(Response.Status.INTERNAL_SERVER_ERROR, "Exception while saving or updating form config"),
    INVALID_LOCATION_CHECK_PARAMS(Response.Status.BAD_REQUEST, "Required location params provided in request are invalid"),
    INVALID_ASSET_OR_MERCHANT_STORE_FOR_CONFIGURED_FORM(Response.Status.BAD_REQUEST, "Required asset/store provided in request are invalid for configured form"),
    UNABLE_TO_RETRIEVE_FORM_DATA_FROM_BRICKBAT(Response.Status.INTERNAL_SERVER_ERROR, "Unable to retrieve form data from brickbat"),
    LEAD_INTENTS_NOT_AVAILABLE(Response.Status.BAD_REQUEST, "Lead Intents Not available"),
    SELF_ASSIGN_LIMIT_BREACHED(Response.Status.BAD_REQUEST, "Task assignment limit reached: You cannot self-assign additional tasks."),
    DISTANCE_TOO_FAR_TO_SELF_ASSIGN(Response.Status.BAD_REQUEST, "You are too far from the location to self assign the task");


    private Response.Status code;
    private String message;

    LegionTaskErrorCode(Response.Status code, String message) {
        this.code = code;
        this.message = message;
    }
}
