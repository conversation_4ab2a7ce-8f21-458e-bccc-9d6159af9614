package com.phonepe.merchant.legion.tasks.cache;

import com.codahale.metrics.MetricRegistry;
import com.google.inject.Inject;
import com.phonepe.merchant.gladius.models.configs.chimeraconfig.LeadIntents;
import com.phonepe.merchant.legion.core.cache.AsyncCache;
import com.phonepe.merchant.legion.core.cache.CacheConfig;
import com.phonepe.merchant.legion.core.cache.CacheName;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.repository.impl.ChimeraRepositoryImpl;
import com.phonepe.merchant.legion.tasks.utils.CacheUtils;
import lombok.extern.slf4j.Slf4j;
import ru.vyarus.dropwizard.guice.module.installer.feature.eager.EagerSingleton;

import java.util.Map;

@Slf4j
@EagerSingleton
public class LeadIntentCache extends AsyncCache<String, LeadIntents> {

    private static final String LEAD_INTENT_CHIMERA_KEY = "gladius_lead_intents";
    @Inject
    public LeadIntentCache(Map<CacheName, CacheConfig> cacheConfigs,
                           ChimeraRepositoryImpl chimeraRepository,
                           MetricRegistry metricRegistry,
                           CacheUtils cacheUtils) {
        super(CacheName.LEAD_INTENTS, cacheConfigs.get(CacheName.LEAD_INTENTS), key -> {
            try {
                return chimeraRepository.getChimeraConfig(LEAD_INTENT_CHIMERA_KEY, LeadIntents.class);
            } catch (Exception e) {
                log.warn("Error while loading Lead intents config from chimera {}", key);
                throw LegionException.propagate(CoreErrorCode.INTERNAL_ERROR, e);
            }
        }, metricRegistry);
        cacheUtils.registerCache(CacheName.LEAD_INTENTS, this);
    }
}