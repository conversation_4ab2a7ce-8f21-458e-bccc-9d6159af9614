package com.phonepe.merchant.legion.tasks.search.query.search;

import com.phonepe.merchant.legion.core.repository.ChimeraLiteRepository;
import com.phonepe.merchant.legion.tasks.cache.FilterCraftBuilderCache;
import com.phonepe.merchant.legion.tasks.cache.models.FilterCraftBuilderCacheKey;
import com.phonepe.merchant.legion.tasks.search.query.filter.request.TaskTypeFiltersConfig;
import io.appform.functionmetrics.MonitoredFunction;
import org.elasticsearch.index.query.BoolQueryBuilder;

import javax.inject.Inject;
import javax.inject.Singleton;
import java.util.Map;

@Singleton
public class TaskTypeFilterQuery {

    private final ChimeraLiteRepository chimeraRepository;
    private static final String TASK_TYPE_MAPPING = "task_type_config_mapping";
    private final FilterCraftBuilderCache filterCraftBuilderCache;

    @Inject
    TaskTypeFilterQuery(ChimeraLiteRepository chimeraRepository,
                        FilterCraftBuilderCache filterCraftBuilderCache) {
        this.chimeraRepository = chimeraRepository;
        this.filterCraftBuilderCache = filterCraftBuilderCache;
    }

    @MonitoredFunction
    public BoolQueryBuilder taskTypeToFilterQuery(String taskType){
        TaskTypeFiltersConfig taskTypeFiltersConfig = chimeraRepository.getChimeraConfig(TASK_TYPE_MAPPING, TaskTypeFiltersConfig.class);
        for (Map.Entry<String, String> type : taskTypeFiltersConfig.getRules().entrySet()) {
            if (type.getKey().equals(taskType)) {
                return filterCraftBuilderCache.get(FilterCraftBuilderCacheKey.builder()
                        .config(chimeraRepository.getChimeraConfigString(type.getValue())).build())
                        .convertToESFilter();
            }
        }
        return null;
    }
}

