package com.phonepe.merchant.legion.tasks.services;

import com.phonepe.merchant.gladius.models.tasks.request.TaskDefinitionCreateRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskDefinitionFetchByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskDefinitionInstance;
import com.phonepe.merchant.gladius.models.tasks.utils.LeadCreationConfig;
import com.phonepe.merchant.gladius.models.tasks.utils.LeadUpdationConfig;

import java.util.List;

public interface TaskDefinitionService {
    TaskDefinitionInstance save(TaskDefinitionCreateRequest request);

    TaskDefinitionInstance getFromDb(TaskDefinitionFetchByIdRequest request);

    TaskDefinitionInstance getFromCache(TaskDefinitionFetchByIdRequest request);

    TaskDefinitionInstance update(TaskDefinitionInstance request);

    List<TaskDefinitionInstance> fetchByActionId(String actionId);

    LeadCreationConfig getLeadCreationConfig();

    LeadUpdationConfig getLeadUpdationConfig(String taskDefinitionIds);
}
