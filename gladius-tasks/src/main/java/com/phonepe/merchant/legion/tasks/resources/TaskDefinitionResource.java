package com.phonepe.merchant.legion.tasks.resources;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.gandalf.client.annotation.GandalfUserContext;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.merchant.gladius.models.tasks.request.TaskDefinitionCreateRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskDefinitionFetchByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskDefinitionInstance;
import com.phonepe.merchant.gladius.models.tasks.utils.LeadCreationConfig;
import com.phonepe.merchant.gladius.models.tasks.utils.LeadUpdationConfig;
import com.phonepe.merchant.legion.client.annotations.LegionGateKeeper;
import com.phonepe.merchant.legion.client.annotations.LegionUserContext;
import com.phonepe.merchant.legion.core.utils.AuthUserDetails;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.services.TaskDefinitionService;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import com.phonepe.platform.requestinfo.bundle.annotations.RequestContext;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import io.dropwizard.auth.Auth;
import io.dropwizard.primer.auth.annotation.Authorize;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;
import killswitch.enums.OperationType;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.security.RolesAllowed;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import java.util.Optional;

import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.AUTH_NAME;
import static javax.ws.rs.core.HttpHeaders.AUTHORIZATION;

@Slf4j
@Singleton
@Path("/v1/task/definition")
@Tag(name = "Task Definition Related APIs")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@SecurityRequirement(name = AUTH_NAME)
@SecurityScheme(name = AUTH_NAME, type = SecuritySchemeType.APIKEY,
        in = SecuritySchemeIn.HEADER, paramName = AUTHORIZATION)
public class TaskDefinitionResource {

    private final TaskDefinitionService taskDefinitionService;

    @Inject
    public TaskDefinitionResource(TaskDefinitionService taskDefinitionService) {
        this.taskDefinitionService = taskDefinitionService;
    }

    @POST
    @Path("/create")
    @Authorize(value = "all")
	@RolesAllowed(value = "all")
    @Timed
    @Operation(summary = "Create Task Definition")
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.WRITE})
    @LegionGateKeeper
	public GenericResponse<TaskDefinitionInstance> create(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @Parameter(hidden = true) @RequestContext RequestInfo requestInfo,
                                                          @NotNull @Valid TaskDefinitionCreateRequest request,
                                                          @Parameter(hidden = true) @GandalfUserContext UserDetails userDetails,
                                                          @Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
                                                          @Parameter(hidden = true) @LegionUserContext AgentProfile agentProfile){
        String actor = AuthUserDetails.getLegionUserId(agentProfile, userDetails, userPrincipal);
        request.setCreatedBy(actor);
        TaskDefinitionInstance response = taskDefinitionService.save(request);
        return GenericResponse.<TaskDefinitionInstance>builder()
                        .success(response != null)
                        .data(response)
                        .build();
    }

    @GET
    @Path("/{taskDefinitionId}")
    @Timed
    @Operation(summary = "Fetch Task Definition")
    @RolesAllowed(value = "all")
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.READ})
	public GenericResponse<TaskDefinitionInstance> fetch(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @Parameter(hidden = true) @RequestContext RequestInfo requestInfo,
                                                         @PathParam("taskDefinitionId") @NotNull final String taskDefinitionId) {
        TaskDefinitionInstance response = taskDefinitionService.getFromDb(
                TaskDefinitionFetchByIdRequest.builder()
                        .taskDefinitionId(taskDefinitionId)
                        .build()
        );
        return GenericResponse.<TaskDefinitionInstance>builder()
                        .success(response != null)
                        .data(response)
                        .build();
    }

    @PUT
    @Path("/update")
    @Authorize(value = "all")
    @RolesAllowed(value = "all")
    @Timed
    @Operation(summary = "update Task Definition")
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.WRITE})
    @LegionGateKeeper
    public GenericResponse<TaskDefinitionInstance> update(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal, @Parameter(hidden = true) @RequestContext RequestInfo requestInfo,
                                                          @Valid @NotNull TaskDefinitionInstance request,
                                                          @Parameter(hidden = true) @GandalfUserContext UserDetails userDetails,
                                                          @Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
                                                          @Parameter(hidden = true) @LegionUserContext AgentProfile agentProfile) {
        String actor = AuthUserDetails.getLegionUserId(agentProfile, userDetails, userPrincipal);
        request.setUpdatedBy(actor);
        TaskDefinitionInstance response = taskDefinitionService.update(request);
        return GenericResponse.<TaskDefinitionInstance>builder()
                .success(response != null)
                .data(response)
                .build();
    }


    @GET
    @Path("/leadCreationConfig")
    @Timed
    @Operation(summary = "Get Lead Creation Config")
    @ExceptionMetered
    @RolesAllowed("getLeadConfig")
    @ApiKillerMeta(tags = {OperationType.READ})
    public GenericResponse<LeadCreationConfig> getLeadCreationConfig() {

        return GenericResponse.<LeadCreationConfig>builder()
                .success(true).data(taskDefinitionService.getLeadCreationConfig())
                .build();
    }

    @GET
    @Path("/leadUpdationConfig/{taskDefinitionId}")
    @Timed
    @Operation(summary = "Get Lead Updation Config")
    @RolesAllowed("getLeadConfig")
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.READ})
    public GenericResponse<LeadUpdationConfig> getLeadUpdationConfig(@PathParam("taskDefinitionId") @NotNull @NotEmpty String taskDefinitionId) {

        return GenericResponse.<LeadUpdationConfig>builder()
                .success(true)
                .data(taskDefinitionService.getLeadUpdationConfig(taskDefinitionId))
                .build();
    }
}
