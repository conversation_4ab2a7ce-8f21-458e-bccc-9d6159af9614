package com.phonepe.merchant.legion.tasks.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.phonepe.discovery.models.core.request.query.filter.Filter;
import com.phonepe.discovery.models.core.request.query.filter.general.EqualsFilter;
import com.phonepe.merchant.gladius.models.entitystore.Entity;
import com.phonepe.merchant.gladius.models.entitystore.MerchantEntity;
import com.phonepe.merchant.gladius.models.entitystore.StoreEntity;
import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineEvent;
import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState;
import com.phonepe.merchant.gladius.models.tasks.domain.TaskDefinitionAttributes;
import com.phonepe.merchant.gladius.models.tasks.domain.TaskInstance;
import com.phonepe.merchant.gladius.models.tasks.domain.TaskTransition;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.request.Category;
import com.phonepe.merchant.gladius.models.tasks.request.ClientTaskCreateAndAssignRequest;
import com.phonepe.merchant.gladius.models.tasks.request.ClientTaskDeleteRequest;
import com.phonepe.merchant.gladius.models.tasks.request.CreateTaskInstanceRequest;
import com.phonepe.merchant.gladius.models.tasks.request.ListingRequestMeta;
import com.phonepe.merchant.gladius.models.tasks.request.TaskDefinitionMeta;
import com.phonepe.merchant.gladius.models.tasks.request.TaskExpireRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskInstanceMeta;
import com.phonepe.merchant.gladius.models.tasks.request.TaskMetaUpdateRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskAssignRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCompleteRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCreateRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskMarkAvailableRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.UserTaskCreationRequest;
import com.phonepe.merchant.gladius.models.tasks.response.Campaign;
import com.phonepe.merchant.gladius.models.tasks.response.SectorTaskSearchResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskActionInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskAttributeInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskDefinitionInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskDetailResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMapViewDetails;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaInformation;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaType;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskDefinition;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskTransition;
import com.phonepe.merchant.gladius.models.tasks.utils.LeadConfig;
import com.phonepe.merchant.gladius.models.tasks.utils.Miscellaneous;
import com.phonepe.merchant.gladius.models.tasks.utils.TaskCreationConfigParameters;
import com.phonepe.merchant.gladius.models.tasks.utils.TaskParams;
import com.phonepe.merchant.gladius.models.tasks.utils.TaskUtils;
import com.phonepe.merchant.legion.core.utils.DateUtils;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.merchant.legion.tasks.actions.models.ActionVerificationResponse;
import com.phonepe.merchant.legion.tasks.repository.TaskInstanceRepository;
import com.phonepe.models.merchants.tasks.EntityType;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.search.SearchHit;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;

import static com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.TASK_DEFINITION_ID;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.NON_PRIORITY_TASK_COUNT;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.PRIORITY_TASK_COUNT;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.STEP_KEY_NAME;


@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class TaskInstanceTransformationUtils {

  public static List<Object> getSteps(TaskActionInstance taskActionInstance, TaskDefinitionInstance taskDefinitionInstance) {

    TaskDefinitionMeta taskDefinitionMeta = (taskDefinitionInstance!=null)?taskDefinitionInstance.getTaskDefinitionMeta():null;
    List<Object> actionSubsteps = (taskActionInstance!=null)?taskActionInstance.getSubSteps():null;
    List<Object> taskDefinitionSubsteps = (taskDefinitionMeta!=null)?taskDefinitionMeta.getSubsteps():null;
    List<Object> steps = null;
    if (actionSubsteps == null && taskDefinitionSubsteps == null) {
        return steps;
    }
    actionSubsteps = ObjectUtils.defaultIfNull(actionSubsteps, new ArrayList<>());
    taskDefinitionSubsteps = ObjectUtils.defaultIfNull(taskDefinitionSubsteps, new ArrayList<>());
    int actionStepNo=0;
    int taskDefinitionStepNo=0;
    steps = new ArrayList<>();
    while (actionStepNo < actionSubsteps.size() && taskDefinitionStepNo < taskDefinitionSubsteps.size()) {
        Map<String,Object> actionSubstep = (Map<String, Object>) actionSubsteps.get(actionStepNo);
        Map<String,Object> taskDefinitionSubstep = (Map<String, Object>) taskDefinitionSubsteps.get(taskDefinitionStepNo);
        if (!actionSubstep.containsKey(STEP_KEY_NAME)) {
            actionStepNo++;
        } else if (!taskDefinitionSubstep.containsKey(STEP_KEY_NAME)) {
            taskDefinitionStepNo++;
        } else {
            int actionSubstepStepNo = Integer.parseInt((String) actionSubstep.get(STEP_KEY_NAME));
            int taskDefinitionSubstepStepNo = Integer.parseInt((String) taskDefinitionSubstep.get(STEP_KEY_NAME));
            if (actionSubstepStepNo <= taskDefinitionSubstepStepNo) {
                steps.add(actionSubstep);
                actionStepNo++;
            } else {
                steps.add(taskDefinitionSubstep);
                taskDefinitionStepNo++;
            }
        }
    }
    while (actionStepNo<actionSubsteps.size()) {
        steps.add(actionSubsteps.get(actionStepNo));
        actionStepNo++;
    }
    while (taskDefinitionStepNo<taskDefinitionSubsteps.size()) {
        steps.add(taskDefinitionSubsteps.get(taskDefinitionStepNo));
        taskDefinitionStepNo++;
    }
    return steps;
  }

  public static TaskMetaResponse discoveryTaskInstanceToTaskMeta(DiscoveryTaskInstance discoveryTaskInstance,
                                                                 TaskDefinitionInstance taskDefinitionInstance,
                                                                 TaskInstance taskInstance) {
      String description = taskDefinitionInstance.getName();
      TaskMetaResponse taskMetaResponse = TaskMetaResponse.builder()
              .createdAt(discoveryTaskInstance.getCreatedAt())
              .updatedAt(discoveryTaskInstance.getUpdatedAt())
              .location(discoveryTaskInstance.getLocation())
              .points(discoveryTaskInstance.getPoints())
              .entityId(discoveryTaskInstance.getEntityId())
              .taskInstanceId(discoveryTaskInstance.getTaskInstanceId())
              .description(description)
              .taskStatus(discoveryTaskInstance.getTaskState())
              .dueDate(discoveryTaskInstance.getDueDate())
              .resheduledAt(discoveryTaskInstance.getRescheduledAt())
              .completedOn(discoveryTaskInstance.getCompletedOn())
              .entityType(discoveryTaskInstance.getEntityType())
              .category(Optional.ofNullable(taskDefinitionInstance.getDefinitionAttributes())
                      .map(TaskDefinitionAttributes::getCategory)
                      .orElse(Category.OTHERS))
              .taskDefinitionId(taskDefinitionInstance.getTaskDefinitionId())
              .build();

    if (taskInstance != null && taskInstance.getInstanceMeta() != null && taskInstance.getInstanceMeta().getTaskMetaList() != null) {
        taskInstance.getInstanceMeta().getTaskMetaList().forEach(taskMetaInformation -> {
            taskMetaResponse.addTaskMetaToMap(taskMetaInformation);
            if (Boolean.TRUE.equals(taskMetaInformation.getDisplayInformation()))
                taskMetaResponse.addTaskMetaToDisplayList(taskMetaInformation);
        });
    }

    ListingRequestMeta listingRequestMeta = discoveryTaskInstance.getEntityType().accept(new EntityType.EntityTypeVisitor<ListingRequestMeta,DiscoveryTaskInstance>(){
        @Override
        public ListingRequestMeta visitUser(DiscoveryTaskInstance discoveryTaskInstance) {
            return null;
        }

        @Override
        public ListingRequestMeta visitSector(DiscoveryTaskInstance discoveryTaskInstance) {
            List<Filter> filters = new ArrayList<>();
            filters.add(new EqualsFilter(TASK_DEFINITION_ID,discoveryTaskInstance.getTaskDefinitionId()));
            return ListingRequestMeta.builder()
                    .additionalFilters(filters)
                    .build();
        }

        @Override
        public ListingRequestMeta visitMerchant(DiscoveryTaskInstance discoveryTaskInstance) {
            return null;
        }

        @Override
        public ListingRequestMeta visitNone(DiscoveryTaskInstance discoveryTaskInstance) {
            return null;
        }

        @Override
        public ListingRequestMeta visitStore(DiscoveryTaskInstance discoveryTaskInstance) {
            return null;
        }

        @Override
        public ListingRequestMeta visitTask(DiscoveryTaskInstance discoveryTaskInstance) {
            return null;
        }

        @Override
        public ListingRequestMeta visitVpa(DiscoveryTaskInstance discoveryTaskInstance) {
            return null;
        }

        @Override
        public ListingRequestMeta visitExternal(DiscoveryTaskInstance discoveryTaskInstance) {
            return null;
        }

        @Override
        public ListingRequestMeta visitAgent(DiscoveryTaskInstance discoveryTaskInstance) {
            return null;
        }
    },discoveryTaskInstance);
    taskMetaResponse.setListingRequestMeta(listingRequestMeta);
    return taskMetaResponse;
  }

    public static DiscoveryTaskInstance toDiscoveryTaskInstance(TaskDefinitionInstance taskDefinitionInstance,
                                                                TaskActionInstance taskActionInstance,
                                                                Entity entity,
                                                                Campaign campaign,
                                                                CreateTaskInstanceRequest request,
                                                                String taskInstanceId) {
        long dueDate;
        dueDate = TaskUtils.getDueDate(campaign.getExpiryPeriod(), campaign.getExpiryValue());
        Map<String, Set<String>> attributes = mergeAttribute(taskActionInstance.getAttributes(), taskDefinitionInstance.getAttributes());
        DiscoveryTaskInstance discoveryTaskInstance = DiscoveryTaskInstance.builder()
                .createdAt(System.currentTimeMillis())
                .updatedAt(System.currentTimeMillis())
                .points((long) ObjectUtils.firstNonNull(request.getPoints(), taskDefinitionInstance.getPoints()))
                .location(entity.getLocation())
                .entityId(entity.getEntityId())
                .entityType(taskActionInstance.getEntityType())
                .taskDefinitionId(taskDefinitionInstance.getTaskDefinitionId())
                .actionId(taskActionInstance.getActionId())
                .taskInstanceId(taskInstanceId)
                .taskState(LegionTaskStateMachineState.CREATED)
                .namespace(taskActionInstance.getNamespace())
                .dueDate(dueDate)
                .startDate(campaign.getStartDate().getTime())
                .polygonIds(entity.getPolygonIds())
                .campaign(campaign.getCampaignId())
                .active(true)
                .createdBy(request.getCreatedBy())
                .updatedBy(request.getCreatedBy())
                .build();
        if (!attributes.isEmpty()) {
            discoveryTaskInstance.setAttributes(attributes);
        }

        return entity.getType().accept(new EntityType.EntityTypeVisitor<DiscoveryTaskInstance, Entity>() {
            @Override
            public DiscoveryTaskInstance visitUser(Entity entity) {
                return discoveryTaskInstance;
            }

            @Override
            public DiscoveryTaskInstance visitSector(Entity entity) {
                return discoveryTaskInstance;
            }

            @Override
            public DiscoveryTaskInstance visitMerchant(Entity entity) {
                MerchantEntity merchantEntity = (MerchantEntity) entity;
                discoveryTaskInstance.setBusinessUnit(merchantEntity.getBusinessUnit());
                return discoveryTaskInstance;
            }

            @Override
            public DiscoveryTaskInstance visitNone(Entity entity) {
                return discoveryTaskInstance;
            }

            @Override
            public DiscoveryTaskInstance visitStore(Entity entity) {
                StoreEntity storeEntity = (StoreEntity) entity;
                discoveryTaskInstance.setBusinessUnit(storeEntity.getBusinessUnit());
                return discoveryTaskInstance;
            }

            @Override
            public DiscoveryTaskInstance visitTask(Entity entity) {
                return discoveryTaskInstance;
            }

            @Override
            public DiscoveryTaskInstance visitVpa(Entity entity) {
                return discoveryTaskInstance;
            }

            @Override
            public DiscoveryTaskInstance visitExternal(Entity entity) {
                return discoveryTaskInstance;
            }

            @Override
            public DiscoveryTaskInstance visitAgent(Entity entity) {
                return discoveryTaskInstance;
            }
        }, entity);
    }

    public static DiscoveryTaskInstance dtoToDiscoveryTaskInstance(StoredTaskInstance storedTaskInstance) {
        return dtoToDiscoveryTaskInstance(storedTaskInstance, null);
    }

    public static DiscoveryTaskInstance dtoToDiscoveryTaskInstance(StoredTaskInstance storedTaskInstance,
                                                                   TaskDefinitionInstance taskDefinitionInstance) {

        String assignedTo = null;
        String completedBy = null;
        Long completedOn = null;
        Long verifiedOn = null;

        storedTaskInstance.getTaskTransitions().sort(TaskUtils::taskComparator);

        for (StoredTaskTransition storedTaskTransition : storedTaskInstance.getTaskTransitions()) {
            switch (storedTaskTransition.getToState()) {
                case BOUNDED_ASSIGNED:
                case SELF_ASSIGNED:
                    assignedTo = storedTaskTransition.getActor();
                    break;
                case COMPLETED:
                    completedBy = storedTaskTransition.getActor();
                    assignedTo = completedBy;
                    completedOn = storedTaskTransition.getCreatedAt().getTime();
                    break;
                case VERIFICATION_SUCCESS:
                case VERIFICATION_FAILED:
                    verifiedOn = storedTaskTransition.getCreatedAt().getTime();
                    break;
                default:
                    break;
            }
        }

        DiscoveryTaskInstance.DiscoveryTaskInstanceBuilder discoveryTaskInstanceBuilder = DiscoveryTaskInstance.builder()
                .updatedAt(System.currentTimeMillis())
                .dueDate(storedTaskInstance.getDueDate().getTime())
                .entityId(storedTaskInstance.getEntityId())
                .entityType(storedTaskInstance.getEntityType())
                .taskDefinitionId(storedTaskInstance.getTaskDefinitionId())
                .taskInstanceId(storedTaskInstance.getTaskInstanceId())
                .taskState(storedTaskInstance.getCurState())
                .completedBy(completedBy)
                .assignedTo(assignedTo)
                .completedOn(completedOn)
                .verifiedOn(verifiedOn)
                .campaign(storedTaskInstance.getCampaignId())
                .createdBy(storedTaskInstance.getCreatedBy())
                .updatedBy(storedTaskInstance.getUpdatedBy())
                .active(storedTaskInstance.isActive())
                .points((long) storedTaskInstance.getPoints())
                .rescheduledAt(storedTaskInstance.getRescheduledAt() == null ? 0 : storedTaskInstance.getRescheduledAt().getTime());

        if (Objects.nonNull(storedTaskInstance.getInstanceMeta())) {
            discoveryTaskInstanceBuilder.taskInstanceMeta(SerDe.readValue(storedTaskInstance.getInstanceMeta(),
                    new TypeReference<TaskInstanceMeta>() {
                    }
            ));
        }

        if (Objects.nonNull(taskDefinitionInstance)) {
            discoveryTaskInstanceBuilder.actionId(taskDefinitionInstance.getActionId());

            if (taskDefinitionInstance.getAttributes() != null) {
                discoveryTaskInstanceBuilder.attributes(taskDefinitionInstance.getAttributes());
            }
        }

        return discoveryTaskInstanceBuilder.build();
    }

  public static StoredTaskInstance toStoredTaskInstance(TaskDefinitionInstance taskDefinitionInstance, DiscoveryTaskInstance discoveryTaskInstance, CreateTaskInstanceRequest request) {
    return StoredTaskInstance.builder()
            .actionId(taskDefinitionInstance.getActionId())
            .taskInstanceId(discoveryTaskInstance.getTaskInstanceId())
            .entityId(discoveryTaskInstance.getEntityId())
            .entityType(discoveryTaskInstance.getEntityType())
            .taskDefinitionId(discoveryTaskInstance.getTaskDefinitionId())
            .curState(LegionTaskStateMachineState.INITIATED)
            .curActor(request.getCreatedBy())
            .secondaryIndexSyncRequired(true)
            .dueDate(new Date(discoveryTaskInstance.getDueDate()))
            .active(true)
            .campaignId(request.getCampaignId())
            .partitionId(DateUtils.monthOfYear())
            .points(ObjectUtils.firstNonNull(request.getPoints(),taskDefinitionInstance.getPoints()))
            .instanceMeta(SerDe.writeValueAsBytes(request.getTaskInstanceMeta()))
            .namespace(discoveryTaskInstance.getNamespace())
            .createdBy(request.getCreatedBy())
            .updatedBy(request.getCreatedBy())
            .build();
  }

  public static byte[] toTaskInstanceMeta(TaskMetaUpdateRequest request, byte[] existingInstanceMeta) {
      List<TaskMetaInformation> taskMetaList = request.getUserTaskCreationMeta()
              .stream()
              .filter(meta -> meta.getType().isAllowUpdate())
              .map(meta -> buildTaskMetaInformation(meta.getType(), meta.getValue(), meta.isDisplayInformation()))
              .toList();

      Set<TaskMetaType> updatedTaskMetaTypes = taskMetaList.stream()
              .map(TaskMetaInformation::getType)
              .collect(Collectors.toSet());

      if (!taskMetaList.isEmpty()) {
          TaskInstanceMeta taskMetaToBeUpdated = Optional.ofNullable(existingInstanceMeta)
                  .map(
                          bytes -> {
                              TaskInstanceMeta instanceMeta = SerDe.readValue(bytes, TaskInstanceMeta.class);
                              if (Objects.nonNull(instanceMeta) && Objects.isNull(instanceMeta.getTaskMetaList())) {
                                  instanceMeta.setTaskMetaList(new LinkedList<>());
                              }
                              return instanceMeta;
                          })
                  .orElse(TaskInstanceMeta.builder().taskMetaList(new LinkedList<>()).build());
          taskMetaToBeUpdated.getTaskMetaList().removeIf(taskMetaInformation -> updatedTaskMetaTypes.contains(taskMetaInformation.getType()));
          taskMetaToBeUpdated.getTaskMetaList().addAll(taskMetaList);

          return SerDe.writeValueAsBytes(taskMetaToBeUpdated);
      }
      return existingInstanceMeta;
  }

  public static StoredTaskInstance setDueDateAndRescheduleDate(TaskMetaUpdateRequest request,
                                                 DiscoveryTaskInstance discoveryTaskInstance,
                                                 StoredTaskInstance instance,
                                                 Miscellaneous miscellaneous) {
      if (request.getRescheduleAt() != 0L) {
          Date reschedulingDate = new Date(request.getRescheduleAt());
          instance.setRescheduledAt(reschedulingDate);

          return syncDueDateWithRescheduleDate(
                  instance,
                  reschedulingDate,
                  new Date(discoveryTaskInstance.getDueDate()),
                  miscellaneous);
      }
      return instance;
  }

    public static StoredTaskInstance syncDueDateWithRescheduleDate(StoredTaskInstance taskInstance,
                                                     Date rescheduleDate,
                                                     Date dueDate,
                                                     Miscellaneous miscellaneous) {
        taskInstance.setDueDate(
                rescheduleDate.after(dueDate)
                        ? DateUtils.addDaysToDate(rescheduleDate, miscellaneous.getRescheduleOffsetInDays())
                        : taskInstance.getDueDate());
        return taskInstance;
    }

    public static TaskMetaInformation buildTaskMetaInformation(TaskMetaType type,
                                                                String value,
                                                                boolean displayInformation) {
        return TaskMetaInformation.builder()
                .value(value)
                .type(type)
                .displayInformation(displayInformation)
                .build();
    }

    public static void setSecondaryIndexSyncRequiredFalse(TaskInstanceRepository taskInstanceRepository, String taskInstanceId) {
    taskInstanceRepository.update(taskInstanceId, storedTaskInstance -> {
      storedTaskInstance.setSecondaryIndexSyncRequired(false);
      return storedTaskInstance;
    });
  }

  public static StoredTaskTransition toCreationTransition(StoredTaskInstance storedTaskInstance,
                                                          CreateTaskInstanceRequest request) {
    return StoredTaskTransition.builder()
            .context(SerDe.writeValueAsBytes(request))
            .taskInstanceId(storedTaskInstance.getTaskInstanceId())
            .actor(request.getCreatedBy())
            .toState(LegionTaskStateMachineState.CREATED)
            .fromState(LegionTaskStateMachineState.INITIATED)
            .event(LegionTaskStateMachineEvent.CREATED)
            .taskInstance(storedTaskInstance)
            .partitionId(storedTaskInstance.getPartitionId())
            .transitionId(1)
            .build();
  }

  public static StoredTaskTransition toBoundedAssignmentTransition(StoredTaskInstance storedTaskInstance,
                                                                   TaskAssignRequest taskAssignRequest) {
    return StoredTaskTransition.builder()
            .toState(LegionTaskStateMachineState.BOUNDED_ASSIGNED)
            .fromState(storedTaskInstance.getCurState())
            .event(LegionTaskStateMachineEvent.ASSIGNMENT)
            .transitionId(storedTaskInstance.lastTransition().getTransitionId() + 1)
            .actor(taskAssignRequest.getAssignedTo())
            .partitionId(storedTaskInstance.getPartitionId())
            .context(SerDe.writeValueAsBytes(taskAssignRequest))
            .taskInstanceId(storedTaskInstance.getTaskInstanceId())
            .taskInstance(storedTaskInstance)
            .build();
  }


  public static StoredTaskTransition toSelfAssignmentTransition(StoredTaskInstance storedTaskInstance,
                                                                TaskAssignRequest taskAssignRequest) {
    return StoredTaskTransition.builder()
            .toState(LegionTaskStateMachineState.SELF_ASSIGNED)
            .fromState(storedTaskInstance.getCurState())
            .event(LegionTaskStateMachineEvent.ASSIGNMENT)
            .transitionId(storedTaskInstance.lastTransition().getTransitionId() + 1)
            .actor(taskAssignRequest.getAssignedTo())
            .partitionId(storedTaskInstance.getPartitionId())
            .context(SerDe.writeValueAsBytes(taskAssignRequest))
            .taskInstance(storedTaskInstance)
            .taskInstanceId(storedTaskInstance.getTaskInstanceId())
            .build();
  }

  public static TaskInstance toTaskInstance(StoredTaskInstance storedTaskInstance) {
      if(storedTaskInstance == null){
          return null;
      }
    return TaskInstance.builder()
            .actionId(storedTaskInstance.getActionId())
            .createdAt(storedTaskInstance.getCreatedAt())
            .updatedAt(storedTaskInstance.getUpdatedAt())
            .entityId(storedTaskInstance.getEntityId())
            .entityType(storedTaskInstance.getEntityType())
            .secondaryIndexSyncRequired(storedTaskInstance.isSecondaryIndexSyncRequired())
            .curState(storedTaskInstance.getCurState())
            .active(storedTaskInstance.isActive())
            .namespace(storedTaskInstance.getNamespace())
            .partitionId(storedTaskInstance.getPartitionId())
            .dueDate(storedTaskInstance.getDueDate())
            .rescheduledAt(storedTaskInstance.getRescheduledAt())
            .instanceMeta(SerDe.readValue(storedTaskInstance.getInstanceMeta(),
                    new TypeReference<TaskInstanceMeta>() {}))
            .taskDefinitionId(storedTaskInstance.getTaskDefinitionId())
            .taskInstanceId(storedTaskInstance.getTaskInstanceId())
            .taskTransitions(null != storedTaskInstance.getTaskTransitions() ?
                    storedTaskInstance.getTaskTransitions().stream()
                            .map(TaskInstanceTransformationUtils::toTaskTransition)
                            .collect(Collectors.toList()) : Collections.emptyList())
            .points(storedTaskInstance.getPoints())
            .curActor(storedTaskInstance.getCurActor())
            .campaign(storedTaskInstance.getCampaignId())
            .createdBy(storedTaskInstance.getCreatedBy())
            .updatedBy(storedTaskInstance.getUpdatedBy())
            .build();
  }

    public static TaskTransition toTaskTransition(StoredTaskTransition storedTaskTransition) {
    return TaskTransition.builder()
            .actor(storedTaskTransition.getActor())
            .context(SerDe.readValue(storedTaskTransition.getContext(), new TypeReference<Map<String, Object>>() {}))
            .createdAt(storedTaskTransition.getCreatedAt())
            .updatedAt(storedTaskTransition.getUpdatedAt())
            .expiredAt(storedTaskTransition.getExpiredAt())
            .taskInstanceId(storedTaskTransition.getTaskInstanceId())
            .toState(storedTaskTransition.getToState())
            .fromState(storedTaskTransition.getFromState())
            .event(storedTaskTransition.getEvent())
            .transitionId(storedTaskTransition.getTransitionId())
            .build();
  }

  public static StoredTaskTransition toMarkAvailableTransition(StoredTaskInstance storedTaskInstance,
                                                               TaskMarkAvailableRequest request) {
    return StoredTaskTransition.builder()
            .toState(LegionTaskStateMachineState.AVAILABLE)
            .fromState(storedTaskInstance.getCurState())
            .event(LegionTaskStateMachineEvent.AVAILABLE)
            .taskInstance(storedTaskInstance)
            .transitionId(storedTaskInstance.lastTransition().getTransitionId() + 1)
            .actor(request.getMarkedBy())
            .partitionId(storedTaskInstance.getPartitionId())
            .context(SerDe.writeValueAsBytes(request))
            .taskInstanceId(storedTaskInstance.getTaskInstanceId())
            .build();
  }

  public static StoredTaskTransition toVerificationTransition(ActionVerificationResponse response, TaskCompleteRequest data) {
    return StoredTaskTransition.builder()
            .taskInstance(data.getStoredTaskInstance())
            .taskInstanceId(data.getTaskInstanceId())
            .transitionId(data.getStoredTaskInstance().lastTransition().getTransitionId() + 1)
            .actor(data.getCompletedBy())
            .toState(Boolean.TRUE.equals(response.getVerified()) ? LegionTaskStateMachineState.VERIFICATION_SUCCESS :
                    LegionTaskStateMachineState.VERIFICATION_FAILED)
            .fromState(data.getStoredTaskInstance().getCurState())
            .event(LegionTaskStateMachineEvent.VERIFICATION_EXECUTE)
            .context(SerDe.writeValueAsBytes(response.getContext()))
            .partitionId(data.getStoredTaskInstance().getPartitionId())
            .build();
  }

  public static UnaryOperator<StoredTaskInstance> update(TaskInstance request) {
    return storedTaskInstance ->  {
          storedTaskInstance.setSecondaryIndexSyncRequired(request.getSecondaryIndexSyncRequired());
          storedTaskInstance.setInstanceMeta(SerDe.writeValueAsBytes(request.getInstanceMeta()));
          storedTaskInstance.setPoints(request.getPoints());
          return storedTaskInstance;
        };
    }

    public static UnaryOperator<StoredTaskInstance> delete() {
      return storedTaskInstance -> {
          storedTaskInstance.setActive(false);
          return storedTaskInstance;
        };
  }

  public static SectorTaskSearchResponse toSectorTaskList(List<DiscoveryTaskInstance> discoveryTaskInstances, long totalTaskCount){
    List<TaskMapViewDetails> taskMapViewDetailsList = discoveryTaskInstances.stream()
            .filter(Objects::nonNull)
            .filter(discoveryTaskInstance -> Objects.nonNull(discoveryTaskInstance.getLocation()))
            .map(TaskMapViewDetails::new)
            .toList();
    return SectorTaskSearchResponse.builder()
            .taskCount(totalTaskCount)
            .taskDetails(taskMapViewDetailsList)
            .build();
  }

  public static TaskCreateRequest toTaskCreateRequest(StoredTaskInstance storedTaskInstance, StoredTaskDefinition storedTaskDefinition, boolean markAvailable, TaskInstanceMeta instanceMeta) {
      return TaskCreateRequest.builder()
              .taskInstance(CreateTaskInstanceRequest.builder()
                      .taskDefinitionId(storedTaskInstance.getTaskDefinitionId())
                      .campaignId(storedTaskInstance.getCampaignId())
                      .createdBy(storedTaskDefinition.getCreatedBy())
                      .entityId(storedTaskInstance.getEntityId())
                      .points(storedTaskInstance.getPoints())
                      .taskInstanceMeta(instanceMeta)
                      .build())
              .markAvailable(markAvailable)
              .build();
  }

  public static CreateTaskInstanceRequest toCreateTaskInstanceRequest(ClientTaskCreateAndAssignRequest request) {
      return CreateTaskInstanceRequest.builder()
              .taskDefinitionId(request.getTaskDefinitionId())
              .campaignId(request.getCampaignId())
              .entityId(request.getEntityId())
              .createdBy(request.getCreatedBy())
              .taskInstanceMeta(request.getTaskInstanceMeta())
              .transactionLocation(request.getTransactionLocation())
              .build();
  }

    public static CreateTaskInstanceRequest toCreateTaskInstanceRequest(String createdBy,
                                                                        TaskParams taskParams,
                                                                        String remark,
                                                                        String leadIntent) {
        return CreateTaskInstanceRequest.builder()
                .taskDefinitionId(taskParams.getDefinitionId())
                .campaignId(taskParams.getCampaignId())
                .entityId(taskParams.getEntityId())
                .createdBy(createdBy)
                .taskInstanceMeta(getTaskInstanceMeta(remark, leadIntent))
                .build();
    }

    private static TaskInstanceMeta getTaskInstanceMeta(String remark, String leadIntent) {
        TaskInstanceMeta taskInstanceMeta = TaskInstanceMeta.builder()
                .taskMetaList(new ArrayList<>()).build();
        addTaskInstanceMeta(TaskMetaType.REMARK, remark, false, taskInstanceMeta);
        addTaskInstanceMeta(TaskMetaType.LEAD_INTENT, leadIntent, true, taskInstanceMeta);
        return taskInstanceMeta;
    }

    private static void addTaskInstanceMeta(TaskMetaType taskMetaType,
                                            String value,
                                            boolean displayInformation,
                                            TaskInstanceMeta taskInstanceMeta) {
        if (StringUtils.isNotEmpty(value)) {
            taskInstanceMeta.getTaskMetaList().add(TaskMetaInformation.builder()
                    .displayInformation(displayInformation)
                    .type(taskMetaType)
                    .value(value)
                    .build());
        }
    }

    public static StoredTaskTransition toMarkExpiredTransition(StoredTaskInstance storedTaskInstance,
                                                                 TaskExpireRequest request) {
        return StoredTaskTransition.builder()
                .toState(LegionTaskStateMachineState.EXPIRED)
                .fromState(storedTaskInstance.getCurState())
                .event(LegionTaskStateMachineEvent.MARK_TASK_EXPIRED)
                .taskInstance(storedTaskInstance)
                .transitionId(storedTaskInstance.lastTransition().getTransitionId() + 1)
                .actor(request.getMarkedBy())
                .partitionId(storedTaskInstance.getPartitionId())
                .context(SerDe.writeValueAsBytes(request))
                .taskInstanceId(storedTaskInstance.getTaskInstanceId())
                .build();
    }

    public static StoredTaskTransition toMarkDeletedTransition(StoredTaskInstance storedTaskInstance,
                                                               ClientTaskDeleteRequest request) {
        return StoredTaskTransition.builder()
                .toState(LegionTaskStateMachineState.DELETED)
                .fromState(storedTaskInstance.getCurState())
                .event(LegionTaskStateMachineEvent.MARK_TASK_DELETED)
                .taskInstance(storedTaskInstance)
                .transitionId(storedTaskInstance.lastTransition().getTransitionId() + 1)
                .actor(request.getDeletedBy())
                .partitionId(storedTaskInstance.getPartitionId())
                .context(SerDe.writeValueAsBytes(request))
                .taskInstanceId(storedTaskInstance.getTaskInstanceId())
                .build();
    }

    public static TaskDetailResponse discoveryTaskInstanceToTaskDetails(DiscoveryTaskInstance discoveryTaskInstance, TaskActionInstance taskActionInstance, TaskDefinitionInstance taskDefinitionInstance, TaskInstance taskInstance, List<TaskAttributeInstance> taskDefinitionAttributes) {
        TaskMetaResponse metaResponse = discoveryTaskInstanceToTaskMeta(discoveryTaskInstance, taskDefinitionInstance, taskInstance);
        TaskDetailResponse taskDetailResponse = TaskDetailResponse.builder().taskSteps(getSteps(taskActionInstance, taskDefinitionInstance)).build();
        try {
            BeanUtils.copyProperties(taskDetailResponse, metaResponse);
        } catch (IllegalAccessException | InvocationTargetException e) {
            throw new RuntimeException(e);
        }
        taskDetailResponse.setSteps(null);
        taskDetailResponse.setTaskDefinitionAttributes(taskDefinitionAttributes);
        return taskDetailResponse;
    }

    public static TaskParams buildTaskParams(UserTaskCreationRequest data,
                                             TaskCreationConfigParameters taskCreationConfigParameters) {
      return TaskParams.builder()
              .entityId(String.join("_", data.getMerchantId(), data.getStoreId()))
              .definitionId(taskCreationConfigParameters.getDefinitionId())
              .campaignId(taskCreationConfigParameters.getCampaignId())
              .build();
    }

    /**
        Keeping the logic of overridding all attribute on definition
     */
    private static Map<String, Set<String>> mergeAttribute(Map<String, Set<String>> taskActionAttributes,
                                                           Map<String, Set<String>> taskDefinitionAttributes) {

        if (taskDefinitionAttributes == null && taskActionAttributes == null) {
            return Collections.emptyMap();
        } else if (taskDefinitionAttributes == null) {
            return taskActionAttributes;
        } else if (taskActionAttributes == null) {
            return taskDefinitionAttributes;
        }

        Set<String> attributeTypes = new HashSet<>(taskActionAttributes.keySet());
        attributeTypes.addAll(taskDefinitionAttributes.keySet());

        return attributeTypes.stream().collect(Collectors.toMap(
                key -> key,
                value -> taskDefinitionAttributes.containsKey(value) ? taskDefinitionAttributes.get(value) : taskActionAttributes.get(value)
        ));
    }

    public static LeadConfig toLeadConfig() {
        return LeadConfig.builder()
                .leadCreation(LeadManagementConfiguration.getLeadCreation())
                .leadUpdation(LeadManagementConfiguration.getLeadUpdation())
                .build();
    }

    public static Map<String, Integer> getPriorityTasksFrequencyMap(SearchHit[] searchHits, Set<String> prioritizedTaskDefinitions) {
        Map<String, Integer> taskDefinitionFrequencyMap = new HashMap<>();
        if(searchHits != null && !Arrays.stream(searchHits).toList().isEmpty()) {
            Arrays.stream(searchHits).forEach(searchHit -> {
                DiscoveryTaskInstance task = SerDe.readValue(searchHit.getSourceAsString(), DiscoveryTaskInstance.class);
                if(prioritizedTaskDefinitions.contains(task.getTaskDefinitionId())){
                    taskDefinitionFrequencyMap.put(PRIORITY_TASK_COUNT, taskDefinitionFrequencyMap.getOrDefault(PRIORITY_TASK_COUNT, 0) + 1);
                }
                else {
                    taskDefinitionFrequencyMap.put(NON_PRIORITY_TASK_COUNT, taskDefinitionFrequencyMap.getOrDefault(NON_PRIORITY_TASK_COUNT,0) + 1);
                }
            });
        }
        return taskDefinitionFrequencyMap;
    }
}
