package com.phonepe.merchant.legion.tasks;

import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import com.google.inject.TypeLiteral;
import com.phonepe.frontend.chimera.lite.ChimeraLite;
import com.phonepe.frontend.chimera.lite.ChimeraLiteBundle;
import com.phonepe.merchant.gladius.models.entitystore.ActorMessageType;
import com.phonepe.merchant.gladius.models.hotspots.storage.StoredHotspot;
import com.phonepe.merchant.gladius.models.hotspots.storage.StoredHotspotConfig;
import com.phonepe.merchant.gladius.models.survey.storage.StoredFeedback;
import com.phonepe.merchant.gladius.models.survey.storage.StoredFormConfig;
import com.phonepe.merchant.gladius.models.tags.StoreTag;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskAssignRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCompleteRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskStartRequest;
import com.phonepe.merchant.gladius.models.tasks.request.filter.AssignedLocationTaskFilterRequest;
import com.phonepe.merchant.gladius.models.tasks.request.filter.AssignedSectorTaskFilterRequest;
import com.phonepe.merchant.gladius.models.tasks.request.filter.DiscoveryLocationTaskFilterRequest;
import com.phonepe.merchant.gladius.models.tasks.request.filter.DiscoverySectorTaskFilterRequest;
import com.phonepe.merchant.gladius.models.tasks.request.filter.EscalationViewTaskFilterRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.AssignedViewTaskFetchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.DiscoveryViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.EscalatedViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.SectorDiscoveryViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.geopolygon.GeoFenceRemappingSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.map.AssignedSectorMapViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.map.DiscoverySectorMapViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredActionAttributeMappings;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredCampaign;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredCommentsOnTask;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredDefinitionAttributeMappings;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskAction;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskAttribute;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskDefinition;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskTransition;
import com.phonepe.merchant.gladius.models.tasks.utils.LeadCreationConfig;
import com.phonepe.merchant.gladius.models.tasks.utils.Miscellaneous;
import com.phonepe.merchant.gladius.models.tasks.utils.UserTaskCreationConfig;
import com.phonepe.merchant.legion.core.AppConfig;
import com.phonepe.merchant.legion.core.config.DiscoveryViewRestrictionConfig;
import com.phonepe.merchant.legion.core.daos.AuditDao;
import com.phonepe.merchant.legion.core.daos.AuditDaoImpl;
import com.phonepe.merchant.legion.core.flows.factories.LegionTransitionManager;
import com.phonepe.merchant.legion.core.flows.factories.LegionTransitionManagerImpl;
import com.phonepe.merchant.legion.tasks.actions.processor.ActionValidationProcessor;
import com.phonepe.merchant.legion.tasks.actions.processor.ActionVerificationProcessor;
import com.phonepe.merchant.legion.tasks.actions.processor.impl.ActionVerificationProcessorImpl;
import com.phonepe.merchant.legion.tasks.actions.processor.impl.AssignmentActionValidationProcessor;
import com.phonepe.merchant.legion.tasks.actions.processor.impl.CompletionActionValidationProcessor;
import com.phonepe.merchant.legion.tasks.actions.processor.impl.StartActionValidationProcessor;
import com.phonepe.merchant.legion.tasks.actor.annotations.ClientTaskCreateAndAssignActorConfig;
import com.phonepe.merchant.legion.tasks.actor.annotations.ClientTaskDeleteActorConfig;
import com.phonepe.merchant.legion.tasks.actor.annotations.ClientTaskVerificationActorConfig;
import com.phonepe.merchant.legion.tasks.actor.annotations.CommentCreationPerDayConfig;
import com.phonepe.merchant.legion.tasks.actor.annotations.EventBasedTaskCreationActorConfig;
import com.phonepe.merchant.legion.tasks.actor.annotations.HotspotSyncActorConfig;
import com.phonepe.merchant.legion.tasks.actor.annotations.QcTaskCreateAndAssignActorConfig;
import com.phonepe.merchant.legion.tasks.actor.annotations.TaskEsChangeEventActorConfig;
import com.phonepe.merchant.legion.tasks.actor.annotations.TaskEsRetryRmqActorConfig;
import com.phonepe.merchant.legion.tasks.actor.annotations.TaskRecreationScheduleActorConfig;
import com.phonepe.merchant.legion.tasks.actor.annotations.TaskVerifyRmqActorConfig;
import com.phonepe.merchant.legion.tasks.actor.annotations.TaskVerifyScheduleActorConfig;
import com.phonepe.merchant.legion.tasks.bindings.filter.AssignedSectorTaskFilterRequestQueryBuilderProvider;
import com.phonepe.merchant.legion.tasks.bindings.filter.AssignedTaskFilterRequestQueryBuilderProvider;
import com.phonepe.merchant.legion.tasks.bindings.filter.DiscoverySectorFilterQueryGeneratorProvider;
import com.phonepe.merchant.legion.tasks.bindings.filter.DiscoveryTaskFilterRequestQueryBuilderProvider;
import com.phonepe.merchant.legion.tasks.bindings.filter.EscalationViewFilterQueryGeneratorProvider;
import com.phonepe.merchant.legion.tasks.bindings.filter.views.AssignedViewWiseFiltersProvider;
import com.phonepe.merchant.legion.tasks.bindings.filter.views.DiscoveryViewWiseFiltersProvider;
import com.phonepe.merchant.legion.tasks.bindings.listing.AgentTaskEligibilityEnricherProvider;
import com.phonepe.merchant.legion.tasks.bindings.listing.AllTaskListingEnricherProvider;
import com.phonepe.merchant.legion.tasks.bindings.listing.AssignedViewTaskListingEnricherProvider;
import com.phonepe.merchant.legion.tasks.bindings.listing.DiscoveryViewTaskListingEnricherProvider;
import com.phonepe.merchant.legion.tasks.bindings.listing.EntityViewListingEnricherProvider;
import com.phonepe.merchant.legion.tasks.bindings.listing.TaskTypeListingEnricherProvider;
import com.phonepe.merchant.legion.tasks.bindings.mapview.AssignedMapViewTaskSearchRequestQueryBuilderProvider;
import com.phonepe.merchant.legion.tasks.bindings.mapview.DiscoveryMapViewTaskSearchRequestQueryBuilderProvider;
import com.phonepe.merchant.legion.tasks.bindings.search.AssignedViewTaskSearchRequestQueryBuilderProvider;
import com.phonepe.merchant.legion.tasks.bindings.search.DiscoveryViewTaskSearchRequestQueryBuilderProvider;
import com.phonepe.merchant.legion.tasks.bindings.search.EscalatedViewTaskSearchRequestQueryBuilderProvider;
import com.phonepe.merchant.legion.tasks.bindings.search.SectorDiscoveryViewTaskSearchRequestQueryBuilderProvider;
import com.phonepe.merchant.legion.tasks.flows.TaskEngine;
import com.phonepe.merchant.legion.tasks.flows.TaskEngineImpl;
import com.phonepe.merchant.legion.tasks.repository.CampaignRepository;
import com.phonepe.merchant.legion.tasks.repository.CommentsOnTaskRepository;
import com.phonepe.merchant.legion.tasks.repository.FeedbackRepository;
import com.phonepe.merchant.legion.tasks.repository.FormConfigRepository;
import com.phonepe.merchant.legion.tasks.repository.HotspotConfigRepository;
import com.phonepe.merchant.legion.tasks.repository.HotspotRepository;
import com.phonepe.merchant.legion.tasks.repository.StoredTagRepository;
import com.phonepe.merchant.legion.tasks.repository.TaskActionRepository;
import com.phonepe.merchant.legion.tasks.repository.TaskAttributeRepository;
import com.phonepe.merchant.legion.tasks.repository.TaskDefinitionRepository;
import com.phonepe.merchant.legion.tasks.repository.TaskESRepository;
import com.phonepe.merchant.legion.tasks.repository.TaskInstanceRepository;
import com.phonepe.merchant.legion.tasks.repository.TaskTransitionRepository;
import com.phonepe.merchant.legion.tasks.repository.impl.CampaignRepositoryImpl;
import com.phonepe.merchant.legion.tasks.repository.impl.CommentsOnTaskRepositoryImpl;
import com.phonepe.merchant.legion.tasks.repository.impl.FeedbackRepositoryImpl;
import com.phonepe.merchant.legion.tasks.repository.impl.FormConfigRepositoryImpl;
import com.phonepe.merchant.legion.tasks.repository.impl.HotspotConfigRepositoryImpl;
import com.phonepe.merchant.legion.tasks.repository.impl.HotspotRepositoryImpl;
import com.phonepe.merchant.legion.tasks.repository.impl.StoredTagRepositoryImpl;
import com.phonepe.merchant.legion.tasks.repository.impl.TaskActionRepositoryImpl;
import com.phonepe.merchant.legion.tasks.repository.impl.TaskAttributeRepositoryImpl;
import com.phonepe.merchant.legion.tasks.repository.impl.TaskDefinitionRepositoryImpl;
import com.phonepe.merchant.legion.tasks.repository.impl.TaskESRepositoryImpl;
import com.phonepe.merchant.legion.tasks.repository.impl.TaskInstanceRepositoryImpl;
import com.phonepe.merchant.legion.tasks.repository.impl.TaskTransitionRepositoryImpl;
import com.phonepe.merchant.legion.tasks.search.query.filter.AssignedTaskFilterRequestQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.filter.DiscoveryTaskFilterRequestQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.filter.EscalationViewTaskFilterRequestQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.filter.SectorAssignedTaskFilterRequestQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.filter.SectorDiscoveryTaskFilterRequestQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.filter.TaskFilterRequestQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.geopolygon.GeoPolygonQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.geopolygon.RemappingGeoPolygonQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.listing.AgentTaskEligibilityEnricher;
import com.phonepe.merchant.legion.tasks.search.query.listing.AssignedViewTaskListingEnricher;
import com.phonepe.merchant.legion.tasks.search.query.listing.DiscoveryViewTaskListingEnricher;
import com.phonepe.merchant.legion.tasks.search.query.listing.EntityHistoryViewTaskListingEnricher;
import com.phonepe.merchant.legion.tasks.search.query.listing.EntityTaskListingEnricher;
import com.phonepe.merchant.legion.tasks.search.query.listing.EntityViewTaskListingEnricher;
import com.phonepe.merchant.legion.tasks.search.query.listing.RequestViewTaskListingEnricher;
import com.phonepe.merchant.legion.tasks.search.query.listing.TaskTypeListingQueryEnricher;
import com.phonepe.merchant.legion.tasks.search.query.mapview.AssignedMapViewTaskSearchRequestQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.mapview.DiscoveryMapViewTaskSearchRequestQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.mapview.SectorMapViewTaskSearchRequestQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.search.AssignedViewTaskSearchRequestQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.search.DiscoveryViewTaskSearchRequestQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.search.EscalatedViewTaskSearchRequestQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.search.SectorDiscoveryViewTaskSearchRequestQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.query.search.TaskSearchRequestQueryBuilder;
import com.phonepe.merchant.legion.tasks.search.response.filter.GenerateAssignedViewWiseFilters;
import com.phonepe.merchant.legion.tasks.search.response.filter.GenerateDiscoveryViewWiseFilters;
import com.phonepe.merchant.legion.tasks.search.response.filter.RequestViewWiseFilter;
import com.phonepe.merchant.legion.tasks.services.BrickbatFormService;
import com.phonepe.merchant.legion.tasks.services.CampaignService;
import com.phonepe.merchant.legion.tasks.services.CategoryInsightService;
import com.phonepe.merchant.legion.tasks.services.ClientTaskService;
import com.phonepe.merchant.legion.tasks.services.CommentsService;
import com.phonepe.merchant.legion.tasks.services.EntityHistoryService;
import com.phonepe.merchant.legion.tasks.services.FeedbackService;
import com.phonepe.merchant.legion.tasks.services.FormConfigService;
import com.phonepe.merchant.legion.tasks.services.HotspotConfigService;
import com.phonepe.merchant.legion.tasks.services.HotspotService;
import com.phonepe.merchant.legion.tasks.services.SectorStatsService;
import com.phonepe.merchant.legion.tasks.services.TagService;
import com.phonepe.merchant.legion.tasks.services.TaskActionService;
import com.phonepe.merchant.legion.tasks.services.TaskAttributeService;
import com.phonepe.merchant.legion.tasks.services.TaskDefinitionService;
import com.phonepe.merchant.legion.tasks.services.TaskDiscoveryService;
import com.phonepe.merchant.legion.tasks.services.TaskDuplicateValidationService;
import com.phonepe.merchant.legion.tasks.services.TaskFilterService;
import com.phonepe.merchant.legion.tasks.services.TaskHousekeepingService;
import com.phonepe.merchant.legion.tasks.services.TaskInstanceHistoryService;
import com.phonepe.merchant.legion.tasks.services.TaskInstanceManagementService;
import com.phonepe.merchant.legion.tasks.services.TaskManagementService;
import com.phonepe.merchant.legion.tasks.services.TaskRecreationService;
import com.phonepe.merchant.legion.tasks.services.TaskVerificationService;
import com.phonepe.merchant.legion.tasks.services.ValidationService;
import com.phonepe.merchant.legion.tasks.services.impl.BrickbatFormServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.CampaignServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.CategoryInsightServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.ClientTaskServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.CommentsServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.EntityHistoryServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.FeedbackServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.FormConfigServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.HotspotConfigServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.HotspotServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.SectorStatsServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.TagServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.TaskActionServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.TaskAttributeServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.TaskDefinitionServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.TaskDiscoveryServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.TaskDuplicateValidationServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.TaskFilterServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.TaskHousekeepingServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.TaskInstanceHistoryServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.TaskInstanceManagementServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.TaskManagementServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.TaskRecreationServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.TaskVerificationServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.ValidationServiceImpl;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.RabbitmqActorBundle;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.sharding.DBShardingBundle;
import io.appform.dropwizard.sharding.dao.LookupDao;
import io.appform.dropwizard.sharding.dao.RelationalDao;
import io.appform.dropwizard.sharding.sharding.impl.ConsistentHashBucketIdExtractor;
import io.appform.dropwizard.sharding.utils.ShardCalculator;
import lombok.AllArgsConstructor;



@AllArgsConstructor
public abstract class TaskModule extends AbstractModule {

    private final DBShardingBundle<AppConfig> shardingBundle;
    private final RabbitmqActorBundle<AppConfig> rabbitmqActorBundle;
    private final ChimeraLiteBundle<AppConfig> chimeraLiteBundle;

    @Override
    protected void configure() {
        bind(TaskDefinitionRepository.class).to(TaskDefinitionRepositoryImpl.class);
        bind(TaskActionRepository.class).to(TaskActionRepositoryImpl.class);
        bind(TaskTransitionRepository.class).to(TaskTransitionRepositoryImpl.class);
        bind(TaskInstanceRepository.class).to(TaskInstanceRepositoryImpl.class);
        bind(CampaignRepository.class).to(CampaignRepositoryImpl.class);
        bind(HotspotRepository.class).to(HotspotRepositoryImpl.class);
        bind(TaskDiscoveryService.class).to(TaskDiscoveryServiceImpl.class);
        bind(SectorStatsService.class).to(SectorStatsServiceImpl.class);
        bind(TaskAttributeRepository.class).to(TaskAttributeRepositoryImpl.class);
        bind(CommentsOnTaskRepository.class).to(CommentsOnTaskRepositoryImpl.class);
        bind(TaskAttributeService.class).to(TaskAttributeServiceImpl.class);
        bind(HotspotConfigRepository.class).to(HotspotConfigRepositoryImpl.class);

        bind(TaskManagementService.class).to(TaskManagementServiceImpl.class);
        bind(TaskRecreationService.class).to(TaskRecreationServiceImpl.class);
        bind(TaskDefinitionService.class).to(TaskDefinitionServiceImpl.class);
        bind(TaskInstanceManagementService.class).to(TaskInstanceManagementServiceImpl.class);
        bind(TaskInstanceHistoryService.class).to(TaskInstanceHistoryServiceImpl.class);
        bind(EntityHistoryService.class).to(EntityHistoryServiceImpl.class);

        bind(ClientTaskService.class).to(ClientTaskServiceImpl.class);
        bind(TaskEngine.class).to(TaskEngineImpl.class);
        bind(LegionTransitionManager.class).to(LegionTransitionManagerImpl.class);
        bind(CommentsService.class).to(CommentsServiceImpl.class);
        bind(HotspotService.class).to(HotspotServiceImpl.class);
        bind(HotspotConfigService.class).to(HotspotConfigServiceImpl.class);

        bind(TaskESRepository.class).to(TaskESRepositoryImpl.class);
        bind(TaskVerificationService.class).to(TaskVerificationServiceImpl.class);
        bind(TaskActionService.class).to(TaskActionServiceImpl.class);
        bind(CampaignService.class).to(CampaignServiceImpl.class);
        bind(TaskHousekeepingService.class).to(TaskHousekeepingServiceImpl.class);
        bind(StoredTagRepository.class).to(StoredTagRepositoryImpl.class);
        bind(TagService.class).to(TagServiceImpl.class);
        bind(ValidationService.class).to(ValidationServiceImpl.class);
        bind(BrickbatFormService.class).to(BrickbatFormServiceImpl.class);
        bind(TaskFilterService.class).to(TaskFilterServiceImpl.class);
        bind(ActionVerificationProcessor.class).to(ActionVerificationProcessorImpl.class);
        bind(TaskDuplicateValidationService.class).to(TaskDuplicateValidationServiceImpl.class);
        bind(CategoryInsightService.class).to(CategoryInsightServiceImpl.class);
        bind(FeedbackRepository.class).to(FeedbackRepositoryImpl.class);
        bind(FormConfigRepository.class).to(FormConfigRepositoryImpl.class);
        bind(FeedbackService.class).to(FeedbackServiceImpl.class);
        bind(FormConfigService.class).to(FormConfigServiceImpl.class);

        bind(new TypeLiteral<ActionValidationProcessor<TaskStartRequest>>() {
        }).annotatedWith(com.phonepe.merchant.legion.tasks.annotations.StartActionValidationProcessor.class).to(StartActionValidationProcessor.class);
        bind(new TypeLiteral<ActionValidationProcessor<TaskCompleteRequest>>() {
        }).annotatedWith(com.phonepe.merchant.legion.tasks.annotations.CompletionActionValidationProcessor.class).to(CompletionActionValidationProcessor.class);
        bind(new TypeLiteral<ActionValidationProcessor<TaskStartRequest>>() {
        }).annotatedWith(com.phonepe.merchant.legion.tasks.annotations.StartActionValidationProcessor.class).to(StartActionValidationProcessor.class);
        bind(new TypeLiteral<ActionValidationProcessor<TaskCompleteRequest>>() {
        }).annotatedWith(com.phonepe.merchant.legion.tasks.annotations.CompletionActionValidationProcessor.class).to(CompletionActionValidationProcessor.class);
        bind(new TypeLiteral<ActionValidationProcessor<TaskAssignRequest>>() {
        }).annotatedWith(com.phonepe.merchant.legion.tasks.annotations.AssignmentActionValidatorProcessor.class).to(AssignmentActionValidationProcessor.class);

        bind(new TypeLiteral<TaskFilterRequestQueryBuilder<DiscoveryLocationTaskFilterRequest>>() {
        }).annotatedWith(DiscoveryTaskFilterRequestQueryBuilderProvider.class).to(DiscoveryTaskFilterRequestQueryBuilder.class);
        bind(new TypeLiteral<TaskFilterRequestQueryBuilder<AssignedLocationTaskFilterRequest>>() {
        }).annotatedWith(AssignedTaskFilterRequestQueryBuilderProvider.class).to(AssignedTaskFilterRequestQueryBuilder.class);
        bind(new TypeLiteral<TaskFilterRequestQueryBuilder<DiscoverySectorTaskFilterRequest>>() {
        }).annotatedWith(DiscoverySectorFilterQueryGeneratorProvider.class).to(SectorDiscoveryTaskFilterRequestQueryBuilder.class);
        bind(new TypeLiteral<TaskFilterRequestQueryBuilder<EscalationViewTaskFilterRequest>>() {
        }).annotatedWith(EscalationViewFilterQueryGeneratorProvider.class).to(EscalationViewTaskFilterRequestQueryBuilder.class);
        bind(new TypeLiteral<TaskFilterRequestQueryBuilder<AssignedSectorTaskFilterRequest>>() {
        }).annotatedWith(AssignedSectorTaskFilterRequestQueryBuilderProvider.class).to(SectorAssignedTaskFilterRequestQueryBuilder.class);

        bind(new TypeLiteral<SectorMapViewTaskSearchRequestQueryBuilder<AssignedSectorMapViewTaskSearchRequest>>() {
        }).annotatedWith(AssignedMapViewTaskSearchRequestQueryBuilderProvider.class)
                .to(AssignedMapViewTaskSearchRequestQueryBuilder.class);
        bind(new TypeLiteral<SectorMapViewTaskSearchRequestQueryBuilder<DiscoverySectorMapViewTaskSearchRequest>>() {
        }).annotatedWith(DiscoveryMapViewTaskSearchRequestQueryBuilderProvider.class)
                .to(DiscoveryMapViewTaskSearchRequestQueryBuilder.class);

        bind(new TypeLiteral<TaskSearchRequestQueryBuilder<SectorDiscoveryViewTaskSearchRequest>>() {
        }).annotatedWith(SectorDiscoveryViewTaskSearchRequestQueryBuilderProvider.class)
                .to(SectorDiscoveryViewTaskSearchRequestQueryBuilder.class);
        bind(new TypeLiteral<TaskSearchRequestQueryBuilder<DiscoveryViewTaskSearchRequest>>() {
        }).annotatedWith(DiscoveryViewTaskSearchRequestQueryBuilderProvider.class)
                .to(DiscoveryViewTaskSearchRequestQueryBuilder.class);
        bind(new TypeLiteral<TaskSearchRequestQueryBuilder<AssignedViewTaskFetchRequest>>() {
        }).annotatedWith(AssignedViewTaskSearchRequestQueryBuilderProvider.class)
                .to(AssignedViewTaskSearchRequestQueryBuilder.class);
        bind(new TypeLiteral<TaskSearchRequestQueryBuilder<EscalatedViewTaskSearchRequest>>() {
        }).annotatedWith(EscalatedViewTaskSearchRequestQueryBuilderProvider.class)
                .to(EscalatedViewTaskSearchRequestQueryBuilder.class);


        bind(RequestViewWiseFilter.class).annotatedWith(DiscoveryViewWiseFiltersProvider.class)
                .to(GenerateDiscoveryViewWiseFilters.class);
        bind(RequestViewWiseFilter.class).annotatedWith(AssignedViewWiseFiltersProvider.class)
                .to(GenerateAssignedViewWiseFilters.class);

        bind(RequestViewTaskListingEnricher.class)
                .annotatedWith(AssignedViewTaskListingEnricherProvider.class)
                .to(AssignedViewTaskListingEnricher.class);
        bind(RequestViewTaskListingEnricher.class)
                .annotatedWith(DiscoveryViewTaskListingEnricherProvider.class)
                .to(DiscoveryViewTaskListingEnricher.class);
        bind(RequestViewTaskListingEnricher.class)
                .annotatedWith(AllTaskListingEnricherProvider.class)
                .to(EntityTaskListingEnricher.class);
        bind(RequestViewTaskListingEnricher.class)
                .annotatedWith(TaskTypeListingEnricherProvider.class)
                .to(TaskTypeListingQueryEnricher.class);
        bind(RequestViewTaskListingEnricher.class)
                .annotatedWith(EntityViewListingEnricherProvider.class)
                .to(EntityViewTaskListingEnricher.class);
        bind(RequestViewTaskListingEnricher.class)
                .annotatedWith(AgentTaskEligibilityEnricherProvider.class)
                .to(AgentTaskEligibilityEnricher.class);
        bind(RequestViewTaskListingEnricher.class)
                .annotatedWith(EntityHistoryViewEnricherProvider.class)
                .to(EntityHistoryViewTaskListingEnricher.class);

        bind(new TypeLiteral<GeoPolygonQueryBuilder<GeoFenceRemappingSearchRequest>>() {}).to(RemappingGeoPolygonQueryBuilder.class);
    }

    @Provides
    @Singleton
    public LookupDao<StoredTaskInstance> provideStoredTaskInstanceLookupDao() {
        return shardingBundle.createParentObjectDao(StoredTaskInstance.class);
    }

    @Provides
    @Singleton
    public AuditDao<StoredTaskInstance> provideStoredTaskInstanceAuditDao() {
        return new AuditDaoImpl<>(shardingBundle.getSessionFactories(), StoredTaskInstance.class,
                new ShardCalculator<>(shardingBundle.getShardManager(),
                        new ConsistentHashBucketIdExtractor<>(shardingBundle.getShardManager())));
    }

    @Provides
    @Singleton
    public LookupDao<StoredDefinitionAttributeMappings> provideStoredAttributeDefinitionMappingsLookupDao() {
        return shardingBundle.createParentObjectDao(StoredDefinitionAttributeMappings.class);
    }

    @Provides
    @Singleton
    public LookupDao<StoredActionAttributeMappings> provideStoredActionAttributeMappingsLookupDao() {
        return shardingBundle.createParentObjectDao(StoredActionAttributeMappings.class);
    }

    @Provides
    @Singleton
    public LookupDao<StoredTaskAttribute> provideStoredTaskAttributeLookupDao() {
        return shardingBundle.createParentObjectDao(StoredTaskAttribute.class);
    }

    @Provides
    @Singleton
    public RelationalDao<StoredTaskInstance> provideStoredTaskInstanceRelationalDao() {
        return shardingBundle.createRelatedObjectDao(StoredTaskInstance.class);
    }

    @Provides
    @Singleton
    public ChimeraLite provideChimeraLite() {
        return chimeraLiteBundle.getChimeraLite();
    }

    @Provides
    @Singleton
    public LookupDao<StoredTaskDefinition> provideStoredTaskLookupDao() {
        return shardingBundle.createParentObjectDao(StoredTaskDefinition.class);
    }
    @Provides
    @Singleton
    public RelationalDao<StoredDefinitionAttributeMappings> provideStoredAttributeDefinitionMappingsRelationalDao() {
        return shardingBundle.createRelatedObjectDao(StoredDefinitionAttributeMappings.class);
    }

    @Provides
    @Singleton
    public LookupDao<StoredTaskTransition> provideStoredTaskTransitionLookupDao() {
        return shardingBundle.createParentObjectDao(StoredTaskTransition.class);
    }

    @Provides
    @Singleton
    public RelationalDao<StoredTaskTransition> provideStoredTaskTransitionRelationalDao() {
        return shardingBundle.createRelatedObjectDao(StoredTaskTransition.class);
    }

    @Provides
    @Singleton
    public LookupDao<StoredTaskAction> provideStoredTaskActionLookupDao() {
        return shardingBundle.createParentObjectDao(StoredTaskAction.class);
    }

    @Provides
    @Singleton
    public RelationalDao<StoredTaskAction> provideStoredTaskActionRelationalDao() {
        return shardingBundle.createRelatedObjectDao(StoredTaskAction.class);
    }

    @Provides
    @Singleton
    public RelationalDao<StoreTag> provideStoredTaskTagRelationalDao() {
        return shardingBundle.createRelatedObjectDao(StoreTag.class);
    }

    @Provides
    @Singleton
    public ConnectionRegistry provideRmqConnection() {
        return rabbitmqActorBundle.getConnectionRegistry();
    }

    @Provides
    @TaskEsRetryRmqActorConfig
    @Singleton
    public ActorConfig provideTaskEsRetryActorConfig(AppConfig configuration) {
        return configuration.getActorConfigs().get(ActorMessageType.TASK_ES_UPDATE_RETRY);
    }

    @Provides
    @TaskEsChangeEventActorConfig
    @Singleton
    public ActorConfig provideTaskEsChangeEventActorConfig(AppConfig configuration) {
        return configuration.getActorConfigs().get(ActorMessageType.TASK_ES_CHANGE_EVENT);
    }

    @Provides
    @TaskVerifyScheduleActorConfig
    @Singleton
    public ActorConfig provideTaskVerifyScheduleActorConfig(AppConfig configuration) {
        return configuration.getActorConfigs().get(ActorMessageType.TASK_SCHEDULE_VERIFICATION);
    }

    @Provides
    @TaskVerifyRmqActorConfig
    @Singleton
    public ActorConfig provideTaskVerifyRmqActorConfig(AppConfig configuration) {
        return configuration.getActorConfigs().get(ActorMessageType.TASK_VERIFICATION);
    }

    @Provides
    @QcTaskCreateAndAssignActorConfig
    @Singleton
    public ActorConfig provideQcTaskCreateAndAssignActorConfig(AppConfig configuration) {
        return configuration.getActorConfigs().get(ActorMessageType.QC_TASK_CREATE_EVENT);
    }

    @Provides
    @EventBasedTaskCreationActorConfig
    @Singleton
    public ActorConfig provideEventBasedTaskCreationActorConfig(AppConfig configuration) {
        return configuration.getActorConfigs().get(ActorMessageType.TASK_CREATION_FROM_EVENT);
    }

    @Provides
    @TaskRecreationScheduleActorConfig
    @Singleton
    public ActorConfig provideTaskRecreateActorConfig(AppConfig configuration) {
        return configuration.getActorConfigs().get(ActorMessageType.TASK_RECREATION);
    }


    @Provides
    @ClientTaskCreateAndAssignActorConfig
    @Singleton
    public ActorConfig provideClientTaskCreateAndAssignActorConfig(AppConfig configuration) {
        return configuration.getActorConfigs().get(ActorMessageType.CLIENT_TASK_CREATE_AND_ASSIGN);
    }

    @Provides
    @ClientTaskDeleteActorConfig
    @Singleton
    public ActorConfig provideClientTaskDeleteActorConfig(AppConfig configuration) {
        return configuration.getActorConfigs().get(ActorMessageType.CLIENT_TASK_DELETE);
    }

    @Provides
    @ClientTaskVerificationActorConfig
    @Singleton
    public ActorConfig provideClientTaskVerifyActorConfig(AppConfig configuration) {
        return configuration.getActorConfigs().get(ActorMessageType.CLIENT_TASK_MANUAL_VERIFICATION);
    }

    @Provides
    @HotspotSyncActorConfig
    @Singleton
    public ActorConfig provideHotspotSyncActorConfig(AppConfig configuration) {
        return configuration.getActorConfigs().get(ActorMessageType.HOTSPOT_SYNC);
    }

    @Provides
    @Singleton
    public LookupDao<StoredCampaign> provideStoredCampaignLookupDao() {
        return shardingBundle.createParentObjectDao(StoredCampaign.class);
    }
    @Provides
    @Singleton
    public LookupDao<StoreTag> provideStoredTagLookupDao() {
        return shardingBundle.createParentObjectDao(StoreTag.class);
    }

    @Provides
    @Singleton
    public LookupDao<StoredCommentsOnTask> provideStoredCommentsOnTask() {
        return shardingBundle.createParentObjectDao(StoredCommentsOnTask.class);
    }

    @Provides
    @Singleton
    public RelationalDao<StoredCommentsOnTask> provideStoredTaskCommentRelationalDao() {
        return shardingBundle.createRelatedObjectDao(StoredCommentsOnTask.class);
    }

    @Provides
    @Singleton
    public Miscellaneous getMiscellaneousConfig(AppConfig config) {
        return config.getMiscellaneous();
    }

    @Provides
    @Singleton
    public UserTaskCreationConfig getUserTaskCreationConfig(AppConfig config) {
        return config.getUserTaskCreationConfigs();
    }

    @Provides
    @Singleton
    public LeadCreationConfig getLeadTaskConfig(AppConfig config) {
        return config.getLeadCreationConfig();
    }

    @Provides
    @Singleton
    public DiscoveryViewRestrictionConfig getRestrictionContextSectorProfileTags(AppConfig config) {
        return config.getDiscoveryViewRestrictionConfig();
    }

    @Provides
    @Singleton
    public LookupDao<StoredHotspot> provideStoredHotspotLookupDao() {
        return shardingBundle.createParentObjectDao(StoredHotspot.class);
    }

    @Provides
    @Singleton
    public LookupDao<StoredHotspotConfig> provideStoredHotspotDefinitionLookupDao() {
        return shardingBundle.createParentObjectDao(StoredHotspotConfig.class);
    }

    @Provides
    @Singleton
    @CommentCreationPerDayConfig
    public Integer getCommentCreationLimitPerDay(AppConfig config) {
        return config.getCommentCreationLimitPerDay();
    }

    @Provides
    @Singleton
    public RelationalDao<StoredFormConfig> provideStoredFormConfigRelationalDao() {
        return shardingBundle.createRelatedObjectDao(StoredFormConfig.class);
    }

    @Provides
    @Singleton
    public RelationalDao<StoredFeedback> provideStoredFeedbackRelationalDao() {
        return shardingBundle.createRelatedObjectDao(StoredFeedback.class);
    }
}
