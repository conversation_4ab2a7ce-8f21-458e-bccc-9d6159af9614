package com.phonepe.merchant.legion.tasks.resources;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.gandalf.client.annotation.GandalfUserContext;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.merchant.gladius.models.legion.v2.AgentProfileV2;
import com.phonepe.merchant.gladius.models.tasks.request.AgentTaskEligibilityRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskListingRequest;
import com.phonepe.merchant.gladius.models.tasks.response.AgentTaskEligibilityResponse;
import com.phonepe.merchant.gladius.models.tasks.response.TaskSearchResponse;
import com.phonepe.merchant.legion.client.annotations.LegionGateKeeper;
import com.phonepe.merchant.legion.client.annotations.LegionUserContext;
import com.phonepe.merchant.legion.core.services.LegionServiceAdapter;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.services.TaskDiscoveryService;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import io.dropwizard.auth.Auth;
import io.dropwizard.primer.auth.annotation.Authorize;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import killswitch.enums.OperationType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.security.RolesAllowed;
import javax.validation.Valid;
import javax.ws.rs.BeanParam;
import javax.ws.rs.GET;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import java.util.Optional;

import static javax.ws.rs.core.HttpHeaders.AUTHORIZATION;

/**
 * V4 Task Discovery Resource with org-level support
 * Provides org-aware task discovery and management capabilities
 */
@Slf4j
@Path("/v4/tasks")
@Produces(MediaType.APPLICATION_JSON)
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class TaskDiscoveryResourceV4 {

    private final TaskDiscoveryService taskDiscoveryService;
    private final LegionServiceAdapter legionServiceAdapter;

    @POST
    @Timed
    @Operation(summary = "Get list of tasks for a particular entity with org context",
            responses = {@ApiResponse(responseCode = "OK", description = "Success"),
                    @ApiResponse(responseCode = "NOT_FOUND", description = "Failure")})
    @Authorize(value = "userTaskListing")
    @RolesAllowed(value = "userTaskListing")
    @Path("/listing")
    @ApiKillerMeta(tags = {OperationType.READ})
    @LegionGateKeeper
    public GenericResponse<TaskSearchResponseV4> getTaskListingForAgentV4(
            @Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal,
            @Parameter(hidden = true) @GandalfUserContext UserDetails userDetails,
            @Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
            @Valid TaskListingRequest request,
            @Parameter(hidden = true) @LegionUserContext AgentProfile agentProfile,
            @HeaderParam(AUTHORIZATION) String auth) {

        log.info("Getting task listing for agent with org context: {}", request);

        // Get org-aware agent profile
        AgentProfileV2 agentProfileV2 = null;
        try {
            agentProfileV2 = legionServiceAdapter.getAgentProfileV2(agentProfile.getAgentId());
        } catch (Exception e) {
            log.warn("Failed to get V2 agent profile, using V1 profile", e);
        }

        // Get tasks using existing service
        TaskSearchResponse taskResponse = taskDiscoveryService.getTaskListingForAgent(
                serviceUserPrincipal, userDetails, userPrincipal, request, agentProfile, auth);

        // Enhance response with org-level information
        TaskSearchResponseV4 enhancedResponse = enhanceWithOrgContext(taskResponse, agentProfileV2);

        return GenericResponse.<TaskSearchResponseV4>builder()
                .success(true)
                .data(enhancedResponse)
                .build();
    }

    /**
     * Enhanced task search response with org-level context
     */
    public static class TaskSearchResponseV4 extends TaskSearchResponse {
        private String orgId;
        private String orgName;
        private String orgRoleId;
        private String orgRoleName;
        private String effectiveBusinessUnit;
        private String effectiveRegion;

        // Getters and setters
        public String getOrgId() { return orgId; }
        public void setOrgId(String orgId) { this.orgId = orgId; }

        public String getOrgName() { return orgName; }
        public void setOrgName(String orgName) { this.orgName = orgName; }

        public String getOrgRoleId() { return orgRoleId; }
        public void setOrgRoleId(String orgRoleId) { this.orgRoleId = orgRoleId; }

        public String getOrgRoleName() { return orgRoleName; }
        public void setOrgRoleName(String orgRoleName) { this.orgRoleName = orgRoleName; }

        public String getEffectiveBusinessUnit() { return effectiveBusinessUnit; }
        public void setEffectiveBusinessUnit(String effectiveBusinessUnit) { this.effectiveBusinessUnit = effectiveBusinessUnit; }

        public String getEffectiveRegion() { return effectiveRegion; }
        public void setEffectiveRegion(String effectiveRegion) { this.effectiveRegion = effectiveRegion; }
    }

    /**
     * Enhance task response with org-level context
     */
    private TaskSearchResponseV4 enhanceWithOrgContext(TaskSearchResponse originalResponse, AgentProfileV2 agentProfileV2) {
        TaskSearchResponseV4 enhancedResponse = new TaskSearchResponseV4();
        
        // Copy all fields from original response
        enhancedResponse.setTaskDetails(originalResponse.getTaskDetails());
        enhancedResponse.setTaskCount(originalResponse.getTaskCount());
        enhancedResponse.setTotalTaskCount(originalResponse.getTotalTaskCount());
        enhancedResponse.setUserStats(originalResponse.getUserStats());
        enhancedResponse.setFilters(originalResponse.getFilters());
        enhancedResponse.setBadgeConfig(originalResponse.getBadgeConfig());

        // Add org-level information if available
        if (agentProfileV2 != null) {
            enhancedResponse.setOrgId(agentProfileV2.getOrgId());
            enhancedResponse.setOrgName(agentProfileV2.getOrgName());
            enhancedResponse.setOrgRoleId(agentProfileV2.getOrgRoleId());
            enhancedResponse.setOrgRoleName(agentProfileV2.getOrgRoleName());
            enhancedResponse.setEffectiveBusinessUnit(agentProfileV2.getEffectiveBusinessUnit());
            enhancedResponse.setEffectiveRegion(agentProfileV2.getEffectiveRegion());
        }

        return enhancedResponse;
    }

    @GET
    @Timed
    @Operation(summary = "Get list of agents who are eligible to perform a task with org context",
            responses = {@ApiResponse(responseCode = "OK", description = "Success"),
                    @ApiResponse(responseCode = "NOT_FOUND", description = "Failure")})
    @Authorize(value = "agentTaskEligibility")
    @RolesAllowed(value = "agentTaskEligibility")
    @Path("/{taskInstanceId}/fetch/eligible/agents/v4")
    @ApiKillerMeta(tags = {OperationType.READ})
    @LegionGateKeeper
    public GenericResponse<AgentTaskEligibilityResponseV4> fetchAgentsEligibleForTaskV4(
            @Parameter(hidden = true) @GandalfUserContext UserDetails userDetails,
            @Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
            @BeanParam AgentTaskEligibilityRequest request,
            @Parameter(hidden = true) @LegionUserContext AgentProfile agentProfile) {

        log.info("Fetching eligible agents for task with org context: {}", request.getTaskInstanceId());

        // Get eligible agents using existing service
        AgentTaskEligibilityResponse originalResponse = taskDiscoveryService.fetchAgentsEligibleForTask(
                agentProfile.getAgentId(), request);

        // Enhance with org-level information
        AgentTaskEligibilityResponseV4 enhancedResponse = enhanceEligibilityWithOrgContext(
                originalResponse, agentProfile.getAgentId());

        return GenericResponse.<AgentTaskEligibilityResponseV4>builder()
                .success(true)
                .data(enhancedResponse)
                .build();
    }

    /**
     * Enhanced agent task eligibility response with org context
     */
    public static class AgentTaskEligibilityResponseV4 extends AgentTaskEligibilityResponse {
        private String requesterOrgId;
        private String requesterOrgName;
        private java.util.List<AgentEligibilityInfo> agentEligibilityDetails;

        public String getRequesterOrgId() { return requesterOrgId; }
        public void setRequesterOrgId(String requesterOrgId) { this.requesterOrgId = requesterOrgId; }

        public String getRequesterOrgName() { return requesterOrgName; }
        public void setRequesterOrgName(String requesterOrgName) { this.requesterOrgName = requesterOrgName; }

        public java.util.List<AgentEligibilityInfo> getAgentEligibilityDetails() { return agentEligibilityDetails; }
        public void setAgentEligibilityDetails(java.util.List<AgentEligibilityInfo> agentEligibilityDetails) {
            this.agentEligibilityDetails = agentEligibilityDetails;
        }

        public static class AgentEligibilityInfo {
            private String agentId;
            private String agentName;
            private String orgId;
            private String orgName;
            private String orgRoleName;
            private String effectiveBusinessUnit;
            private boolean sameOrg;

            // Getters and setters
            public String getAgentId() { return agentId; }
            public void setAgentId(String agentId) { this.agentId = agentId; }

            public String getAgentName() { return agentName; }
            public void setAgentName(String agentName) { this.agentName = agentName; }

            public String getOrgId() { return orgId; }
            public void setOrgId(String orgId) { this.orgId = orgId; }

            public String getOrgName() { return orgName; }
            public void setOrgName(String orgName) { this.orgName = orgName; }

            public String getOrgRoleName() { return orgRoleName; }
            public void setOrgRoleName(String orgRoleName) { this.orgRoleName = orgRoleName; }

            public String getEffectiveBusinessUnit() { return effectiveBusinessUnit; }
            public void setEffectiveBusinessUnit(String effectiveBusinessUnit) { this.effectiveBusinessUnit = effectiveBusinessUnit; }

            public boolean isSameOrg() { return sameOrg; }
            public void setSameOrg(boolean sameOrg) { this.sameOrg = sameOrg; }
        }
    }

    /**
     * Enhance eligibility response with org context
     */
    private AgentTaskEligibilityResponseV4 enhanceEligibilityWithOrgContext(
            AgentTaskEligibilityResponse originalResponse, String requesterId) {

        AgentTaskEligibilityResponseV4 enhancedResponse = new AgentTaskEligibilityResponseV4();
        enhancedResponse.setEligibleAgentsIds(originalResponse.getEligibleAgentsIds());

        try {
            // Get requester org info
            AgentProfileV2 requesterProfile = legionServiceAdapter.getAgentProfileV2(requesterId);
            enhancedResponse.setRequesterOrgId(requesterProfile.getOrgId());
            enhancedResponse.setRequesterOrgName(requesterProfile.getOrgName());

            // Get detailed info for each eligible agent
            java.util.List<AgentTaskEligibilityResponseV4.AgentEligibilityInfo> agentDetails =
                    new java.util.ArrayList<>();

            for (String agentId : originalResponse.getEligibleAgentsIds()) {
                try {
                    AgentProfileV2 agentProfile = legionServiceAdapter.getAgentProfileV2(agentId);
                    AgentTaskEligibilityResponseV4.AgentEligibilityInfo info =
                            new AgentTaskEligibilityResponseV4.AgentEligibilityInfo();

                    info.setAgentId(agentId);
                    info.setAgentName(agentProfile.getName());
                    info.setOrgId(agentProfile.getOrgId());
                    info.setOrgName(agentProfile.getOrgName());
                    info.setOrgRoleName(agentProfile.getOrgRoleName());
                    info.setEffectiveBusinessUnit(agentProfile.getEffectiveBusinessUnit());
                    info.setSameOrg(requesterProfile.getOrgId() != null &&
                                   requesterProfile.getOrgId().equals(agentProfile.getOrgId()));

                    agentDetails.add(info);
                } catch (Exception e) {
                    log.warn("Failed to get org info for agent {}", agentId, e);
                }
            }

            enhancedResponse.setAgentEligibilityDetails(agentDetails);
        } catch (Exception e) {
            log.warn("Failed to enhance eligibility response with org context", e);
        }

        return enhancedResponse;
    }
}
