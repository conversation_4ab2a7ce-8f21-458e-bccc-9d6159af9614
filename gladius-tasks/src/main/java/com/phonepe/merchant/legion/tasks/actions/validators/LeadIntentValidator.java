package com.phonepe.merchant.legion.tasks.actions.validators;

import com.google.inject.Inject;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.request.ActionToRemarkConfig;
import com.phonepe.merchant.gladius.models.tasks.request.TaskDefinitionFetchByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.LocationCommandRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskDefinitionInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaInformation;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaType;
import com.phonepe.merchant.gladius.models.tasks.validation.ActionValidator;
import com.phonepe.merchant.gladius.models.tasks.validation.ValidatorResponse;
import com.phonepe.merchant.gladius.models.tasks.validation.configs.ValidatorConfig;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.tasks.actions.annotations.ActionValidatorMarker;
import com.phonepe.merchant.legion.tasks.repository.TaskESRepository;
import com.phonepe.merchant.legion.tasks.services.TaskDefinitionService;
import com.phonepe.merchant.legion.tasks.services.ValidationService;
import com.phonepe.merchant.legion.tasks.utils.LeadManagementConfiguration;
import com.phonepe.models.merchants.tasks.EntityType;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.LEAD_INTENT_VALIDATOR;
import static com.phonepe.merchant.legion.core.exceptions.CoreErrorCode.INTENT_RESTRICTED_FOR_COMPLETION;
import static com.phonepe.merchant.legion.tasks.utils.ValidationUtils.getAllowedIntents;

@Slf4j
@ActionValidatorMarker(name = LEAD_INTENT_VALIDATOR)
public class LeadIntentValidator<T extends LocationCommandRequest> implements ActionValidator<T> {

    private final TaskESRepository taskESRepository;
    private final TaskDefinitionService taskDefinitionService;
    private final ValidationService validationService;

    @Inject
    public LeadIntentValidator(TaskESRepository taskESRepository, TaskDefinitionService taskDefinitionService, ValidationService validationService) {
        this.taskESRepository = taskESRepository;
        this.taskDefinitionService = taskDefinitionService;
        this.validationService = validationService;
    }

    @Override
    public void validate(EntityType entityType, ValidatorConfig validatorConfig) {
        // not needed here
    }

    @Override
    public ValidatorResponse validate(T taskCommandRequest, ValidatorConfig validatorConfig) {
        DiscoveryTaskInstance taskInstance = taskESRepository.get(taskCommandRequest.getTaskInstanceId());
        if (null == taskInstance.getTaskInstanceMeta()
                || null == taskInstance.getTaskInstanceMeta().getTaskMetaList()) {
            return buildValidatorResponse(
                    false,
                    INTENT_RESTRICTED_FOR_COMPLETION, "You are not allowed to close the task without filling a lead");

        }

        String leadIntent = getLeadIntentFromTask(taskInstance);
        if (leadIntent == null) {
            return buildValidatorResponse(
                    false,
                    INTENT_RESTRICTED_FOR_COMPLETION, "You are not allowed to close the task without filling a lead");
        }

        TaskDefinitionInstance taskDefinitionInstance =  taskDefinitionService.getFromCache(TaskDefinitionFetchByIdRequest.builder()
                .taskDefinitionId(taskInstance.getTaskDefinitionId())
                .build());
        List<ActionToRemarkConfig> leadUpdation = new ArrayList<>();
        if (!validationService.checkIfDefinitionIsWhitelisted(taskDefinitionInstance.getTaskDefinitionId())) {
            leadUpdation = LeadManagementConfiguration.getLeadUpdation();
        } else if (taskDefinitionInstance.isLeadUpdationAllowed()) {
            leadUpdation = taskDefinitionInstance.getDefinitionAttributes().getLeadConfig().getLeadUpdation();
        }
        List<String> allowedLeadIntents = getAllowedIntents(leadUpdation, taskInstance);
        if (!allowedLeadIntents.contains(leadIntent)) {
            return buildValidatorResponse(
                    false,
                    INTENT_RESTRICTED_FOR_COMPLETION,
                    String.format("You can't complete the task if the intent is %s", leadIntent));
        }

        return buildValidatorResponse(
                true,
                null,
                null);
    }

    private ValidatorResponse buildValidatorResponse(boolean validated, CoreErrorCode errorCode, String errorMessage) {
        return ValidatorResponse.builder()
                .validated(validated)
                .errorMessage(errorMessage)
                .errorCode(errorCode)
                .build();
    }

    private String getLeadIntentFromTask(DiscoveryTaskInstance taskInstance) {
        Optional<TaskMetaInformation> taskMetaInformation = taskInstance
                .getTaskInstanceMeta()
                .getTaskMetaList()
                .stream()
                .filter(metaInformation -> metaInformation.getType() == TaskMetaType.LEAD_INTENT)
                .findFirst();
        return taskMetaInformation.map(metaInformation -> metaInformation.getValue().toString()).orElse(null);
    }
}
