package com.phonepe.merchant.legion.tasks.resources;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.gandalf.client.annotation.GandalfUserContext;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.merchant.gladius.models.tasks.request.TaskActionFetchByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskActionInstance;
import com.phonepe.merchant.legion.client.annotations.LegionGateKeeper;
import com.phonepe.merchant.legion.client.annotations.LegionUserContext;
import com.phonepe.merchant.legion.core.utils.AuthUserDetails;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.services.TaskActionService;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import com.phonepe.platform.requestinfo.bundle.annotations.RequestContext;
import com.phonepe.platform.requestinfo.models.RequestInfo;
import io.dropwizard.auth.Auth;
import io.dropwizard.primer.auth.annotation.Authorize;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Optional;
import java.util.Set;
import javax.annotation.security.RolesAllowed;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import killswitch.enums.OperationType;
import lombok.extern.slf4j.Slf4j;

import static com.phonepe.merchant.legion.core.utils.LegionCoreConstants.AUTH_NAME;
import static javax.ws.rs.core.HttpHeaders.AUTHORIZATION;

@Slf4j
@Singleton
@Path("/v1/task/action")
@Tag(name = "Task Action Related APIs")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@SecurityRequirement(name = AUTH_NAME)
@SecurityScheme(name = AUTH_NAME, type = SecuritySchemeType.APIKEY,
        in = SecuritySchemeIn.HEADER, paramName = AUTHORIZATION)
public class TaskActionResource {

    private final TaskActionService taskActionService;

    @Inject
    public TaskActionResource(TaskActionService taskActionService) {
        this.taskActionService = taskActionService;
    }

    @POST
    @Path("/create")
    @Timed
    @Authorize(value = "all")
    @RolesAllowed(value = "all")
    @Operation(summary = "Create Task Action")
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.WRITE})
    @LegionGateKeeper
	public GenericResponse<TaskActionInstance> create(@Parameter(hidden = true) @RequestContext RequestInfo requestInfo,
                                                      @NotNull @Valid TaskActionInstance request,
                                                      @Parameter(hidden = true) @GandalfUserContext UserDetails userDetails,
                                                      @Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
                                                      @Parameter(hidden = true) @LegionUserContext AgentProfile agentProfile){
        String actor = AuthUserDetails.getLegionUserId(agentProfile, userDetails, userPrincipal);
        request.setCreatedBy(actor);
        request.setUpdatedBy(actor);
        TaskActionInstance response = taskActionService.save(request);
        return GenericResponse.<TaskActionInstance>builder()
                .success(response != null)
                .data(response)
                .build();
    }

    @GET
    @Path("/{taskActionId}")
    @Timed
    @Operation(summary = "Fetch Task Action")
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.READ})
	public GenericResponse<TaskActionInstance> fetch(@Parameter(hidden = true) @RequestContext RequestInfo requestInfo,
                                                     @PathParam("taskActionId") @NotNull final String taskActionId) {
        TaskActionInstance taskActionInstance = taskActionService.getFromDB(
                TaskActionFetchByIdRequest.builder()
                        .taskActionId(taskActionId)
                        .build()
        );
        return GenericResponse.<TaskActionInstance>builder()
                .success(taskActionInstance != null)
                .data(taskActionInstance)
                .build();
    }


    @PUT
    @Path("/update")
    @Timed
    @Authorize(value = "all")
    @RolesAllowed(value = "all")
    @Operation(summary = "update Task Action")
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.WRITE})
    @LegionGateKeeper
	public GenericResponse<TaskActionInstance> update(@Parameter(hidden = true) @RequestContext RequestInfo requestInfo,
                                                      @Valid @NotNull TaskActionInstance request,
                                                      @Parameter(hidden = true) @GandalfUserContext UserDetails userDetails,
                                                      @Parameter(hidden = true) @Auth ServiceUserPrincipal userPrincipal,
                                                      @Parameter(hidden = true) @LegionUserContext AgentProfile agentProfile){
        String actor = AuthUserDetails.getLegionUserId(agentProfile, userDetails, userPrincipal);
        request.setUpdatedBy(actor);
        TaskActionInstance response = taskActionService.update(request);
        return GenericResponse.<TaskActionInstance>builder()
                .success(response != null)
                .data(response)
                .build();
    }

    @GET
    @Timed
    @Authorize(value = "getAllActionIds")
    @RolesAllowed(value = "getAllActionIds")
    @ExceptionMetered
    @ApiKillerMeta(tags = {OperationType.READ})
    @Operation(summary = "Get All Task Action Ids From Cache",
            responses = {@ApiResponse(responseCode = "OK", description = "Success"),
                    @ApiResponse(responseCode = "NOT_FOUND", description = "Failure")})
    @Path("/all")
    public GenericResponse<Set<String>> getAllActionIds(@Parameter(hidden = true) @Auth final Optional<ServiceUserPrincipal> serviceUserPrincipal){
        return GenericResponse.<Set<String>>builder()
                .success(true)
                .data(taskActionService.getAllActionIds())
                .build();
    }
}
