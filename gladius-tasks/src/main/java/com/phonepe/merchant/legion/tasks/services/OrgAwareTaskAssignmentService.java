package com.phonepe.merchant.legion.tasks.services;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.gladius.models.legion.v2.AgentProfileV2;
import com.phonepe.merchant.gladius.models.legion.v2.OrgProfile;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.request.TaskAssignRequest;
import com.phonepe.merchant.legion.core.config.LegionMigrationConfig;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.services.LegionServiceAdapter;
import com.phonepe.merchant.legion.core.services.OrgAwareValidationService;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.exceptions.LegionTaskErrorCode;
import io.appform.functionmetrics.MonitoredFunction;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.phonepe.merchant.legion.core.utils.CommonUtils.isNullOrEmpty;

/**
 * Service for org-aware task assignment logic
 * Handles task assignment validation and business logic with org-level context
 */
@Slf4j
@Singleton
public class OrgAwareTaskAssignmentService {

    private final LegionServiceAdapter legionServiceAdapter;
    private final OrgAwareValidationService orgAwareValidationService;
    private final LegionMigrationConfig migrationConfig;

    @Inject
    public OrgAwareTaskAssignmentService(LegionServiceAdapter legionServiceAdapter,
                                       OrgAwareValidationService orgAwareValidationService,
                                       LegionMigrationConfig migrationConfig) {
        this.legionServiceAdapter = legionServiceAdapter;
        this.orgAwareValidationService = orgAwareValidationService;
        this.migrationConfig = migrationConfig;
    }

    /**
     * Validate task assignment with org-level context
     */
    @MonitoredFunction
    public void validateTaskAssignment(TaskAssignRequest taskAssignRequest, 
                                     DiscoveryTaskInstance discoveryTaskInstance,
                                     String requesterId) {
        String agentId = taskAssignRequest.getAssignedTo();
        
        if (migrationConfig.isEnableOrgLevelLogic()) {
            validateOrgAwareTaskAssignment(agentId, discoveryTaskInstance, requesterId);
        } else {
            validateLegacyTaskAssignment(agentId, discoveryTaskInstance, requesterId);
        }
    }

    /**
     * Validate task assignment using org-level logic
     */
    private void validateOrgAwareTaskAssignment(String agentId, 
                                              DiscoveryTaskInstance discoveryTaskInstance,
                                              String requesterId) {
        try {
            AgentProfileV2 agentProfile = legionServiceAdapter.getAgentProfileV2(agentId);
            AgentProfileV2 requesterProfile = null;
            
            if (requesterId != null) {
                requesterProfile = legionServiceAdapter.getAgentProfileV2(requesterId);
            }

            // Validate agent is active
            if (!agentProfile.isActive()) {
                throw LegionException.error(LegionTaskErrorCode.INVALID_AGENT_ID,
                        Map.of("message", "Agent is not active"));
            }

            // Validate org-level constraints
            validateOrgConstraints(agentProfile, requesterProfile, discoveryTaskInstance);

            // Validate role-based constraints
            validateRoleConstraints(agentProfile, discoveryTaskInstance);

            // Validate sector accessibility
            validateSectorAccessibility(agentProfile, discoveryTaskInstance);

        } catch (Exception e) {
            log.error("Error in org-aware task assignment validation for agent {}", agentId, e);
            throw e;
        }
    }

    /**
     * Validate task assignment using legacy logic
     */
    private void validateLegacyTaskAssignment(String agentId, 
                                            DiscoveryTaskInstance discoveryTaskInstance,
                                            String requesterId) {
        try {
            AgentProfile agentProfile = legionServiceAdapter.getAgentProfile(agentId);

            // Validate agent is active
            if (!agentProfile.isActive()) {
                throw LegionException.error(LegionTaskErrorCode.INVALID_AGENT_ID,
                        Map.of("message", "Agent is not active"));
            }

            // Validate business unit and role constraints
            Set<String> tags = discoveryTaskInstance.getTags();
            if (!isNullOrEmpty(tags)) {
                String businessUnit = agentProfile.getBusinessUnit() != null ? 
                        agentProfile.getBusinessUnit().name() : null;
                String agentType = agentProfile.getAgentType() != null ? 
                        agentProfile.getAgentType().name() : null;
                
                if (!(tags.contains(businessUnit) || tags.contains(agentType))) {
                    throw LegionException.error(LegionTaskErrorCode.BUSINESS_UNIT_MISMATCH);
                }
            }

        } catch (Exception e) {
            log.error("Error in legacy task assignment validation for agent {}", agentId, e);
            throw e;
        }
    }

    /**
     * Validate org-level constraints
     */
    private void validateOrgConstraints(AgentProfileV2 agentProfile, 
                                      AgentProfileV2 requesterProfile,
                                      DiscoveryTaskInstance discoveryTaskInstance) {
        
        // If requester is specified, validate they belong to same org or have cross-org permissions
        if (requesterProfile != null) {
            if (!isSameOrg(agentProfile, requesterProfile)) {
                // Check if cross-org assignment is allowed
                if (!isCrossOrgAssignmentAllowed(requesterProfile, agentProfile)) {
                    throw LegionException.error(LegionTaskErrorCode.CROSS_ORG_ASSIGNMENT_NOT_ALLOWED,
                            Map.of("message", "Cross-org task assignment not allowed"));
                }
            }
        }

        // Validate org-specific task constraints
        validateOrgSpecificConstraints(agentProfile, discoveryTaskInstance);
    }

    /**
     * Validate role-based constraints using org roles
     */
    private void validateRoleConstraints(AgentProfileV2 agentProfile, 
                                       DiscoveryTaskInstance discoveryTaskInstance) {
        Set<String> tags = discoveryTaskInstance.getTags();
        if (!isNullOrEmpty(tags)) {
            String effectiveBusinessUnit = agentProfile.getEffectiveBusinessUnit();
            String effectiveRole = agentProfile.getEffectiveRole();
            
            if (!(tags.contains(effectiveBusinessUnit) || tags.contains(effectiveRole))) {
                throw LegionException.error(LegionTaskErrorCode.BUSINESS_UNIT_MISMATCH);
            }
        }
    }

    /**
     * Validate sector accessibility with org context
     */
    private void validateSectorAccessibility(AgentProfileV2 agentProfile, 
                                           DiscoveryTaskInstance discoveryTaskInstance) {
        if (discoveryTaskInstance.getLocation() != null) {
            // Get sectors for the task location
            // This would integrate with Atlas service to get sectors
            // For now, we'll use the existing sector validation logic
            String agentId = agentProfile.getAgentId();
            
            // Use adapter which handles V1/V2 routing
            List<String> agentSectors = legionServiceAdapter.getAllAccessibleSectorsOfAgent(agentId);
            
            if (agentSectors.isEmpty()) {
                throw LegionException.error(LegionTaskErrorCode.NO_ACCESSIBLE_SECTORS,
                        Map.of("message", "Agent has no accessible sectors"));
            }
        }
    }

    /**
     * Check if two agents belong to the same organization
     */
    private boolean isSameOrg(AgentProfileV2 agent1, AgentProfileV2 agent2) {
        if (agent1.getOrgId() == null || agent2.getOrgId() == null) {
            return true; // Backward compatibility
        }
        return agent1.getOrgId().equals(agent2.getOrgId());
    }

    /**
     * Check if cross-org assignment is allowed
     */
    private boolean isCrossOrgAssignmentAllowed(AgentProfileV2 requester, AgentProfileV2 assignee) {
        // This would check if the requester has permissions to assign tasks across orgs
        // For now, we'll allow it for certain roles
        String requesterRole = requester.getEffectiveRole();
        return requesterRole != null && (
                requesterRole.contains("ADMIN") || 
                requesterRole.contains("MANAGER") ||
                requesterRole.contains("SUPERVISOR")
        );
    }

    /**
     * Validate org-specific constraints
     */
    private void validateOrgSpecificConstraints(AgentProfileV2 agentProfile, 
                                              DiscoveryTaskInstance discoveryTaskInstance) {
        // This would validate any org-specific business rules
        // For example, certain task types might only be allowed for specific orgs
        
        String orgId = agentProfile.getOrgId();
        if (orgId != null) {
            // Get org profile and validate constraints
            try {
                OrgProfile orgProfile = orgAwareValidationService.getAgentOrgProfile(agentProfile.getAgentId());
                if (orgProfile != null) {
                    // Validate org-specific rules here
                    log.debug("Validating org-specific constraints for org: {}", orgProfile.getName());
                }
            } catch (Exception e) {
                log.warn("Failed to get org profile for validation", e);
            }
        }
    }

    /**
     * Get eligible agents for task with org filtering
     */
    @MonitoredFunction
    public List<String> getEligibleAgentsForTask(List<String> candidateAgents, 
                                                String taskInstanceId,
                                                String requesterId) {
        if (migrationConfig.isEnableOrgLevelLogic()) {
            return getOrgAwareEligibleAgents(candidateAgents, taskInstanceId, requesterId);
        } else {
            return candidateAgents; // Use existing logic
        }
    }

    /**
     * Filter eligible agents based on org-level constraints
     */
    private List<String> getOrgAwareEligibleAgents(List<String> candidateAgents, 
                                                  String taskInstanceId,
                                                  String requesterId) {
        try {
            AgentProfileV2 requesterProfile = null;
            if (requesterId != null) {
                requesterProfile = legionServiceAdapter.getAgentProfileV2(requesterId);
            }

            final AgentProfileV2 finalRequesterProfile = requesterProfile;
            
            return candidateAgents.stream()
                    .filter(agentId -> {
                        try {
                            AgentProfileV2 agentProfile = legionServiceAdapter.getAgentProfileV2(agentId);
                            
                            // Filter based on org constraints
                            if (finalRequesterProfile != null && 
                                !isSameOrg(agentProfile, finalRequesterProfile) &&
                                !isCrossOrgAssignmentAllowed(finalRequesterProfile, agentProfile)) {
                                return false;
                            }
                            
                            return true;
                        } catch (Exception e) {
                            log.warn("Error filtering agent {} for eligibility", agentId, e);
                            return false;
                        }
                    })
                    .toList();
        } catch (Exception e) {
            log.error("Error in org-aware agent filtering", e);
            return candidateAgents; // Fallback to original list
        }
    }
}
