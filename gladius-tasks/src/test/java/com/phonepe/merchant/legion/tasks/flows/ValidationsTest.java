package com.phonepe.merchant.legion.tasks.flows;

import com.phonepe.merchant.gladius.models.tasks.utils.Miscellaneous;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.repository.impl.ChimeraRepositoryImpl;
import com.phonepe.merchant.legion.core.services.AtlasService;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.tasks.flows.models.SelfOrderTaskActions;
import com.phonepe.merchant.legion.tasks.repository.TaskInstanceRepository;
import com.phonepe.merchant.legion.tasks.services.impl.TaskActionServiceImpl;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.Mockito;

import java.util.Set;

import static org.mockito.Mockito.when;

public class ValidationsTest {

    private final LegionService legionService;
    private final AtlasService atlasService;
    private final ChimeraRepositoryImpl chimeraLiteRepository;
    private final Validations validations;
    private final TaskActionServiceImpl taskActionService;
    private final TaskInstanceRepository taskInstanceRepository;
    private final FoxtrotEventIngestionService foxtrotEventIngestionService;
    private final Miscellaneous miscellaneous;
    private static final String SELF_ORDER_TASK_ACTIONS = "self_order_task_actions";


    public ValidationsTest() {
        legionService = Mockito.mock(LegionService.class);
        atlasService = Mockito.mock(AtlasService.class);
        chimeraLiteRepository = Mockito.mock(ChimeraRepositoryImpl.class);
        taskActionService = Mockito.mock(TaskActionServiceImpl.class);
        taskInstanceRepository = Mockito.mock(TaskInstanceRepository.class);
        miscellaneous = Mockito.mock(Miscellaneous.class);
        foxtrotEventIngestionService = Mockito.mock(FoxtrotEventIngestionService.class);
        validations = new Validations(legionService, atlasService, chimeraLiteRepository, taskActionService, miscellaneous, foxtrotEventIngestionService);
    }

    @Test
    public void isSelfOrderTaskAction() {
        when(chimeraLiteRepository.getChimeraConfig(SELF_ORDER_TASK_ACTIONS,
                SelfOrderTaskActions.class)).thenReturn(SelfOrderTaskActions.builder().actionIds(Set.of("actionId")).build());
        Assertions.assertTrue(validations.isSelfOrderTaskAction("actionId"));
    }




}
