package com.phonepe.merchant.legion.tasks.cache;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.merchant.gladius.models.tasks.response.TaskActionInstance;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskAction;
import com.phonepe.merchant.legion.core.cache.CacheConfig;
import com.phonepe.merchant.legion.core.cache.CacheName;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.merchant.legion.tasks.repository.TaskActionRepository;
import com.phonepe.merchant.legion.tasks.utils.CacheUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class TaskActionCacheTest {

    private TaskActionCache taskActionCache;
    private TaskActionRepository taskActionRepository;
    private CacheUtils cacheUtils;
    private MetricRegistry metricRegistry;
    private Map<CacheName, CacheConfig> cacheConfigs;
    private ObjectMapper objectMapper = new ObjectMapper();
    private static final CacheName CACHE_NAME = CacheName.AGENT_ACTION;
    private static final String ACTION_ID = "actionId";

    @BeforeEach
    void setUp() {
        SerDe.init(objectMapper);
        metricRegistry = new MetricRegistry();
        cacheUtils = mock(CacheUtils.class);
        cacheConfigs = Map.of(CacheName.AGENT_ACTION, new CacheConfig());
        taskActionRepository = mock(TaskActionRepository.class);
        Mockito.reset(taskActionRepository);
        taskActionCache = new TaskActionCache(cacheConfigs, () -> taskActionRepository, metricRegistry, cacheUtils);
    }

    @Test
    void testConstructor_CacheRegister() {
        verify(cacheUtils).registerCache(CACHE_NAME, taskActionCache);
    }

    @Test
    void test_FetchTaskActionDetailsFromCache_Success() {
        when(taskActionRepository.get(ACTION_ID)).thenReturn(Optional.ofNullable(StoredTaskAction.builder().actionId(ACTION_ID).build()));
        TaskActionInstance result = taskActionCache.get(ACTION_ID);
        assertNotNull(result);
        assertEquals(ACTION_ID, result.getActionId());
    }

    @Test
    void test_FetchTaskActionDetailsFromCache_Notfound() {
        when(taskActionRepository.get(ACTION_ID)).thenReturn(Optional.empty());
        Assertions.assertThrows(LegionException.class, () -> taskActionCache.get(ACTION_ID));
    }

    @Test
    void test_FetchTaskActionDetailsFromCache_LegionException() {
        when(taskActionRepository.get(ACTION_ID)).thenThrow(LegionException.class);
        Assertions.assertThrows(LegionException.class, ()-> taskActionCache.get(ACTION_ID));
    }

    @Test
    void test_FetchTaskActionDetailsFromCache_Failure() {
        when(taskActionRepository.get(ACTION_ID)).thenThrow(RuntimeException.class);
        Assertions.assertThrows(LegionException.class, ()-> taskActionCache.get(ACTION_ID));
    }


}
