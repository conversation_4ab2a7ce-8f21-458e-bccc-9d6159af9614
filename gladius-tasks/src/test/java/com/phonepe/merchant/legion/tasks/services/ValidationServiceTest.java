package com.phonepe.merchant.legion.tasks.services;

import com.phonepe.merchant.gladius.models.entitystore.Entity;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.request.CampaignFetchByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskDefinitionFetchByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskViewRequestType;
import com.phonepe.merchant.gladius.models.tasks.request.search.AssignedViewTaskFetchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.DiscoveryViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.SectorAssignedViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.SectorDiscoveryViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.map.AssignedSectorMapViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.map.DiscoverySectorMapViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.response.Campaign;
import com.phonepe.merchant.gladius.models.tasks.response.ExpiryPeriod;
import com.phonepe.merchant.gladius.models.tasks.response.TaskDefinitionInstance;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.services.AtlasService;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.models.profile.enums.AgentType;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.entitystore.EntityStore;
import com.phonepe.merchant.legion.tasks.repository.TaskESRepository;
import com.phonepe.merchant.legion.tasks.services.impl.ValidationServiceImpl;
import com.phonepe.models.merchants.tasks.EntityType;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import org.junit.After;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.Mockito;

import java.util.Calendar;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static com.phonepe.merchant.legion.tasks.DiscoveryTestUtils.getEntity;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class ValidationServiceTest extends LegionTaskBaseTest {

    private static ValidationService validationService;
    private static EntityStore entityStore;
    private static LegionService profileCRUDService;
    private static CampaignService campaignService;
    private static TaskDefinitionService taskDefinitionService;
    private static TaskESRepository taskESRepository;
    private static AtlasService atlasService;

    @BeforeClass
    public static void init() {
        taskDefinitionService = mock(TaskDefinitionService.class);
        entityStore = mock(EntityStore.class);
        profileCRUDService = mock(LegionService.class);
        campaignService = mock(CampaignService.class);
        taskESRepository = mock(TaskESRepository.class);
        atlasService = mock(AtlasService.class);
        validationService = new ValidationServiceImpl(campaignService, entityStore, profileCRUDService , atlasService, taskDefinitionService, taskESRepository, List.of("SS_DEPLOYMENTS"), Set.of("DEFINITION_ID"));
    }

    @After
    public void tearDown() {
        Mockito.reset(taskDefinitionService, entityStore, profileCRUDService, campaignService, taskESRepository);
    }

    @Test
    public void validateTaskDefinitionTestWithCorrectDefinitionTest() {
        when(taskDefinitionService.getFromDb(any(TaskDefinitionFetchByIdRequest.class))).thenReturn(TaskDefinitionInstance.builder().build());
        validationService.validateAndGetTaskDefinition("Random_definition");
    }

    @Test(expected = LegionException.class)
    public void validateTaskDefinitionTestWithWrongDefinitionTest() {
        when(taskDefinitionService.getFromDb(TaskDefinitionFetchByIdRequest.builder()
                .taskDefinitionId("Random__definition").build())).thenThrow(LegionException.class);
        validationService.validateAndGetTaskDefinition("Random__definition");
    }

    @Test
    public void validateBoundedAssignmentWithMissingAssigneeTest() {
        validationService.validateBoundedAssignmentWithMissingAssignee(false, "saransh_user");
    }

    @Test(expected = LegionException.class)
    public void validateBoundedAssignmentWithMissingAssigneeNullTest() {
        validationService.validateBoundedAssignmentWithMissingAssignee(false, null);
    }

    @Test
    public void validateCampaignTest() {

        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.YEAR, 1); // to get previous year add -1
        Campaign campaign = Campaign.builder()
                .campaignId("16june_camp")
                .createdBy("me")
                .expiryPeriod(ExpiryPeriod.TIMESTAMP)
                .expiryValue(cal.getTimeInMillis())
                .updatedBy("me")
                .build();
        campaignService.save(campaign);

        doReturn(campaign).when(campaignService).get((CampaignFetchByIdRequest.builder()
                .campaignId("16june_camp")
                .build()));
        validationService.validateAndGetCampaign("16june_camp");
    }

    @Test
    public void validateCampaignNullEndDateTest() {
        Campaign campaign = Campaign.builder()
                .campaignId("16june_campaign")
                .createdBy("me")
                .updatedBy("me")
                .build();
        campaignService.save(campaign);

        doReturn(campaign).when(campaignService).get((CampaignFetchByIdRequest.builder()
                .campaignId("16june_campaign")
                .build()));
        validationService.validateAndGetCampaign("16june_campaign");
    }

    @Test(expected = LegionException.class)
    public void validateInactiveCampaignTest() {
        String campaignId = "CAMPAIGN12";
        Campaign expectedOutput = Campaign.builder()
                .expiryPeriod(ExpiryPeriod.TIMESTAMP)
                .expiryValue(System.currentTimeMillis())
                .updatedBy("me")
                .createdBy("me")
                .campaignId(campaignId)
                .build();
        campaignService.save(expectedOutput);

        doReturn(expectedOutput).when(campaignService).get((CampaignFetchByIdRequest.builder()
                .campaignId("CAMPAIGN12")
                .build()));

        validationService.validateAndGetCampaign("CAMPAIGN12");
    }

    @Test
    public void validateStoreEntityTest() {
        Entity entity = getEntity(EntityType.STORE);
        when(entityStore.getById(any())).thenReturn(Optional.of(entity));
        validationService.validateEntity(IdGenerator.generate("MS").getId(), EntityType.STORE);
    }

    @Test()
    public void validateAgentAndHisManager() {
        String agentId = "RandomAgent123";
        AgentProfile expectedOutput = AgentProfile.builder()
                .sectors(Collections.singletonList("String"))
                .agentType(AgentType.AGENT)
                .managerId(null)
                .active(true)
                .build();
        when(profileCRUDService.getAgentProfile(agentId)).thenReturn(expectedOutput);
        AgentProfile actualOutput = profileCRUDService.getAgentProfile(agentId);
        Assert.assertEquals(expectedOutput, actualOutput);
    }

    @Test
    public void validateRequester1(){
        validationService.validateRequesterInHierarchy("Mohit", "Mohit");
    }

    @Test
    public void validateRequester2(){
        when(profileCRUDService.getAgentProfile("mohit")).thenReturn(AgentProfile.builder()
                .agentId("mohit")
                .managerId("Mr Puri")
                .agentType(AgentType.AGENT)
                .active(true)
                .build());
        validationService.validateRequesterInHierarchy("mohit", "Mr Puri");
    }

    @Test
    public void validateRequester3(){
        when(profileCRUDService.getAgentProfile("mohit")).thenReturn(AgentProfile.builder()
                .agentId("mohit")
                .managerId("Mr Puri")
                .agentType(AgentType.AGENT)
                .active(true)
                .build());

        when(profileCRUDService.getAgentProfile("Mr Puri")).thenReturn(AgentProfile.builder()
                .agentId("Mr Puri")
                .managerId("Mr Mohit Puri")
                .agentType(AgentType.AGENT)
                .active(true)
                .build());
        validationService.validateRequesterInHierarchy("mohit", "Mr Mohit Puri");
    }

    @Test(expected = LegionException.class)
    public void validateRequester4(){
        when(profileCRUDService.getAgentProfile("mohit")).thenReturn(AgentProfile.builder()
                .agentId("mohit")
                .managerId("Mr Puri")
                .agentType(AgentType.AGENT)
                .active(true)
                .build());

        when(profileCRUDService.getAgentProfile("Mr Puri")).thenReturn(AgentProfile.builder()
                .agentId("Mr Puri")
                .managerId("Mr Mohit")
                .agentType(AgentType.AGENT)
                .active(true)
                .build());
        when(profileCRUDService.getAgentProfile("Mr Mohit")).thenReturn(AgentProfile.builder()
                .agentId("Mr Mohit")
                .managerId(null)
                .agentType(AgentType.AGENT)
                .active(true)
                .build());
        validationService.validateRequesterInHierarchy("mohit", "Mr Mohit Puri");
    }



    @Test
    public void validateRequesterWithTaskSearchRequest(){
        when(profileCRUDService.getAgentProfile("mohit")).thenReturn(AgentProfile.builder()
                .agentId("mohit")
                .managerId("Mr Puri")
                .agentType(AgentType.AGENT)
                .active(true)
                .build());
        AssignedViewTaskFetchRequest taskSearchRequest =  new AssignedViewTaskFetchRequest();
        taskSearchRequest.setAssignedTo("mohit");
        validationService.validateRequesterAndGetActor("mohit", taskSearchRequest);
    }

    @Test
    public void validateRequesterWithDiscoveryTaskSearchRequest(){
        when(profileCRUDService.getAgentProfile("mohit")).thenReturn(AgentProfile.builder()
                .agentId("mohit")
                .managerId("Mr Puri")
                .agentType(AgentType.AGENT)
                .active(true)
                .build());
        DiscoveryViewTaskSearchRequest taskSearchRequest =  new DiscoveryViewTaskSearchRequest();
        taskSearchRequest.setAgentId("mohit");
        validationService.validateRequesterAndGetActor("mohit", taskSearchRequest);
    }

    @Test
    public void validateRequesterWithSectorAssignedTaskSearchRequest(){
        when(profileCRUDService.getAgentProfile("mohit")).thenReturn(AgentProfile.builder()
                .agentId("mohit")
                .managerId("Mr Puri")
                .agentType(AgentType.AGENT)
                .active(true)
                .build());
        SectorAssignedViewTaskSearchRequest taskSearchRequest =  new SectorAssignedViewTaskSearchRequest();
        taskSearchRequest.setSectorId("sector");
        validationService.validateRequesterAndGetActor("mohit", taskSearchRequest);
    }

    @Test
    public void validateRequesterWithSectorDiscoveryTaskSearchRequest(){
        when(profileCRUDService.getAgentProfile("mohit")).thenReturn(AgentProfile.builder()
                .agentId("mohit")
                .managerId("Mr Puri")
                .agentType(AgentType.AGENT)
                .active(true)
                .build());
        SectorDiscoveryViewTaskSearchRequest taskSearchRequest =  new SectorDiscoveryViewTaskSearchRequest();
        taskSearchRequest.setSectorId("sector");
        validationService.validateRequesterAndGetActor("mohit", taskSearchRequest);
    }

    @Test
    public void validateRequesterWithSectorAssignedTaskMapView(){
        when(profileCRUDService.getAgentProfile("assignedTo")).thenReturn(AgentProfile.builder()
                .agentId("assignedTo")
                .managerId("mohit")
                .agentType(AgentType.AGENT)
                .active(true)
                .build());
        when(profileCRUDService.getAgentProfile("mohit")).thenReturn(AgentProfile.builder()
                .agentId("mohit")
                .managerId("Mr Puri")
                .agentType(AgentType.TSE)
                .active(true)
                .build());
        AssignedSectorMapViewTaskSearchRequest taskSearchRequest =  new AssignedSectorMapViewTaskSearchRequest();
        taskSearchRequest.setSectorId("sector");
        taskSearchRequest.setAssignedTo("assignedTo");

        validationService.validateRequesterAndGetActor("mohit", taskSearchRequest);
    }


    @Test
    public void validateRequesterWithSectorDiscoveryTaskMapView(){
        when(profileCRUDService.getAgentProfile("reporteeId")).thenReturn(AgentProfile.builder()
                .agentId("reporteeId")
                .managerId("mohit")
                .agentType(AgentType.AGENT)
                .active(true)
                .build());
        when(profileCRUDService.getAgentProfile("mohit")).thenReturn(AgentProfile.builder()
                .agentId("mohit")
                .managerId("Mr Puri")
                .agentType(AgentType.TSE)
                .active(true)
                .build());
        DiscoverySectorMapViewTaskSearchRequest taskSearchRequest =  new DiscoverySectorMapViewTaskSearchRequest();
        taskSearchRequest.setSectorId("sector");
        taskSearchRequest.setReporteeId("reporteeId");
        validationService.validateRequesterAndGetActor("mohit", taskSearchRequest);
    }

    @Test
    public void validateRequesterWithSectorEntityTaskMapView() {
        when(profileCRUDService.getAgentProfile("assignedTo")).thenReturn(AgentProfile.builder()
                .agentId("assignedTo")
                .managerId("mohit")
                .agentType(AgentType.AGENT)
                .active(true)
                .build());
        when(profileCRUDService.getAgentProfile("mohit")).thenReturn(AgentProfile.builder()
                .agentId("mohit")
                .managerId("Mr Puri")
                .agentType(AgentType.TSE)
                .active(true)
                .build());
        AssignedSectorMapViewTaskSearchRequest taskSearchRequest = new AssignedSectorMapViewTaskSearchRequest();
        taskSearchRequest.setTaskSearchRequestType(TaskViewRequestType.ENTITY_VIEW);
        validationService.validateRequesterAndGetActor("mohit", taskSearchRequest);
    }

    @Test
    public void testValidateAccessibleSectors() {
        DiscoveryTaskInstance discoveryTaskInstance = DiscoveryTaskInstance.builder()
                .polygonIds(List.of("polygon1", "polygon2"))
                .build();
        when(atlasService.getSectorIds(any())).thenReturn(List.of("polygon1"));
        when(taskESRepository.get(anyString())).thenReturn(discoveryTaskInstance);
        when(profileCRUDService.getAllAccessibleSectors(anyString(), anyString() )).thenReturn(List.of("polygon1", "polygon3"));
        validationService.validateAccessibleSectors("task1", "authToken");
        verify(profileCRUDService).getAllAccessibleSectors(anyString(), anyString());
        verify(taskESRepository).get(anyString());
    }

    @Test
    public void testValidateAccessibleSectorsInvalid() {
        DiscoveryTaskInstance discoveryTaskInstance = DiscoveryTaskInstance.builder()
                .polygonIds(List.of("polygon1", "polygon2"))
                .build();
        when(taskESRepository.get(anyString())).thenReturn(discoveryTaskInstance);
        when(profileCRUDService.getAllAccessibleSectors(anyString(), anyString() )).thenReturn(List.of("polygon4", "polygon3"));
        assertThrows(LegionException.class, ()-> validationService.validateAccessibleSectors("task1", "authToken"));
    }

    @Test
    public void testValidateAccessibleSectorsInvalidTid() {
        when(taskESRepository.get(anyString())).thenReturn(null);
        assertThrows(LegionException.class, ()-> validationService.validateAccessibleSectors("task1", "authToken"));
    }

    @Test
    public void testValidateTaskType_Exception() {
        assertThrows(LegionException.class, ()-> validationService.validateTaskType("task1"));
    }

    @Test
    public void testValidateTaskType() {
        Assertions.assertDoesNotThrow(() -> {
            validationService.validateTaskType("SS_DEPLOYMENTS");
        });
    }


    @Test
    public void testCheckIfDefinitionIsWhitelisted() {
        Assertions.assertDoesNotThrow(() -> {
            validationService.checkIfDefinitionIsWhitelisted("SS_DEPLOYMENTS");
        });
    }


    @Test
    public void testCheckIfDefinitionIsWhitelisted_Exception() {
        assertThrows(LegionException.class, ()-> validationService.validateTaskType("task1"));
    }


}
