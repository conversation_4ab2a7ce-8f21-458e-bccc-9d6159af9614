package com.phonepe.merchant.legion.tasks;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.gandalf.models.authn.UserType;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.enums.Namespace;
import com.phonepe.merchant.gladius.models.tasks.enums.Sorter;
import com.phonepe.merchant.gladius.models.tasks.filters.TaskFilters;
import com.phonepe.merchant.gladius.models.tasks.filtersnew.TaskFiltersV2;
import com.phonepe.merchant.gladius.models.tasks.request.TaskInstanceMeta;
import com.phonepe.merchant.gladius.models.tasks.request.TaskSearchRequestType;
import com.phonepe.merchant.gladius.models.tasks.request.TaskViewRequestType;
import com.phonepe.merchant.gladius.models.tasks.request.search.AssignedViewTaskFetchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.DiscoveryViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.SectorDiscoveryViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.TaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.map.AssignedSectorMapViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.map.DiscoverySectorMapViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.request.search.map.SectorMapViewTaskSearchRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaInformation;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaType;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskTransition;
import com.phonepe.merchant.gladius.models.tasks.utils.LeadConfig;
import com.phonepe.merchant.gladius.models.tasks.validation.ActionValidator;
import com.phonepe.merchant.gladius.models.tasks.verification.ActionVerifier;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.repository.ESRepository;
import com.phonepe.merchant.legion.core.services.BrickbatService;
import com.phonepe.merchant.legion.core.services.FortunaService;
import com.phonepe.merchant.legion.core.services.FoxtrotService;
import com.phonepe.merchant.legion.core.services.GeminiService;
import com.phonepe.merchant.legion.core.services.HermodService;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.core.services.MerchantOnboardingService;
import com.phonepe.merchant.legion.core.services.MerchantService;
import com.phonepe.merchant.legion.core.services.ParadoxService;
import com.phonepe.merchant.legion.core.services.TmsService;
import com.phonepe.merchant.legion.external.services.ExternalEntityService;
import com.phonepe.merchant.legion.models.profile.enums.AgentType;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.actions.validators.AgentLocationValidator;
import com.phonepe.merchant.legion.tasks.actions.validators.BrickbatFormFillValidator;
import com.phonepe.merchant.legion.tasks.actions.validators.ImageQcActionValidator;
import com.phonepe.merchant.legion.tasks.actions.validators.LeadIntentValidator;
import com.phonepe.merchant.legion.tasks.actions.validators.OnboardExternalEntityAsPhonepeMxnValidator;
import com.phonepe.merchant.legion.tasks.actions.validators.SmartSpeakerDeploymentValidator;
import com.phonepe.merchant.legion.tasks.actions.verifiers.AgentDeboardingVerifier;
import com.phonepe.merchant.legion.tasks.actions.verifiers.BrickbatFormFillVerifier;
import com.phonepe.merchant.legion.tasks.actions.verifiers.BusinessCategoryUpdateVerifier;
import com.phonepe.merchant.legion.tasks.actions.verifiers.EdcDeploymentVerifier;
import com.phonepe.merchant.legion.tasks.actions.verifiers.EdcReversePickupVerifier;
import com.phonepe.merchant.legion.tasks.actions.verifiers.EventExistenceVerifier;
import com.phonepe.merchant.legion.tasks.actions.verifiers.FlDriveVerifier;
import com.phonepe.merchant.legion.tasks.actions.verifiers.MerchantKycVerifier;
import com.phonepe.merchant.legion.tasks.actions.verifiers.MerchantLendingCompletionVerifier;
import com.phonepe.merchant.legion.tasks.actions.verifiers.MultiEdcDeploymentVerifier;
import com.phonepe.merchant.legion.tasks.actions.verifiers.NoActionVerifier;
import com.phonepe.merchant.legion.tasks.actions.verifiers.OnboardExternalEntityAsPhonepeMxnVerifier;
import com.phonepe.merchant.legion.tasks.actions.verifiers.OqcValidationVerifier;
import com.phonepe.merchant.legion.tasks.actions.verifiers.P2MLmigrationVerifier;
import com.phonepe.merchant.legion.tasks.actions.verifiers.PoaVerifier;
import com.phonepe.merchant.legion.tasks.actions.verifiers.PobVerifier;
import com.phonepe.merchant.legion.tasks.actions.verifiers.SelfOnboardingMerchantVerifier;
import com.phonepe.merchant.legion.tasks.actions.verifiers.SmartSpeakerReversePickupVerifier;
import com.phonepe.merchant.legion.tasks.actions.verifiers.SmartspeakerDueCollectionVerifier;
import com.phonepe.merchant.legion.tasks.actions.verifiers.SmartspeakerTroubleshootVerifier;
import com.phonepe.merchant.legion.tasks.actions.verifiers.SoundboxDeploymentVerifier;
import com.phonepe.merchant.legion.tasks.actions.verifiers.StoreCheckInVerifier;
import com.phonepe.merchant.legion.tasks.actions.verifiers.StoreQcVerifier;
import com.phonepe.merchant.legion.tasks.actions.verifiers.TaskQcVerifier;
import com.phonepe.merchant.legion.tasks.repository.TaskESRepository;
import com.phonepe.merchant.legion.tasks.search.query.scoring.models.PrioritiseStrategyConfig;
import com.phonepe.merchant.legion.tasks.search.query.scoring.models.PrioritiseStrategyPriority;
import com.phonepe.merchant.legion.tasks.services.TaskDefinitionService;
import com.phonepe.merchant.legion.tasks.services.ValidationService;
import com.phonepe.merchant.legion.tasks.services.impl.TaskDiscoveryServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.TaskFilterServiceImpl;
import com.phonepe.models.merchants.tasks.EntityType;
import com.phonepe.platform.atlas.model.fence.Polygon;
import com.phonepe.platform.atlas.model.fence.Shape;
import edu.emory.mathcs.backport.java.util.Arrays;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchResponseSections;
import org.elasticsearch.action.search.ShardSearchFailure;
import org.elasticsearch.common.bytes.BytesArray;
import org.elasticsearch.common.bytes.BytesReference;
import org.elasticsearch.common.text.Text;
import org.elasticsearch.common.unit.DistanceUnit;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.DocValueFormat;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.SearchSortValues;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.StringTerms;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.AGENT_DEBOARDING_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.AGENT_LOCATION_VALIDATOR;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.BRICKBAT_FORM_FILL_VALIDATOR;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.BRICKBAT_FORM_FILL_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.BUSINESS_CATEGORY_UPDATE_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.EDC_DEPLOYMENT_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.EDC_REVERSE_PICKUP_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.EVENT_EXISTENCE_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.EXTERNAL_ENTITY_TO_PHONEPE_MXN_VALIDATOR;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.EXTERNAL_ENTITY_TO_PHONEPE_MXN_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.FL_DRIVE_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.IMAGE_QC_ACTION_VALIDATOR;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.LEAD_INTENT_VALIDATOR;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.MERCHANT_KYC_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.MERCHANT_LENDING_COMPLETION_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.MULTI_EDC_DEPLOYMENT_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.NO_ACTION_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.OQC_VALIDATION_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.P2ML_MIGRATION_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.POA_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.POB_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.SELF_ONBOARDING_MERCHANT_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.SMARTSPEAKER_DEPLOYMENT_VALIDATOR;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.SMARTSPEAKER_DUE_COLLECTION_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.SMARTSPEAKER_REVERSE_PICKUP_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.SMARTSPEAKER_TROUBLESHOOT_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.SOUNDBOX_DEPLOYMENT_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.STORE_CHECK_IN_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.STORE_QC_VERIFIER;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.TASK_QC_VERIFIER;
import static com.phonepe.merchant.legion.tasks.utils.LegionTaskConstants.TASK_DEFINITION_ID_PREFIX;


public class TaskTestUtils {

    public static final Long DAY_IN_MS = 1000L*60*60*24;
    public static final String DUMMY_ACTION_ID = "ACTION_ID";

    public static UserDetails getUserDetails() {
        return UserDetails.builder()
                .userId(IdGenerator.generate("G").getId())
                .userType(UserType.USER)
                .externalReferenceId(IdGenerator.generate("B").getId())
                .build();
    }

    public static AgentProfile getAgentProfile() {

        return AgentProfile.builder()
                .agentId("testAgent123")
                .agentType(AgentType.AGENT)
                .name("testAgent123Name")
                .build();
    }

    public static Map<String, ActionVerifier> getVerifierMap(MerchantOnboardingService merchantOnboardingService,
                                                             FoxtrotService foxtrotService, ParadoxService paradoxService,
                                                             MerchantService merchantService, FortunaService fortunaService,
                                                             ExternalEntityService externalEntityService, FoxtrotEventIngestionService foxtrotEventIngestionService,
                                                             HermodService hermodService, TmsService tmsService, LegionService legionService) {
        Map<String, ActionVerifier> verifierMap = new HashMap<>();
        verifierMap.put(NO_ACTION_VERIFIER, new NoActionVerifier());
        verifierMap.put(STORE_CHECK_IN_VERIFIER, new StoreCheckInVerifier(merchantOnboardingService));
        verifierMap.put(OQC_VALIDATION_VERIFIER, new OqcValidationVerifier(merchantOnboardingService));
        verifierMap.put(TASK_QC_VERIFIER, new TaskQcVerifier());
        verifierMap.put(MERCHANT_KYC_VERIFIER, new MerchantKycVerifier(merchantOnboardingService));
        verifierMap.put(EVENT_EXISTENCE_VERIFIER, new EventExistenceVerifier(foxtrotService));
        verifierMap.put(STORE_QC_VERIFIER, new StoreQcVerifier(merchantOnboardingService));
        verifierMap.put(BRICKBAT_FORM_FILL_VERIFIER,new BrickbatFormFillVerifier(foxtrotService));
        verifierMap.put(FL_DRIVE_VERIFIER,new FlDriveVerifier(merchantOnboardingService,foxtrotService));
        verifierMap.put(POA_VERIFIER,new PoaVerifier(merchantOnboardingService));
        verifierMap.put(POB_VERIFIER,new PobVerifier(merchantOnboardingService));
        verifierMap.put(SOUNDBOX_DEPLOYMENT_VERIFIER, new SoundboxDeploymentVerifier(paradoxService));
        verifierMap.put(SMARTSPEAKER_TROUBLESHOOT_VERIFIER, new SmartspeakerTroubleshootVerifier(paradoxService));
        verifierMap.put(P2ML_MIGRATION_VERIFIER, new P2MLmigrationVerifier(merchantOnboardingService));
        verifierMap.put(BUSINESS_CATEGORY_UPDATE_VERIFIER, new BusinessCategoryUpdateVerifier(merchantService));
        verifierMap.put(SMARTSPEAKER_DUE_COLLECTION_VERIFIER,new SmartspeakerDueCollectionVerifier(paradoxService, fortunaService));
        verifierMap.put(SELF_ONBOARDING_MERCHANT_VERIFIER,new SelfOnboardingMerchantVerifier(merchantService));
        verifierMap.put(SMARTSPEAKER_REVERSE_PICKUP_VERIFIER,new SmartSpeakerReversePickupVerifier(paradoxService));
        verifierMap.put(EDC_DEPLOYMENT_VERIFIER, new EdcDeploymentVerifier(paradoxService));
        verifierMap.put(MULTI_EDC_DEPLOYMENT_VERIFIER, new MultiEdcDeploymentVerifier(tmsService));
        verifierMap.put(EDC_REVERSE_PICKUP_VERIFIER, new EdcReversePickupVerifier(paradoxService));
        verifierMap.put(EXTERNAL_ENTITY_TO_PHONEPE_MXN_VERIFIER, new OnboardExternalEntityAsPhonepeMxnVerifier(merchantOnboardingService, externalEntityService, foxtrotEventIngestionService));
        verifierMap.put(MERCHANT_LENDING_COMPLETION_VERIFIER, new MerchantLendingCompletionVerifier(hermodService));
        verifierMap.put(AGENT_DEBOARDING_VERIFIER, new AgentDeboardingVerifier(legionService));
        return verifierMap;
    }

    public static Map<String, ActionValidator> getValidatorMap(FoxtrotService foxtrotService,
                                                               FoxtrotEventIngestionService foxtrotEventIngestionService,
                                                               BrickbatService brickbatService,
                                                               ESRepository esRepository,
                                                               MerchantOnboardingService merchantOnboardingService,
                                                               ExternalEntityService externalEntityService,
                                                               LegionService legionService,
                                                               TaskESRepository taskESRepository,
                                                               TaskDefinitionService taskDefinitionService,
                                                               LeadConfig leadConfig,
                                                               GeminiService geminiService,
                                                               ValidationService validationService
) {
        Map<String, ActionValidator> validatorMap = new HashMap<>();
        validatorMap.put(BRICKBAT_FORM_FILL_VALIDATOR,new BrickbatFormFillValidator(brickbatService,foxtrotService));
        validatorMap.put(AGENT_LOCATION_VALIDATOR,new AgentLocationValidator(foxtrotEventIngestionService,esRepository));
        validatorMap.put(LEAD_INTENT_VALIDATOR, new LeadIntentValidator(taskESRepository, taskDefinitionService, validationService));
        validatorMap.put(IMAGE_QC_ACTION_VALIDATOR, new ImageQcActionValidator(legionService));
        validatorMap.put(EXTERNAL_ENTITY_TO_PHONEPE_MXN_VALIDATOR,new OnboardExternalEntityAsPhonepeMxnValidator(externalEntityService));
        validatorMap.put(SMARTSPEAKER_DEPLOYMENT_VALIDATOR, new SmartSpeakerDeploymentValidator(geminiService));
        return validatorMap;
    }

    public static DiscoveryTaskInstance getDiscoveryTaskInstance(LegionTaskStateMachineState state) {
        return DiscoveryTaskInstance.builder()
                .taskInstanceId(IdGenerator.generate("T").getId())
                .taskDefinitionId(IdGenerator.generate(TASK_DEFINITION_ID_PREFIX).getId())
                .campaign(IdGenerator.generate("C").getId())
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .taskState(state)
                .entityType(EntityType.STORE)
                .distance(10.0)
                .location(EsLocationRequest.builder()
                        .lat(0.0)
                        .lon(0.0)
                        .build())
                .createdAt(0L)
                .updatedAt(0L)
                .startDate(0L)
                .dueDate(0L)
                .assignedTo("Agent")
                .points((long) (Math.random() * 100))
                .active(true)
                .actionId(DUMMY_ACTION_ID)
                .taskInstanceMeta(TaskInstanceMeta.builder()
                        .taskMetaList(List.of(TaskMetaInformation.builder()
                                        .displayInformation(true)
                                        .type(TaskMetaType.LEAD_INTENT)
                                        .value("NOT_INTERESTED")
                                .build()))
                        .build())
                .polygonIds(Arrays.asList(new String[]{"ABCD","EFGH", "string"}))
                .build();
    }

    public static DiscoveryTaskInstance getDiscoveryTaskInstanceWithoutPolygonIds(LegionTaskStateMachineState state) {
        return DiscoveryTaskInstance.builder()
                .taskInstanceId(IdGenerator.generate("T").getId())
                .taskDefinitionId(IdGenerator.generate(TASK_DEFINITION_ID_PREFIX).getId())
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .taskState(state)
                .location(EsLocationRequest.builder()
                        .lat(0.0)
                        .lon(0.0)
                        .build())
                .createdAt(0L)
                .updatedAt(0L)
                .startDate(0L)
                .dueDate(0L)
                .points((long) (Math.random() * 100))
                .active(true)
                .actionId(DUMMY_ACTION_ID)
                .build();
    }

    public static DiscoveryTaskInstance getDiscoveryTaskInstance(EntityType entityType,LegionTaskStateMachineState state) {
        return entityType.accept(new EntityType.EntityTypeVisitor<DiscoveryTaskInstance, LegionTaskStateMachineState>() {
            @Override
            public DiscoveryTaskInstance visitUser(LegionTaskStateMachineState state) {
                return null;
            }

            @Override
            public DiscoveryTaskInstance visitSector(LegionTaskStateMachineState state) {
                return getSectorDiscoveryTaskInstance(state);
            }

            @Override
            public DiscoveryTaskInstance visitMerchant(LegionTaskStateMachineState state) {
                return getMerchantDiscoveryTaskInstance(state);
            }

            @Override
            public DiscoveryTaskInstance visitNone(LegionTaskStateMachineState state) {
                return null;
            }

            @Override
            public DiscoveryTaskInstance visitStore(LegionTaskStateMachineState state) {
                return getStoreDiscoveryTaskInstance(state);
            }

            @Override
            public DiscoveryTaskInstance visitTask(LegionTaskStateMachineState state) {
                return getTaskDiscoveryTaskInstance(state);
            }

            @Override
            public DiscoveryTaskInstance visitVpa(LegionTaskStateMachineState state) {
                return getVpaDiscoveryTaskInstance(state);
            }

            @Override
            public DiscoveryTaskInstance visitExternal(LegionTaskStateMachineState state) {
                return getExternalEntityDiscoveryTaskInstance(state);
            }

            @Override
            public DiscoveryTaskInstance visitAgent(LegionTaskStateMachineState state) {
                return getAgentEntityDiscoveryTaskInstance(state);
            }
        },state);
    }

    public static DiscoveryTaskInstance getMerchantDiscoveryTaskInstance(LegionTaskStateMachineState state) {
        DiscoveryTaskInstance taskInstance = getDiscoveryTaskInstance(state);
        taskInstance.setEntityType(EntityType.MERCHANT);
        taskInstance.setEntityId(IdGenerator.generate("M").getId());
        return taskInstance;
    }

    public static DiscoveryTaskInstance getStoreDiscoveryTaskInstance(LegionTaskStateMachineState state) {
        DiscoveryTaskInstance taskInstance = getDiscoveryTaskInstance(state);
        taskInstance.setEntityType(EntityType.STORE);
        taskInstance.setEntityId(IdGenerator.generate("M").getId()+"_"+IdGenerator.generate("MS").getId());
        return taskInstance;
    }

    public static DiscoveryTaskInstance getSectorDiscoveryTaskInstance(LegionTaskStateMachineState state) {
        DiscoveryTaskInstance taskInstance = getDiscoveryTaskInstance(state);
        taskInstance.setEntityType(EntityType.SECTOR);
        taskInstance.setEntityId(IdGenerator.generate("S").getId());
        return taskInstance;
    }

    public static DiscoveryTaskInstance getTaskDiscoveryTaskInstance(LegionTaskStateMachineState state) {
        DiscoveryTaskInstance taskInstance = getDiscoveryTaskInstance(state);
        taskInstance.setEntityType(EntityType.TASK);
        taskInstance.setEntityId(IdGenerator.generate("TI").getId());
        return taskInstance;
    }

    public static DiscoveryTaskInstance getVpaDiscoveryTaskInstance(LegionTaskStateMachineState state) {
        DiscoveryTaskInstance taskInstance = getDiscoveryTaskInstance(state);
        taskInstance.setEntityType(EntityType.TASK);
        taskInstance.setEntityId(IdGenerator.generate("VPA@").getId());
        return taskInstance;
    }

    public static DiscoveryTaskInstance getExternalEntityDiscoveryTaskInstance(LegionTaskStateMachineState state) {
        DiscoveryTaskInstance taskInstance = getDiscoveryTaskInstance(state);
        taskInstance.setEntityType(EntityType.EXTERNAL);
        taskInstance.setEntityId(IdGenerator.generate("VPA@").getId());
        return taskInstance;
    }

    public static DiscoveryTaskInstance getAgentEntityDiscoveryTaskInstance(LegionTaskStateMachineState state) {
        DiscoveryTaskInstance taskInstance = getDiscoveryTaskInstance(state);
        taskInstance.setEntityType(EntityType.AGENT);
        taskInstance.setEntityId(IdGenerator.generate("GU").getId());
        return taskInstance;
    }

    public static List<DiscoveryTaskInstance> getDiscoveryTaskInstanceListMultipleEntities(LegionTaskStateMachineState state, int tasksPerValidEntityType) {
        List<DiscoveryTaskInstance> taskInstances = new ArrayList<>();
        EnumSet entityTypes = EnumSet.allOf(EntityType.class);
        entityTypes.stream().forEach(entityType -> {
            for (int i = 0; i < tasksPerValidEntityType; i++) {
                DiscoveryTaskInstance taskInstance = getDiscoveryTaskInstance((EntityType) entityType, LegionTaskStateMachineState.COMPLETED);
                if (taskInstance == null) {
                    return;
                }
                taskInstances.add(taskInstance);
            }
        });
        return taskInstances;
    }

    public static List<DiscoveryTaskInstance> getDiscoveryTaskInstanceList(LegionTaskStateMachineState state, int size) {
        List<DiscoveryTaskInstance> taskInstances = new ArrayList<>();
        for (int i=0; i<size; i++) {
            taskInstances.add(getDiscoveryTaskInstance(state));
        }
        return taskInstances;
    }

    public static TaskFilters getTaskFilters() {
        final InputStream data = TaskDiscoveryServiceImpl.class.getClassLoader().getResourceAsStream("test-filters.json");
        return com.phonepe.merchant.legion.core.utils.SerDe.readValue(data,TaskFilters.class);
    }

    public static TaskFilters getFiltersOnTasks() {
        final InputStream data = TaskFilterServiceImpl.class.getClassLoader().getResourceAsStream("test-staticFilter.json");
        com.phonepe.merchant.legion.core.utils.SerDe.init(new ObjectMapper());
        return com.phonepe.merchant.legion.core.utils.SerDe.readValue(data,TaskFilters.class);
    }

    public static TaskFiltersV2 getFiltersOnTasksV2() {
        final InputStream data = TaskFilterServiceImpl.class.getClassLoader().getResourceAsStream("test-staticFilterV2.json");
        com.phonepe.merchant.legion.core.utils.SerDe.init(new ObjectMapper());
        return com.phonepe.merchant.legion.core.utils.SerDe.readValue(data,TaskFiltersV2.class);
    }

    public static Double getTaskDistance(DiscoveryTaskInstance taskInstance) {
        return taskInstance.getPoints()*10.0;
    }

    public static SearchResponse getEsSearchResponse(List<DiscoveryTaskInstance> tasks, boolean locationBasedSorting) {
        float score = 0.2345f;
        List<SearchHit> searchHitsList = new ArrayList<>();
        for (DiscoveryTaskInstance task: tasks) {
            SearchHit hit = new SearchHit(1, IdGenerator.generate("ID").getId(), new Text("elastic_type"), null, null);
            BytesReference source = new BytesArray(com.phonepe.merchant.legion.core.utils.SerDe.writeValueAsString(task));
            hit.sourceRef(source);
            hit.score(2.123f);
            if (locationBasedSorting) {
                Double distance = getTaskDistance(task);
                Object[] values = {distance};
                DocValueFormat[] docValueFormats = {new DocValueFormat.Decimal(distance.toString())};
                hit.sortValues(new SearchSortValues(values, docValueFormats));
            }
            searchHitsList.add(hit);
        }
        SearchHit[] searchHits = new SearchHit[searchHitsList.size()];
        searchHits = searchHitsList.toArray(searchHits);
        SearchHits hits = new SearchHits(searchHits,
                new TotalHits(tasks.size(), TotalHits.Relation.EQUAL_TO),
                100.0f);
        Aggregations aggregations = new Aggregations(new ArrayList<>());
        SearchResponseSections searchResponseSections = new SearchResponseSections( hits, aggregations, null, false, null, null, 5 );
        return new SearchResponse(searchResponseSections,"scrollId", 1, 1, 0, 0l, new ShardSearchFailure[] {},null);
    }

    public static SearchResponse getEsSearchResponseWithAggregation(List<DiscoveryTaskInstance> tasks, boolean locationBasedSorting, Aggregations aggregations) {
        float score = 0.2345f;
        List<SearchHit> searchHitsList = new ArrayList<>();
        for (DiscoveryTaskInstance task: tasks) {
            SearchHit hit = new SearchHit(1, IdGenerator.generate("ID").getId(), new Text("elastic_type"), null, null);
            BytesReference source = new BytesArray(com.phonepe.merchant.legion.core.utils.SerDe.writeValueAsString(task));
            hit.sourceRef(source);
            hit.score(2.123f);
            if (locationBasedSorting) {
                Double distance = getTaskDistance(task);
                Object[] values = {distance};
                DocValueFormat[] docValueFormats = {new DocValueFormat.Decimal(distance.toString())};
                hit.sortValues(new SearchSortValues(values, docValueFormats));
            }
            searchHitsList.add(hit);
        }
        SearchHit[] searchHits = new SearchHit[searchHitsList.size()];
        searchHits = searchHitsList.toArray(searchHits);
        SearchHits hits = new SearchHits(searchHits,
                new TotalHits(tasks.size(), TotalHits.Relation.EQUAL_TO),
                100.0f);

        SearchResponseSections searchResponseSections = new SearchResponseSections( hits, aggregations, null, false, null, null, 5 );
        return new SearchResponse(searchResponseSections,"scrollId", 1, 1, 0, 0l, new ShardSearchFailure[] {},null);
    }

    public static TaskSearchRequest getAgentTaskSearchRequest(String actor, int pageNo, int pageSize) {
        TaskSearchRequest request = AssignedViewTaskFetchRequest.builder().assignedTo(actor)
                                                                            .needScheduledTasks(true)
                                                                            .startDate(123232332L)
                                                                            .endDate(34343434343L)
                .build();
        request.setLocation(EsLocationRequest.builder()
                .lat(0.0)
                .lon(0.0)
                .build());
        request.setPageNo(pageNo);
        request.setPageSize(pageSize);
        request.setTaskSearchRequestType(TaskSearchRequestType.ASSIGNED_VIEW);
        request.setFilters(new ArrayList<>());
        return request;
    }

    public static TaskSearchRequest getDiscoveryTaskSearchRequest(int pageNo, int pageSize, TaskSearchRequestType taskSearchRequestType) {
        TaskSearchRequest request = DiscoveryViewTaskSearchRequest.builder().build();
        request.setLocation(EsLocationRequest.builder()
                .lat(0.0)
                .lon(0.0)
                .build());
        request.setPageNo(pageNo);
        request.setPageSize(pageSize);
        request.setSorter(Sorter.RESCHEDULE);
        request.setTaskSearchRequestType(taskSearchRequestType);
        request.setFilters(new ArrayList<>());
        return request;
    }

    public static TaskSearchRequest getDiscoveryTaskSearchRequestWithLocationSort(int pageNo, int pageSize) {
        TaskSearchRequest request = DiscoveryViewTaskSearchRequest.builder().build();
        request.setLocation(EsLocationRequest.builder()
                .lat(0.0)
                .lon(0.0)
                .build());
        request.setPageNo(pageNo);
        request.setPageSize(pageSize);
        request.setSorter(Sorter.LOCATION);
        request.setTaskSearchRequestType(TaskSearchRequestType.DISCOVERY_VIEW);
        request.setFilters(new ArrayList<>());
        return request;
    }

    public static TaskSearchRequest getSectorDiscoveryTaskSearchRequestWithLocationSort(int pageNo, int pageSize) {
        TaskSearchRequest request = DiscoveryViewTaskSearchRequest.builder().build();
        request.setLocation(EsLocationRequest.builder()
                .lat(0.0)
                .lon(0.0)
                .build());
        request.setPageNo(pageNo);
        request.setPageSize(pageSize);
        request.setSorter(Sorter.LOCATION);
        request.setTaskSearchRequestType(TaskSearchRequestType.SECTOR_DISCOVERY_VIEW);
        request.setFilters(new ArrayList<>());
        return request;
    }

    public static SectorDiscoveryViewTaskSearchRequest getSectorDiscoveryTaskSearchRequest(int pageNo, int pageSize) {
        SectorDiscoveryViewTaskSearchRequest request = SectorDiscoveryViewTaskSearchRequest.builder().build();
        request.setSectorId("KA03BELO1");
        request.setLocation(EsLocationRequest.builder()
                .lat(0.0)
                .lon(0.0)
                .build());
        request.setPageNo(pageNo);
        request.setPageSize(pageSize);
        request.setSorter(Sorter.RESCHEDULE);
        request.setTaskSearchRequestType(TaskSearchRequestType.DISCOVERY_VIEW);
        request.setFilters(new ArrayList<>());
        return request;
    }

    public static Aggregations getDummyStatsAggregation(String aggName) {
        List<Aggregation> aggregations = new ArrayList<>();
        List<StringTerms.Bucket> buckets = new ArrayList<>();
        aggregations.add(new StringTerms(aggName,null,null,0,1,null,null,0,false,0,buckets,0L));
        return new Aggregations(aggregations);
    }

    public static PrioritiseStrategyConfig getPrioritiseStrategyConfig(String chimeraPriorityKey) {
        return PrioritiseStrategyConfig.builder()
                .defaultPrioritiseStrategyPriority(PrioritiseStrategyPriority.builder().build())
                .treatmentGroup("priority_experiment")
                .priorities(List.of(PrioritiseStrategyPriority.builder()
                                .chimeraFilterKey(chimeraPriorityKey)
                                .weight(2.0f)
                        .build())).build();
    }


//    public static Aggregations getDummyFilterAggregation(String agg, List<StringTerms.Bucket> bucket) {
//        List<Aggregation> aggregations = new ArrayList<>();
//        aggregations.add(new StringTerms(agg,null,0,1,null,null,null,0,false,0,bucket,0));
//        return new Aggregations(aggregations);
//    }
   /* public static SearchResponse getDummySearchResponse() {
        float score = 0.2345f;
        List<SearchHit> searchHitsList = new ArrayList<>();

        SearchHit hit = new SearchHit(1, IdGenerator.generate("ID").getId(), new Text("elastic_type"), null, null);
        List<DiscoveryTaskInstance> taskInstances = new ArrayList<>();
        EnumSet entityTypes = EnumSet.allOf(EntityType.class);
        entityTypes.stream().forEach(entityType -> {
            for (int i=0;i<5;i++) {
                DiscoveryTaskInstance taskInstance = getDiscoveryTaskInstance((EntityType) entityType, LegionTaskStateMachineState.COMPLETED);
                if (taskInstance == null) {
                    return;
                }
                taskInstances.add(taskInstance);
            }
        });
        for (DiscoveryTaskInstance task: taskInstances) {
            BytesReference source = new BytesArray(SerDe.writeValueAsString(task));
            hit.sourceRef(source);
            hit.score(2.123f);
            searchHitsList.add(hit);
        }
        SearchHit[] searchHits = new SearchHit[searchHitsList.size()];
        searchHits = searchHitsList.toArray(searchHits);
        SearchHits hits = new SearchHits(searchHits,
                new TotalHits(taskInstances.size(), TotalHits.Relation.EQUAL_TO),
                100.0f);

    }*/



    public static StoredTaskInstance getStoredTaskInstance() {
        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder()
                .taskInstanceId(IdGenerator.generate("T").getId())
                .taskDefinitionId(IdGenerator.generate(TASK_DEFINITION_ID_PREFIX).getId())
                .actionId("DUMMY_ACTION")
                .curState(LegionTaskStateMachineState.VERIFICATION_SUCCESS)
                .entityId(IdGenerator.generate("M").getId())
                .entityType(EntityType.MERCHANT)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .createdBy("Saransh")
                .updatedBy("Saransh")
                .build();
        List<StoredTaskTransition> storedTaskTransitions = new ArrayList<>();
        Date date = new Date();
        for (LegionTaskStateMachineState state: EnumSet.allOf(LegionTaskStateMachineState.class)) {
            StoredTaskTransition storedTaskTransition = StoredTaskTransition.builder()
                    .actor("ACTOR")
                    .toState(state)
                    .createdAt(date)
                    .build();
            storedTaskTransitions.add(storedTaskTransition);
            if (state == LegionTaskStateMachineState.CREATED) {
                Date date1 = new Date();
                date1.setTime(0);
                storedTaskTransition.setCreatedAt(date1);
            }
        }
        storedTaskInstance.setTaskTransitions(storedTaskTransitions);
        return storedTaskInstance;
    }

    public static Date getNDaysAgo(int n) {
        long DAY_IN_MS = 1000 * 60 * 60 * 24;
        return new Date(System.currentTimeMillis() - (7 * DAY_IN_MS));
    }

    public static SectorMapViewTaskSearchRequest getAssignedSectorTaskRequest(String userId, int pageNo, int pageSize) {
        SectorMapViewTaskSearchRequest assignedTaskSearchRequest = AssignedSectorMapViewTaskSearchRequest.builder().assignedTo(userId).build();
        assignedTaskSearchRequest.setSectorId("string");
        assignedTaskSearchRequest.setPageNo(pageNo);
        assignedTaskSearchRequest.setPageSize(pageSize);
        assignedTaskSearchRequest.setFilters(new ArrayList<>());
        assignedTaskSearchRequest.setTaskSearchRequestType(TaskViewRequestType.ASSIGNED_VIEW);
        assignedTaskSearchRequest.setSorter(Sorter.NONE);
        return assignedTaskSearchRequest;
    }

    public static SectorMapViewTaskSearchRequest getDiscoverableSectorTaskRequest(String userId, int pageNo, int pageSize) {
        SectorMapViewTaskSearchRequest discoverableTaskSearchRequest = DiscoverySectorMapViewTaskSearchRequest.builder().build();
        discoverableTaskSearchRequest.setSectorId("string");
        discoverableTaskSearchRequest.setPageNo(pageNo);
        discoverableTaskSearchRequest.setPageSize(pageSize);
        discoverableTaskSearchRequest.setFilters(new ArrayList<>());
        discoverableTaskSearchRequest.setTaskSearchRequestType(TaskViewRequestType.DISCOVERY_VIEW);
        discoverableTaskSearchRequest.setSorter(Sorter.NONE);
        discoverableTaskSearchRequest.setLocation(EsLocationRequest.builder()
                .lon(0.0)
                .lat(0.0)
                .build());
        return discoverableTaskSearchRequest;
    }
    public static void addGeoDistanceFilter(BoolQueryBuilder query, EsLocationRequest location, double maxGeoSortDistance) {
        query.filter(QueryBuilders.geoDistanceQuery(DiscoveryTaskInstance.DiscoveryTaskInstanceFieldNames.LOCATION)
                .point(location.getLat(), location.getLon())
                .distance(maxGeoSortDistance, DistanceUnit.KILOMETERS));
    }

    public static Shape getShapeForSectorId() {
        List<List<List<Double>>> coordinates = new ArrayList<>();
        coordinates.add(getPolygonCoordinates());
        return Polygon.builder()
                .coordinates(coordinates)
                .build();
    }

    public static List<List<Double>> getPolygonCoordinates() {
        List<List<Double>> polygonCoordinates = new ArrayList<>();
        polygonCoordinates.add(Arrays.asList(new Double[]{0.0,0.0}));
        polygonCoordinates.add(Arrays.asList(new Double[]{2.0,4.0}));
        polygonCoordinates.add(Arrays.asList(new Double[]{4.0,2.0}));
        return polygonCoordinates;
    }
}
