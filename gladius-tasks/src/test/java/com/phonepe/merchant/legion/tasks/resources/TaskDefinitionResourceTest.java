package com.phonepe.merchant.legion.tasks.resources;

import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.merchant.gladius.models.tasks.request.TaskDefinitionCreateRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskDefinitionFetchByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.response.TaskDefinitionInstance;
import com.phonepe.merchant.gladius.models.tasks.utils.LeadCreationConfig;
import com.phonepe.merchant.gladius.models.tasks.utils.LeadUpdationConfig;
import com.phonepe.merchant.legion.tasks.services.TaskDefinitionService;
import com.phonepe.models.response.GenericResponse;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;

import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getUserDetails;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TaskDefinitionResourceTest {

    private static TaskDefinitionService taskDefinitionService;
    private static TaskDefinitionResource taskDefinitionResource;

    @BeforeClass
    public static void init() {
        taskDefinitionService = mock(TaskDefinitionService.class);
        taskDefinitionResource = new TaskDefinitionResource(taskDefinitionService);
    }

    @Test
    public void create() {
        //arrange
        UserDetails userDetails = getUserDetails();
        TaskDefinitionCreateRequest request = TaskDefinitionCreateRequest.builder().build();
        TaskDefinitionInstance expectedResponse = TaskDefinitionInstance.builder().build();
        when(taskDefinitionService.save(request)).thenReturn(expectedResponse);

        //call
        GenericResponse<TaskDefinitionInstance> actualResponse = taskDefinitionResource.create(Optional.empty(), null,request,userDetails, null, null);

        //assert
        Assert.assertTrue(actualResponse.isSuccess());
        Assert.assertSame(expectedResponse,actualResponse.getData());

        //arrange
        when(taskDefinitionService.save(request)).thenReturn(null);

        //call
        actualResponse = taskDefinitionResource.create(Optional.empty(), null,request,userDetails, null, null);

        //assert
        Assert.assertFalse(actualResponse.isSuccess());
    }

    @Test
    public void fetch() {
        //arrange
        String taskDefinitionId = "TASK_DEFINITION_ID";
        TaskDefinitionFetchByIdRequest request = TaskDefinitionFetchByIdRequest.builder()
                .taskDefinitionId(taskDefinitionId)
                .build();
        TaskDefinitionInstance expectedResponse = TaskDefinitionInstance.builder()
                .taskDefinitionId(taskDefinitionId)
                .build();
        when(taskDefinitionService.getFromDb(request)).thenReturn(expectedResponse);

        //call
        GenericResponse<TaskDefinitionInstance> actualResponse = taskDefinitionResource.fetch(Optional.empty(), null,taskDefinitionId);

        //assert
        Assert.assertTrue(actualResponse.isSuccess());
        Assert.assertSame(expectedResponse,actualResponse.getData());

        //arrange
        when(taskDefinitionService.getFromDb(request)).thenReturn(null);

        //call
        actualResponse = taskDefinitionResource.fetch(Optional.empty(), null,taskDefinitionId);

        //assert
        Assert.assertFalse(actualResponse.isSuccess());
    }

    @Test
    public void update() {
        //arrange
        UserDetails userDetails = getUserDetails();
        TaskDefinitionInstance request = TaskDefinitionInstance.builder().build();
        TaskDefinitionInstance expectedResponse = TaskDefinitionInstance.builder().build();
        when(taskDefinitionService.update(request)).thenReturn(expectedResponse);

        //call
        GenericResponse<TaskDefinitionInstance> actualResponse = taskDefinitionResource.update(Optional.empty(), null,request,userDetails, null, null);

        //assert
        Assert.assertTrue(actualResponse.isSuccess());
        Assert.assertSame(expectedResponse,actualResponse.getData());

        //arrange
        when(taskDefinitionService.update(request)).thenReturn(null);

        //call
        actualResponse = taskDefinitionResource.update(Optional.empty(), null,request,userDetails, null, null);

        //assert
        Assert.assertFalse(actualResponse.isSuccess());
    }


    @Test
    public void testGetHistory() {
        LeadCreationConfig mockResponses = mock(LeadCreationConfig.class);
        when(taskDefinitionService.getLeadCreationConfig()).thenReturn(mockResponses);
        GenericResponse<LeadCreationConfig> response = taskDefinitionResource.getLeadCreationConfig();
        Assert.assertNotNull(response);
        Assertions.assertEquals(mockResponses, response.getData());
    }

    @Test
    public void test_getLeadCreationConfig() {
        LeadUpdationConfig mockResponses = mock(LeadUpdationConfig.class);
        when(taskDefinitionService.getLeadUpdationConfig(any())).thenReturn(mockResponses);
        GenericResponse<LeadUpdationConfig> response = taskDefinitionResource.getLeadUpdationConfig("test-definition");
        Assert.assertNotNull(response);
        Assertions.assertEquals(mockResponses, response.getData());
    }
}
