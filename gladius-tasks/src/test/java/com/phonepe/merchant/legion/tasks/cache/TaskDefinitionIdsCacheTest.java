package com.phonepe.merchant.legion.tasks.cache;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.merchant.gladius.models.tasks.domain.TaskDefinitionAttributes;
import com.phonepe.merchant.gladius.models.tasks.domain.TaskDefinitionTenants;
import com.phonepe.merchant.gladius.models.tasks.enums.Priority;
import com.phonepe.merchant.gladius.models.tasks.request.Category;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskDefinition;
import com.phonepe.merchant.legion.core.cache.CacheConfig;
import com.phonepe.merchant.legion.core.cache.CacheName;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.repository.TaskDefinitionRepository;
import com.phonepe.merchant.legion.tasks.utils.CacheUtils;
import org.junit.Assert;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.phonepe.merchant.legion.core.cache.CacheName.ALL_TASK_DEFINITION;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class TaskDefinitionIdsCacheTest extends LegionTaskBaseTest {

    private CacheUtils cacheUtils;
    private MetricRegistry metricRegistry;
    private Map<CacheName, CacheConfig> cacheConfigs;
    private TaskDefinitionIdsCache taskDefinitionIdsCache;
    private ObjectMapper objectMapper = new ObjectMapper();
    private TaskDefinitionRepository taskDefinitionRepository;

    @BeforeEach
    void setUp() {
        SerDe.init(objectMapper);
        metricRegistry = new MetricRegistry();
        cacheUtils = mock(CacheUtils.class);
        cacheConfigs = Map.of(ALL_TASK_DEFINITION, new CacheConfig());
        this.taskDefinitionRepository = mock(TaskDefinitionRepository.class);
        taskDefinitionIdsCache = new TaskDefinitionIdsCache(cacheConfigs, ()->taskDefinitionRepository, metricRegistry, cacheUtils);
    }

    @Test
    void testConstructor_CacheRegister() {
        verify(cacheUtils).registerCache(ALL_TASK_DEFINITION, taskDefinitionIdsCache);
    }


    @Test
    void test_FetchAllDefinitionIdsDetails() {
        TaskDefinitionAttributes definitionAttributes = TaskDefinitionAttributes.builder()
                .tenants(TaskDefinitionTenants.builder()
                        .tenants(Set.of("CIG_1")).build()).category(Category.LENDING)
                .taskType("SS_DEPLOYMENTS").build();
        StoredTaskDefinition storedTaskDefinition = StoredTaskDefinition.builder()
                .priority(Priority.P0).actionId("action-id")
                .taskDefinitionId("task-definition-id").build();
        storedTaskDefinition.setDefinitionAttributes(SerDe.writeValueAsBytes(definitionAttributes));
        List<StoredTaskDefinition> storedTaskDefinitions = List.of(storedTaskDefinition);
        when(taskDefinitionRepository.getAll()).thenReturn(storedTaskDefinitions);
        taskDefinitionRepository.save(storedTaskDefinition);
        Set<String> result = taskDefinitionIdsCache.getAllClients("task-definition-id");
        assertNotNull(result);
        assertEquals(1, result.size());
    }
    @Test
    void test_FetchAllDefinitionIdsDetails_empty() {
        TaskDefinitionAttributes definitionAttributes = TaskDefinitionAttributes.builder()
                .category(Category.LENDING).build();
        StoredTaskDefinition storedTaskDefinition = StoredTaskDefinition.builder()
                .priority(Priority.P0).actionId("action-id")
                .taskDefinitionId("task-definition-id").build();
        storedTaskDefinition.setDefinitionAttributes(SerDe.writeValueAsBytes(definitionAttributes));
        List<StoredTaskDefinition> storedTaskDefinitions = List.of(storedTaskDefinition);
        when(taskDefinitionRepository.getAll()).thenReturn(storedTaskDefinitions);
        Set<String> result = taskDefinitionIdsCache.getAllClients("task-definition-id");
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    @Test
    void test_FetchAllDefinitionIdsDetails_failure() {
        when(taskDefinitionRepository.getAll()).thenThrow(new RuntimeException("nopes"));
        assertThrows(LegionException.class, () -> taskDefinitionIdsCache.getAllClients("task-definition-id"));
    }


    @Test
    void mockStart() throws Exception {
        taskDefinitionIdsCache.get("DEFAULT");
        taskDefinitionIdsCache.start();
        Assert.assertTrue(true);
    }

    @Test
    void mockStop() throws Exception {
        taskDefinitionIdsCache.get("DEFAULT");
        taskDefinitionIdsCache.stop();
        Assert.assertTrue(true);
    }
}
