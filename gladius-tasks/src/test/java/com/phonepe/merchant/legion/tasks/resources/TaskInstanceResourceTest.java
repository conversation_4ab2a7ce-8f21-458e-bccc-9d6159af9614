package com.phonepe.merchant.legion.tasks.resources;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.gandalf.models.authn.UserType;
import com.phonepe.gandalf.models.user.UserDetails;
import com.phonepe.merchant.gladius.models.tasks.domain.TaskInstance;
import com.phonepe.merchant.gladius.models.tasks.enums.TaskSchedulingType;
import com.phonepe.merchant.gladius.models.tasks.request.ClientTaskDeleteRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskCompletionByIdRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskCompletionByTaskTypeRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskExpireRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskManualVerificationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskMetaUpdateRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskSchedulingPayload;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskAssignRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskRecreationRequest;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.legion.core.utils.AuthUserDetails;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.actor.TaskActionMessagePublisher;
import com.phonepe.merchant.legion.tasks.actor.message.TaskVerifyRmqMessage;
import com.phonepe.merchant.legion.tasks.services.TaskInstanceHistoryService;
import com.phonepe.merchant.legion.tasks.services.TaskInstanceManagementService;
import com.phonepe.merchant.legion.tasks.services.TaskManagementService;
import com.phonepe.merchant.legion.tasks.services.TaskRecreationService;
import com.phonepe.merchant.legion.tasks.utils.TaskInstanceTransformationUtils;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.olympus.im.client.security.ServiceUserPrincipal;
import com.phonepe.platform.clockwork.model.SchedulingResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import java.util.Optional;

import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getUserDetails;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.anyBoolean;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;


@Slf4j
public class TaskInstanceResourceTest {

    private static TaskInstanceManagementService taskInstanceManagementService;
    private static TaskInstanceHistoryService taskInstanceHistoryService;
    private static TaskManagementService taskManagementService;
    private static TaskInstanceResource taskInstanceResource;
    private static TaskRecreationService taskRecreationService;
    private static MockedStatic<TaskActionMessagePublisher> taskActionMessagePublisherMockedStatic;

    @BeforeClass
    public static void init() {
        taskActionMessagePublisherMockedStatic = mockStatic(TaskActionMessagePublisher.class);
        taskInstanceManagementService = mock(TaskInstanceManagementService.class);
        taskManagementService = mock(TaskManagementService.class);
        taskRecreationService = mock(TaskRecreationService.class);
        taskInstanceHistoryService = mock(TaskInstanceHistoryService.class);
        taskInstanceResource = new TaskInstanceResource(taskInstanceManagementService, taskManagementService, taskInstanceHistoryService, taskRecreationService);
        SerDe.init(new ObjectMapper());
    }

    @AfterClass
    public static void tearDownClass() {
        taskActionMessagePublisherMockedStatic.close();
        taskActionMessagePublisherMockedStatic = null;
    }

    @Test
    public void command() {
        //arrange
        UserDetails userDetails = getUserDetails();
        TaskAssignRequest request = TaskAssignRequest.builder().build();
        StoredTaskInstance response = StoredTaskInstance.builder().build();
        TaskInstance expectedResponse = TaskInstanceTransformationUtils.toTaskInstance(response);
        when(taskManagementService.command(request, userDetails.getExternalReferenceId()))
                .thenReturn(response);
        //call
        GenericResponse<TaskInstance> actualResponse = taskInstanceResource.command(Optional.empty(), null, request, userDetails, null, null);

        //assert
        Assert.assertTrue(actualResponse.isSuccess());
        Assert.assertEquals(expectedResponse, actualResponse.getData());

        //TODO: CAN RESPONSE BE NULL?
    }

    @Test
    public void internalCommand() {
        //arrange
        TaskAssignRequest request = TaskAssignRequest.builder().build();
        StoredTaskInstance response = StoredTaskInstance.builder().build();
        TaskInstance expectedResponse = TaskInstanceTransformationUtils.toTaskInstance(response);
        when(taskManagementService.command(request, "housekeeping-ep"))
                .thenReturn(response);
        //call
        GenericResponse<TaskInstance> actualResponse = taskInstanceResource.internalCommand(Optional.empty(), request);

        //assert
        Assert.assertTrue(actualResponse.isSuccess());
        Assert.assertEquals(expectedResponse, actualResponse.getData());

        //TODO: CAN RESPONSE BE NULL?
    }

    @Test
    public void verify() throws Exception {
        //arrange
        TaskVerifyRmqMessage request = TaskVerifyRmqMessage.builder()
                .build();
        when(TaskActionMessagePublisher.taskVerificationRequest(request)).thenReturn(true);

        //call
        GenericResponse<Void> response = taskInstanceResource.verify(Optional.empty(), null, request);

        //assert
        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void verifyTask() {
        when(taskManagementService.verifyTask(any())).thenReturn(StoredTaskInstance.builder().taskInstanceId("TI123").build());
        UserDetails userDetails = getUserDetails();

        GenericResponse<TaskInstance> response = taskInstanceResource.verifyTask(Optional.empty(), TaskManualVerificationRequest.builder().build(), userDetails, null, null);

        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void expireTask() {
        when(taskManagementService.expireTask(any())).thenReturn(StoredTaskInstance.builder().taskInstanceId("TI123").build());
        UserDetails userDetails = getUserDetails();

        GenericResponse<TaskInstance> response = taskInstanceResource.expireTask(Optional.empty(), TaskExpireRequest.builder().build(), userDetails, null, null);

        Assert.assertTrue(response.isSuccess());
    }

    @Test
    public void deleteTask() {
        try (MockedStatic<AuthUserDetails> profileUtilsMockedStaticMockedStatic = Mockito.mockStatic(AuthUserDetails.class)) {
            when(AuthUserDetails.getLegionUserId(any(AgentProfile.class), any(UserDetails.class), any(ServiceUserPrincipal.class))).thenReturn("User");
            when(taskManagementService.deleteTask(any())).thenReturn(StoredTaskInstance.builder().taskInstanceId("TI123").build());
            UserDetails userDetails = getUserDetails();

            GenericResponse<TaskInstance> response = taskInstanceResource.deleteTask(ClientTaskDeleteRequest.builder().build(), null, null);

            Assert.assertTrue(response.isSuccess());
        }
    }

    @Test
    public void get() {
        //arrange
        String request = "TASK_INSTANCE_ID";
        TaskInstance expectedResponse = TaskInstance.builder().build();
        TaskByIdRequest taskByIdRequest = TaskByIdRequest.builder()
                .taskInstanceId(request)
                .build();
        when(taskInstanceManagementService.getById(taskByIdRequest)).thenReturn(expectedResponse);

        //call
        GenericResponse<TaskInstance> actualResponse = taskInstanceResource.get(Optional.empty(), null, request);

        //assert
        Assert.assertTrue(actualResponse.isSuccess());
        Assert.assertEquals(expectedResponse, actualResponse.getData());

        //arrange
        when(taskInstanceManagementService.getById(taskByIdRequest)).thenReturn(null);

        //call
        actualResponse = taskInstanceResource.get(Optional.empty(), null, request);

        //assert
        Assert.assertFalse(actualResponse.isSuccess());

    }

    @Test
    public void update() {
        //arrange
        String taskInstanceId = "TASK_INSTANCE_ID";
        TaskInstance request = TaskInstance.builder()
                .taskInstanceId(taskInstanceId)
                .active(true)
                .build();
        doNothing().when(taskInstanceManagementService)
                .update(eq(taskInstanceId), eq(TaskInstanceTransformationUtils.update(request)), anyBoolean());

        //call
        GenericResponse<Void> actualResponse = taskInstanceResource.update(Optional.empty(), null, taskInstanceId, request);

        //assert
        Assert.assertTrue(actualResponse.isSuccess());
    }

    @Test
    public void delete() {
        //arrange
        String request = "TASK_INSTANCE_ID";
        doNothing().when(taskInstanceManagementService).delete(request);

        //call
        GenericResponse<Void> actualResponse = taskInstanceResource.delete(Optional.empty(), null, request);

        //assert
        Assert.assertTrue(actualResponse.isSuccess());
    }

    @Test
    public void taskRecreate() {
        String taskInstanceId = "TASK_INSTANCE_ID";
        TaskRecreationRequest taskRecreationRequest = TaskRecreationRequest.builder()
                .taskInstanceId(taskInstanceId)
                .build();
        StoredTaskInstance response = StoredTaskInstance.builder().build();
        TaskInstance expectedResponse = null;
        when(taskRecreationService.recreateTask(taskRecreationRequest))
                .thenReturn(response);

        GenericResponse<TaskInstance> actualResponse = taskInstanceResource.taskRecreate(Optional.empty(), taskRecreationRequest);

        Assert.assertTrue(actualResponse.isSuccess());
        Assert.assertEquals(expectedResponse, actualResponse.getData());

    }

    @Test
    public void clockworkScheduler() {
        TaskRecreationRequest request = TaskRecreationRequest.builder().build();
        SchedulingResponse response = SchedulingResponse.builder().build();

        when(taskRecreationService.clockworkScheduler(request.getTaskInstanceId()))
                .thenReturn(response);

        SchedulingResponse actualResponse = taskRecreationService.clockworkScheduler(request.getTaskInstanceId());

        Assert.assertEquals(response, actualResponse);
    }

    @Test
    public void rescheduleTaskTest() {
        TaskSchedulingPayload request = TaskSchedulingPayload.builder()
                .scheduleType(TaskSchedulingType.CANCEL).build();

        GenericResponse<Void> genericResponse = GenericResponse.<Void>builder()
                .success(true)
                .build();
        when(taskInstanceManagementService.rescheduleTask(any(), anyString())).thenReturn(true);

        GenericResponse<Void> actualResponse = taskInstanceResource.rescheduleTask(Optional.empty(), request, UserDetails.builder()
                .userId("mohit")
                .userType(UserType.USER).build(), null, null);

        Assert.assertEquals(genericResponse, actualResponse);
    }

    @Test
    public void updateMetaTest() {
        TaskMetaUpdateRequest request = TaskMetaUpdateRequest.builder()
                .rescheduleAt(1699783682000L)
                .build();

        taskInstanceManagementService.updateTaskInstanceMeta(request);

        GenericResponse<Void> response = taskInstanceResource.updateMeta(Optional.empty(), request, UserDetails.builder()
                .userId("saransh")
                .userType(UserType.USER).build(), null, null);
        Assert.assertNotNull(response);
    }

    @Test
    public void forceTaskCompletionTest() {
        try (MockedStatic<AuthUserDetails> profileUtilsMockedStaticMockedStatic = Mockito.mockStatic(AuthUserDetails.class)) {
            when(AuthUserDetails.getLegionUserId(any(AgentProfile.class), any(UserDetails.class), any(ServiceUserPrincipal.class))).thenReturn("User");
            TaskCompletionByTaskTypeRequest taskCompletionByTaskTypeRequest = TaskCompletionByTaskTypeRequest.builder()
                    .entityId("MID_SID")
                    .completedBy("Mohit")
                    .taskType("SS_DEPLOYMENTS").build();

            doNothing().when(taskInstanceManagementService).completeAllTasksByType(any());

            GenericResponse<Void> response = taskInstanceResource.completeAllTasksByTaskType(taskCompletionByTaskTypeRequest, null, null);
            Assert.assertNotNull(response);
        }
    }

    @Test
    public void markTaskCompletionTest() {
        try (MockedStatic<AuthUserDetails> profileUtilsMockedStaticMockedStatic = Mockito.mockStatic(AuthUserDetails.class)) {
            when(AuthUserDetails.getLegionUserId(any(AgentProfile.class), any(UserDetails.class), any(ServiceUserPrincipal.class))).thenReturn("USER");
            TaskCompletionByIdRequest taskCompletionByIdRequest = TaskCompletionByIdRequest.builder()
                    .taskInstanceId("")
                    .completedBy("Mohit")
                    .actorId("").build();

            doNothing().when(taskInstanceManagementService).completeTaskById(any());

            GenericResponse<Void> response = taskInstanceResource.markTaskAsComplete(taskCompletionByIdRequest, null, null);
            Assert.assertNotNull(response);
        }
    }

}
