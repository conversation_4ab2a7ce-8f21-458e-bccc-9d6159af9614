package com.phonepe.merchant.legion.tasks.cache;

import com.codahale.metrics.MetricRegistry;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.merchant.gladius.models.configs.chimeraconfig.LeadIntents;
import com.phonepe.merchant.legion.core.cache.CacheConfig;
import com.phonepe.merchant.legion.core.cache.CacheName;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.repository.impl.ChimeraRepositoryImpl;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.merchant.legion.tasks.utils.CacheUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class LeadIntentCacheTest {

    private ChimeraRepositoryImpl chimeraRepository;
    private CacheUtils cacheUtils;
    private MetricRegistry metricRegistry;
    private Map<CacheName, CacheConfig> cacheConfigs;
    private LeadIntentCache leadIntentCache;
    private ObjectMapper objectMapper = new ObjectMapper();
    private static final String LEAD_INTENT_CHIMERA_KEY  = "gladius_lead_intents";
    private static final CacheName CACHE_NAME = CacheName.LEAD_INTENTS;

    @BeforeEach
    void setUp() {
        SerDe.init(objectMapper);
        metricRegistry = new MetricRegistry();
        cacheUtils = mock(CacheUtils.class);
        cacheConfigs = Map.of(CacheName.LEAD_INTENTS, new CacheConfig());
        this.chimeraRepository = mock(ChimeraRepositoryImpl.class);
        leadIntentCache = new LeadIntentCache(cacheConfigs, chimeraRepository, metricRegistry, cacheUtils);
    }

    @Test
    void testConstructor_CacheRegister() {
        verify(cacheUtils).registerCache(CACHE_NAME, leadIntentCache);
    }

    @Test
    void test_FetchAllDefinitionIdsDetails() {
        when(chimeraRepository.getChimeraConfig(LEAD_INTENT_CHIMERA_KEY, LeadIntents.class)).thenReturn(LeadIntents.builder().intents(List.of("X")).build());
        LeadIntents result = leadIntentCache.get("DEFAULT_KEY");
        assertNotNull(result);
        assertEquals(1, result.getIntents().size());
    }

    @Test
    void test_FetchAllDefinitionIdsDetails_failure() {
        when(chimeraRepository.getChimeraConfig(LEAD_INTENT_CHIMERA_KEY, LeadIntents.class)).thenThrow(LegionException.class);
        Assertions.assertThrows(LegionException.class, ()-> leadIntentCache.get("DEFAULT_KEY"));
    }
}