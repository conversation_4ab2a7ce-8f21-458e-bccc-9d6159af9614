package com.phonepe.merchant.legion.tasks.services;


import com.flipkart.foxtrot.common.query.Filter;
import com.flipkart.foxtrot.common.query.general.EqualsFilter;
import com.phonepe.merchant.gladius.models.core.FoxtrotRequest;
import com.phonepe.merchant.gladius.models.core.response.MerchantPendingChargeResponse;
import com.phonepe.merchant.gladius.models.entitystore.Entity;
import com.phonepe.merchant.gladius.models.entitystore.EntityStoreRequest;
import com.phonepe.merchant.gladius.models.entitystore.ExternalEntity;
import com.phonepe.merchant.gladius.models.entitystore.MerchantEntity;
import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineEvent;
import com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState;
import com.phonepe.merchant.gladius.models.tasks.entitystore.DiscoveryTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.entitystore.EsLocationRequest;
import com.phonepe.merchant.gladius.models.tasks.enums.Namespace;
import com.phonepe.merchant.gladius.models.tasks.enums.Priority;
import com.phonepe.merchant.gladius.models.tasks.request.ActionToRemarkConfig;
import com.phonepe.merchant.gladius.models.tasks.request.ClientTaskDeleteRequest;
import com.phonepe.merchant.gladius.models.tasks.request.CreateTaskInstanceRequest;
import com.phonepe.merchant.gladius.models.tasks.request.IntentWithRemarks;
import com.phonepe.merchant.gladius.models.tasks.request.TaskCompletionNoteMeta;
import com.phonepe.merchant.gladius.models.tasks.request.TaskExpireRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskInstanceMeta;
import com.phonepe.merchant.gladius.models.tasks.request.TaskManualVerificationRequest;
import com.phonepe.merchant.gladius.models.tasks.request.TaskMetaUpdateRequest;
import com.phonepe.merchant.gladius.models.tasks.request.UserTaskCreationMeta;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskAssignRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCompleteRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskCreateRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskMarkAvailableRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.TaskStartRequest;
import com.phonepe.merchant.gladius.models.tasks.request.commands.UserTaskCreationRequest;
import com.phonepe.merchant.gladius.models.tasks.response.Campaign;
import com.phonepe.merchant.gladius.models.tasks.response.ExpiryPeriod;
import com.phonepe.merchant.gladius.models.tasks.response.TaskActionInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskDefinitionInstance;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaInformation;
import com.phonepe.merchant.gladius.models.tasks.response.TaskMetaType;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredCampaign;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredCommentsOnTask;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskAction;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskDefinition;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskTransition;
import com.phonepe.merchant.gladius.models.tasks.utils.LeadConfig;
import com.phonepe.merchant.gladius.models.tasks.utils.Miscellaneous;
import com.phonepe.merchant.gladius.models.tasks.utils.TaskCreationConfigParameters;
import com.phonepe.merchant.gladius.models.tasks.utils.UserTaskCreationConfig;
import com.phonepe.merchant.gladius.models.tasks.validation.ActionValidator;
import com.phonepe.merchant.gladius.models.tasks.validation.AndOperationContainer;
import com.phonepe.merchant.gladius.models.tasks.validation.LeafContainer;
import com.phonepe.merchant.gladius.models.tasks.validation.ValidationContainer;
import com.phonepe.merchant.gladius.models.tasks.validation.ValidationStrategy;
import com.phonepe.merchant.gladius.models.tasks.validation.configs.AgentLocationValidatorConfig;
import com.phonepe.merchant.gladius.models.tasks.validation.configs.BrickbatFormFillValidatorConfig;
import com.phonepe.merchant.gladius.models.tasks.validation.configs.ImageQcActionValidtorConfig;
import com.phonepe.merchant.gladius.models.tasks.validation.configs.LeadIntentValidatorConfig;
import com.phonepe.merchant.gladius.models.tasks.validation.configs.OnboardExternalEntityAsPhonepeMxnValidatorConfig;
import com.phonepe.merchant.gladius.models.tasks.verification.ActionVerifier;
import com.phonepe.merchant.gladius.models.tasks.verification.SysSyncVerificationStrategy;
import com.phonepe.merchant.gladius.models.tasks.verification.VerificationStrategy;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.AgentDeboardingVerifierConfig;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.BrickbatFormFillVerifierConfig;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.BusinessCategoryUpdateVerifierConfig;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.EdcDeploymentVerifierConfig;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.EdcReversePickupVerifierConfig;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.EventExistenceVerifierConfig;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.FlDriveVerifierConfig;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.MerchantKycVerifierConfig;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.MerchantLendingCompletionVerifierConfig;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.MultiEdcDeploymentVerifierConfig;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.NoActionVerifierConfig;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.OnboardExternalEntityAsPhonepeMxnVerifierConfig;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.OqcValidationVerifierConfig;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.P2MLmigrationVerifierConfig;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.PoaVerifierConfig;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.PobVerifierConfig;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.SelfOnboardingMerchantVerifierConfig;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.SingleEventVerificationConfig;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.SmartspeakerDueCollectionVerifierConfig;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.SmartspeakerReversePickupVerifierConfig;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.SmartspeakerTroubleshootVerifierConfig;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.SoundboxDeploymentVerifierConfig;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.StoreCheckInVerifierConfig;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.StoreQcVerifierConfig;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.TaskQcVerifierConfig;
import com.phonepe.merchant.gladius.models.tasks.verification.configs.VerificationConfig;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.exceptions.CoreErrorCode;
import com.phonepe.merchant.legion.core.exceptions.LegionException;
import com.phonepe.merchant.legion.core.models.ElasticSearchRequest;
import com.phonepe.merchant.legion.core.models.MerchantDetails;
import com.phonepe.merchant.legion.core.models.MerchantMeta;
import com.phonepe.merchant.legion.core.models.MerchantOnboardedByAgent;
import com.phonepe.merchant.legion.core.repository.ESRepository;
import com.phonepe.merchant.legion.core.repository.impl.ChimeraRepositoryImpl;
import com.phonepe.merchant.legion.core.services.AtlasService;
import com.phonepe.merchant.legion.core.services.LegionService;
import com.phonepe.merchant.legion.core.services.OdinService;
import com.phonepe.merchant.legion.core.services.TmsService;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.merchant.legion.external.services.ExternalEntityService;
import com.phonepe.merchant.legion.models.profile.enums.AgentStatus;
import com.phonepe.merchant.legion.models.profile.enums.AgentType;
import com.phonepe.merchant.legion.models.profile.request.VerificationMeta;
import com.phonepe.merchant.legion.models.profile.response.AgentProfile;
import com.phonepe.merchant.legion.tasks.DiscoveryTestUtils;
import com.phonepe.merchant.legion.tasks.LegionTaskBaseTest;
import com.phonepe.merchant.legion.tasks.actions.processor.impl.ActionVerificationProcessorImpl;
import com.phonepe.merchant.legion.tasks.actions.processor.impl.AssignmentActionValidationProcessor;
import com.phonepe.merchant.legion.tasks.actions.processor.impl.CompletionActionValidationProcessor;
import com.phonepe.merchant.legion.tasks.actions.processor.impl.StartActionValidationProcessor;
import com.phonepe.merchant.legion.tasks.actions.verifiers.AgentDeboardingVerifier;
import com.phonepe.merchant.legion.tasks.actions.verifiers.EdcDeploymentVerifier;
import com.phonepe.merchant.legion.tasks.actions.verifiers.EdcReversePickupVerifier;
import com.phonepe.merchant.legion.tasks.actions.verifiers.MerchantLendingCompletionVerifier;
import com.phonepe.merchant.legion.tasks.actions.verifiers.MultiEdcDeploymentVerifier;
import com.phonepe.merchant.legion.tasks.actions.verifiers.SelfOnboardingMerchantVerifier;
import com.phonepe.merchant.legion.tasks.actions.verifiers.SmartSpeakerReversePickupVerifier;
import com.phonepe.merchant.legion.tasks.actions.verifiers.SmartspeakerTroubleshootVerifier;
import com.phonepe.merchant.legion.tasks.entitystore.EntityStore;
import com.phonepe.merchant.legion.tasks.flows.TaskEngine;
import com.phonepe.merchant.legion.tasks.flows.TaskEngineImpl;
import com.phonepe.merchant.legion.tasks.flows.TransitionValidator;
import com.phonepe.merchant.legion.tasks.flows.transitions.AvailableTaskLegionTransition;
import com.phonepe.merchant.legion.tasks.flows.transitions.BoundedAssignTaskLegionTransition;
import com.phonepe.merchant.legion.tasks.flows.transitions.CreateTaskLegionTransition;
import com.phonepe.merchant.legion.tasks.flows.transitions.DeletedTaskLegionTransition;
import com.phonepe.merchant.legion.tasks.flows.transitions.ExecuteVerificationLegionTransition;
import com.phonepe.merchant.legion.tasks.flows.transitions.ExpiredTaskLegionTransition;
import com.phonepe.merchant.legion.tasks.flows.transitions.ManualVerificationTaskLegionTransition;
import com.phonepe.merchant.legion.tasks.flows.transitions.SelfAssignTaskLegionTransition;
import com.phonepe.merchant.legion.tasks.flows.transitions.StartTaskLegionTransition;
import com.phonepe.merchant.legion.tasks.flows.transitions.VerificationInitTaskLegionTransition;
import com.phonepe.merchant.legion.tasks.repository.TaskESRepository;
import com.phonepe.merchant.legion.tasks.search.query.search.TaskTypeFilterQuery;
import com.phonepe.merchant.legion.tasks.services.impl.TaskActionServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.TaskInstanceManagementServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.TaskManagementServiceImpl;
import com.phonepe.merchant.legion.tasks.services.impl.TaskVerificationServiceImpl;
import com.phonepe.merchant.legion.tasks.utils.LeadManagementConfiguration;
import com.phonepe.merchants.odin.models.merchant.CompleteTransactionMetaDataResponse;
import com.phonepe.merchants.odin.models.merchant.MerchantTransactionAttributeData;
import com.phonepe.models.merchants.DisplayState;
import com.phonepe.models.merchants.MerchantProfile;
import com.phonepe.models.merchants.MerchantType;
import com.phonepe.models.merchants.VerificationStatus;
import com.phonepe.models.merchants.categorytree.CategoryTreeNode;
import com.phonepe.models.merchants.categorytree.CategoryTreeNodeDetailResponse;
import com.phonepe.models.merchants.categorytree.CategoryType;
import com.phonepe.models.merchants.merchantKyc.KycState;
import com.phonepe.models.merchants.tasks.EntityType;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.paradox.models.core.PaginatedResponse;
import com.phonepe.paradox.models.troubleshoot.TicketStatus;
import com.phonepe.paradox.models.troubleshoot.TroubleShootDetails;
import com.phonepe.paradox.models.workflow.duecollection.MerchantDeviceDetails;
import com.phonepe.paradox.models.workflow.registration.responses.TaskStatus;
import com.phonepe.platform.brickbat.models.feedback.StoredFeedback;
import com.phonepe.platform.clockwork.model.ClockworkResponse;
import com.phonepe.platform.clockwork.model.SchedulingResponse;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.AVAILABLE;
import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.COMPLETED;
import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.CREATED;
import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.INITIATED;
import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.SELF_ASSIGNED;
import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.STARTED;
import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.VERIFICATION_FAILED;
import static com.phonepe.merchant.gladius.models.tasks.domain.LegionTaskStateMachineState.VERIFICATION_SUCCESS;
import static com.phonepe.merchant.gladius.models.tasks.enums.UserGenTaskType.USER_CREATED_DUE_COLLECTION;
import static com.phonepe.merchant.gladius.models.tasks.enums.UserGenTaskType.USER_CREATED_LENDING_DEPLOYMENT;
import static com.phonepe.merchant.gladius.models.tasks.enums.UserGenTaskType.USER_CREATED_SS_DEPLOYMENT;
import static com.phonepe.merchant.gladius.models.utils.LegionModelsConstants.SMARTSPEAKER_TROUBLESHOOT_VERIFIER;
import static com.phonepe.merchant.legion.core.exceptions.CoreErrorCode.FEEDBACK_NOT_FOUND;
import static com.phonepe.merchant.legion.core.utils.EsUtil.TASK_INDEX;
import static com.phonepe.merchant.legion.tasks.DiscoveryTestUtils.getEntity;
import static com.phonepe.merchant.legion.tasks.TaskTestUtils.DAY_IN_MS;
import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getDiscoveryTaskInstance;
import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getNDaysAgo;
import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getValidatorMap;
import static com.phonepe.merchant.legion.tasks.TaskTestUtils.getVerifierMap;
import static com.phonepe.merchant.legion.tasks.utils.ValidationUtils.validateTaskMetaAttribute;
import static com.phonepe.models.merchants.tasks.EntityType.STORE;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class TaskManagementServiceTest extends LegionTaskBaseTest {

    private static TaskManagementService taskManagementService = mock(TaskManagementService.class);
    private static TaskManagementService mockedTaskManagementService = mock(TaskManagementService.class);

    private static TaskManagementService taskManagementServiceMock;
    private static TaskInstanceManagementService taskInstanceManagementService;
    private static TaskInstanceManagementService taskInstanceManagementServiceMock;
    private static EntityStore entityStore;
    private static TaskESRepository taskESRepository;
    private static TaskDefinitionService taskDefinitionService;
    private static TaskVerificationService taskVerificationService;
    private static ActionVerificationProcessorImpl actionVerificationProcessor;
    private static TaskActionService taskActionService;
    private static List<VerificationConfig> verifiers;
    private static LegionService profileCRUDService;
    private static TransitionValidator transitionValidator;
    private static OdinService odinService;
    private static TaskRecreationService taskRecreationService;
    private static ESRepository esRepository = mock(ESRepository.class);
    private static SmartspeakerTroubleshootVerifier smartspeakerTroubleshootVerifier;
    private static ExternalEntityService externalEntityService = mock(ExternalEntityService.class);
    private static UserTaskCreationConfig userTaskCreationConfig;
    private static TaskEngine taskEngineMock;
    private static TagService tagService;
    private static LeadConfig leadConfig;
    private static TaskManagementService taskManagementServiceMockWithoutLeadConfig;
    private static TaskManagementService taskManagementServiceLendingMock;
    private static TmsService tmsService;
    private static TaskTypeFilterQuery taskTypeFilterQuery = mock(TaskTypeFilterQuery.class);
    private static CommentsService commentsService;
    private final AtlasService atlasService = mock(AtlasService.class);
    private final ChimeraRepositoryImpl chimeraRepository = mock(ChimeraRepositoryImpl.class);


    public TaskManagementServiceTest() {
        taskEngineMock = mock(TaskEngine.class);
        tmsService = mock(TmsService.class);
        tagService = mock(TagService.class);
        miscellaneous = Miscellaneous.builder().maxPossibleRescheduleOffsetInDays(90)
                .rescheduleOffsetInDays(10)
                .build();
        foxtrotEventIngestionService = mock(FoxtrotEventIngestionService.class);
        commentsService = mock(CommentsService.class);
        leadConfig = LeadConfig.builder()
                .leadUpdation(List.of(ActionToRemarkConfig.builder()
                                .taskType(USER_CREATED_SS_DEPLOYMENT)
                                .actionId("DEPLOY_PHONEPE_SMARTSPEAKER")
                                .displayText("SS Deployment")
                                .config(List.of(
                                        IntentWithRemarks.builder()
                                                .intent("HOT")
                                                .allowMarkTaskComplete(true)
                                                .build(),
                                        IntentWithRemarks.builder()
                                                .allowMarkTaskComplete(true)
                                                .intent("NOT_INTERESTED")
                                                .build())
                                )
                                .build(),
                        ActionToRemarkConfig.builder()
                                .taskType(USER_CREATED_LENDING_DEPLOYMENT)
                                .actionId("PHONEPE_MERCHANT_LENDING")
                                .displayText("PP MXN LENDING")
                                .config(List.of(
                                        IntentWithRemarks.builder()
                                                .intent("HOT")
                                                .allowMarkTaskComplete(true)
                                                .build(),
                                        IntentWithRemarks.builder()
                                                .allowMarkTaskComplete(true)
                                                .intent("NOT_INTERESTED")
                                                .build())
                                )
                                .build()))
                .build();
        LeadManagementConfiguration.create(List.of(ActionToRemarkConfig.builder()
                        .taskType(USER_CREATED_SS_DEPLOYMENT)
                        .actionId("DEPLOY_PHONEPE_SMARTSPEAKER")
                        .displayText("SS Deployment")
                        .config(List.of(
                                IntentWithRemarks.builder()
                                        .intent("HOT")
                                        .allowMarkTaskComplete(true)
                                        .build(),
                                IntentWithRemarks.builder()
                                        .allowMarkTaskComplete(true)
                                        .intent("NOT_INTERESTED")
                                        .build())
                        )
                        .build(),
                ActionToRemarkConfig.builder()
                        .taskType(USER_CREATED_LENDING_DEPLOYMENT)
                        .actionId("PHONEPE_MERCHANT_LENDING")
                        .displayText("PP MXN LENDING")
                        .config(List.of(
                                IntentWithRemarks.builder()
                                        .intent("HOT")
                                        .allowMarkTaskComplete(true)
                                        .build(),
                                IntentWithRemarks.builder()
                                        .allowMarkTaskComplete(true)
                                        .intent("NOT_INTERESTED")
                                        .build())
                        )
                        .build()), List.of(ActionToRemarkConfig.builder()
                        .taskType(USER_CREATED_SS_DEPLOYMENT)
                        .actionId("DEPLOY_PHONEPE_SMARTSPEAKER")
                        .displayText("SS Deployment")
                        .config(List.of(
                                IntentWithRemarks.builder()
                                        .intent("HOT")
                                        .allowMarkTaskComplete(true)
                                        .build(),
                                IntentWithRemarks.builder()
                                        .allowMarkTaskComplete(true)
                                        .intent("NOT_INTERESTED")
                                        .build())
                        )
                        .build(),
                ActionToRemarkConfig.builder()
                        .taskType(USER_CREATED_LENDING_DEPLOYMENT)
                        .actionId("PHONEPE_MERCHANT_LENDING")
                        .displayText("PP MXN LENDING")
                        .config(List.of(
                                IntentWithRemarks.builder()
                                        .intent("HOT")
                                        .allowMarkTaskComplete(true)
                                        .build(),
                                IntentWithRemarks.builder()
                                        .allowMarkTaskComplete(true)
                                        .intent("NOT_INTERESTED")
                                        .build())
                        )
                        .build()));
        userTaskCreationConfig = UserTaskCreationConfig.builder()
                .taskCreationParametersList(List.of(TaskCreationConfigParameters.builder().taskType(USER_CREATED_SS_DEPLOYMENT).campaignId("campaign_id").definitionId("definition_id").build()))
                .build();
        taskESRepository = mock(TaskESRepository.class);
        taskDefinitionService = mock(TaskDefinitionService.class);

         Map<String, ActionVerifier> verifierMap = getVerifierMap(merchantOnboardingService, foxtrotService, paradoxService, merchantService, fortunaService, externalEntityService, foxtrotEventIngestionService, hermodService, tmsService, legionService);
        Map<String, ActionValidator> validatorMap = getValidatorMap(foxtrotService, eventIngestionService, brickbatService, esRepository, merchantOnboardingService, externalEntityService, legionService, taskESRepository, taskDefinitionService, leadConfig, geminiService, validationService);
        taskActionService = mock(TaskActionServiceImpl.class);
        actionVerificationProcessor = new ActionVerificationProcessorImpl(verifierMap, taskTransitionRepository, taskActionService, foxtrotEventIngestionService);
        startActionValidationProcessor = new StartActionValidationProcessor(validatorMap, taskActionService, foxtrotEventIngestionService);
        completeActionValidationProcessor = new CompletionActionValidationProcessor(validatorMap, taskActionService, foxtrotEventIngestionService);
        assignActionValidationProcessor = new AssignmentActionValidationProcessor(validatorMap, taskActionService, foxtrotEventIngestionService);

        taskInstanceManagementServiceMock = mock(TaskInstanceManagementService.class);
    TaskEngine taskEngineMocked = new TaskEngineImpl(legionTransitionManager, eventExecutor);
    transitionValidator = mock(TransitionValidator.class);
    odinService = mock(OdinService.class);
    taskManagementServiceMock = new TaskManagementServiceImpl(taskEngineMock, taskInstanceRepository, transitionValidator, validationService, taskInstanceManagementServiceMock, userTaskCreationConfig, miscellaneous, foxtrotEventIngestionService);
    taskManagementServiceMockWithoutLeadConfig = new TaskManagementServiceImpl(taskEngineMock, taskInstanceRepository, transitionValidator, validationService, taskInstanceManagementServiceMock, userTaskCreationConfig, miscellaneous, foxtrotEventIngestionService);
    taskManagementServiceLendingMock = new TaskManagementServiceImpl(taskEngineMock, taskInstanceRepository, transitionValidator, validationService, taskInstanceManagementServiceMock, UserTaskCreationConfig.builder()
            .taskCreationParametersList(List.of(TaskCreationConfigParameters.builder().taskType(USER_CREATED_SS_DEPLOYMENT).campaignId("campaign_id").definitionId("definition_id").build()))
            .build(), miscellaneous, foxtrotEventIngestionService);
    taskManagementService = new TaskManagementServiceImpl(taskEngineMocked, taskInstanceRepository, transitionValidator, validationService, taskInstanceManagementService, userTaskCreationConfig, miscellaneous, foxtrotEventIngestionService);
    entityStore = mock(EntityStore.class);
    taskVerificationService = new TaskVerificationServiceImpl(clockWorkService, actionVerificationProcessor,taskActionService, eventExecutor);
    when(commentsService.createComment(any(), anyString())).thenReturn(StoredCommentsOnTask.builder().commentId("commentId").build());
    taskInstanceManagementService =
            new TaskInstanceManagementServiceImpl(entityStore, taskESRepository,
            taskTransitionRepository, taskInstanceRepository, taskManagementService, eventIngestionService, taskAttributeService, new Miscellaneous(25, 10, 90, 10, 50), eventExecutor,
                    taskTypeFilterQuery, taskDiscoveryService, legionService, odinService, commentsService, validationService, atlasService, chimeraRepository);
    verifiers = new ArrayList<>();
    verifiers.add(new NoActionVerifierConfig());

        profileCRUDService = mock(LegionService.class);
        transitionValidator = new TransitionValidator(profileCRUDService, esRepository, mock(FoxtrotEventIngestionService.class),
                startActionValidationProcessor, completeActionValidationProcessor, assignActionValidationProcessor, actionVerificationProcessor, validations);
        taskRecreationService = mock(TaskRecreationService.class);
        when(profileCRUDService.getAgentProfile(any())).thenReturn(AgentProfile.builder()
                .sectors(Collections.singletonList("string"))
                .active(true)
                .agentId("agentId")
                .agentType(AgentType.AGENT)
                .build());
        smartspeakerTroubleshootVerifier = (SmartspeakerTroubleshootVerifier) verifierMap.get(SMARTSPEAKER_TROUBLESHOOT_VERIFIER);
    }

    private TaskCreateRequest testCreateLeadTaskSetup(EntityType entityType) {
        String taskDefId = "definition_id";
        String campaignId = "campaign_id";
        String actionId = "action_id";

        StoredTaskDefinition storedTaskDefn = StoredTaskDefinition.builder()
                .taskDefinitionId(taskDefId)
                .actionId(actionId)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .name("TEST TASK DEFINITION")
                .createdBy("SurajTestUser")
                .priority(Priority.P1)
                .build();
        taskDefinitionRepository.save(storedTaskDefn);

        StoredTaskAction storedTaskAction = StoredTaskAction.builder()
                .actionId(actionId)
                .entityType(entityType)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .description("just describing")
                .createdBy("suraj")
                .build();
        taskActionRepository.save(storedTaskAction);

        StoredCampaign storedCampaign = StoredCampaign.builder()
                .campaignId(campaignId)
                .createdBy("suraj")
                .expiryPeriod(ExpiryPeriod.TIMESTAMP)
                .expiryValue(System.currentTimeMillis() + DAY_IN_MS)
                .startDate(new Date())
                .build();
        campaignRepository.save(storedCampaign);

        when(taskEngine.create(any())).thenCallRealMethod();

        Entity entity = getEntity(entityType);
        when(entityStore.getById(any())).thenReturn(Optional.of(entity));

        when(taskActionService.getFromDB(any())).thenReturn(TaskActionInstance.builder()
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId(actionId)
                .verificationStrategy(SysSyncVerificationStrategy.builder()
                        .verifiers(verifiers)
                        .build())
                .entityType(entityType)
                .build());

        createTaskLegionTransition = new CreateTaskLegionTransition(taskInstanceManagementService
                , taskDefinitionRepository, taskActionService, campaignService, transitionValidator, mock(TagService.class));
        when(legionTransitionManager.getProcessor(any())).thenReturn(
                Optional.of(createTaskLegionTransition)
        );

        List<TaskMetaInformation> taskMetaInformationList = new ArrayList<>();
        taskMetaInformationList.add(new TaskMetaInformation(TaskMetaType.DEVICE_ID, "RANDOM_DEVICE_ID", true));
        taskMetaInformationList.add(new TaskMetaInformation(TaskMetaType.WORKFLOW_ID, "WORKFLOW_ID_123", true));
        return TaskCreateRequest.builder()
                .markAvailable(true)
                .taskInstance(CreateTaskInstanceRequest.builder()
                        .createdBy("jakshat")
                        .taskDefinitionId(taskDefId)
                        .entityId(entity.getEntityId())
                        .campaignId(campaignId)
                        .taskInstanceMeta(TaskInstanceMeta.builder()
                                .taskMetaList(taskMetaInformationList)
                                .build())
                        .build())
                .build();
    }

    private TaskCreateRequest testCreateSetup(EntityType entityType) {
        String taskDefId = IdGenerator.generate("T").getId();
        String campaignId = IdGenerator.generate("C").getId();
        String actionId = IdGenerator.generate("A").getId();

        StoredTaskDefinition storedTaskDefn = StoredTaskDefinition.builder()
                .taskDefinitionId(taskDefId)
                .actionId(actionId)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .name("TEST TASK DEFINITION")
                .createdBy("SurajTestUser")
                .priority(Priority.P1)
                .build();
        taskDefinitionRepository.save(storedTaskDefn);

        StoredTaskAction storedTaskAction = StoredTaskAction.builder()
                .actionId(actionId)
                .entityType(entityType)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .description("just describing")
                .createdBy("suraj")
                .build();
        taskActionRepository.save(storedTaskAction);

        StoredCampaign storedCampaign = StoredCampaign.builder()
                .campaignId(campaignId)
                .createdBy("suraj")
                .expiryPeriod(ExpiryPeriod.TIMESTAMP)
                .expiryValue(System.currentTimeMillis() + DAY_IN_MS)
                .startDate(new Date())
                .build();
        campaignRepository.save(storedCampaign);

        when(taskEngine.create(any())).thenCallRealMethod();

        Entity entity = getEntity(entityType);
        when(entityStore.getById(any())).thenReturn(Optional.of(entity));

        when(taskActionService.getFromDB(any())).thenReturn(TaskActionInstance.builder()
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId(actionId)
                .verificationStrategy(SysSyncVerificationStrategy.builder()
                        .verifiers(verifiers)
                        .build())
                .entityType(entityType)
                .build());

    createTaskLegionTransition = new CreateTaskLegionTransition(taskInstanceManagementService
            , taskDefinitionRepository, taskActionService, campaignService, transitionValidator, mock(TagService.class));
    when(legionTransitionManager.getProcessor(any())).thenReturn(
            Optional.of(createTaskLegionTransition)
    );

        List<TaskMetaInformation> taskMetaInformationList = new ArrayList<>();
        taskMetaInformationList.add(new TaskMetaInformation(TaskMetaType.DEVICE_ID, "RANDOM_DEVICE_ID", true));
        taskMetaInformationList.add(new TaskMetaInformation(TaskMetaType.WORKFLOW_ID, "WORKFLOW_ID_123", true));
        return TaskCreateRequest.builder()
                .taskInstance(CreateTaskInstanceRequest.builder()
                        .createdBy("jakshat")
                        .taskDefinitionId(taskDefId)
                        .entityId(entity.getEntityId())
                        .campaignId(campaignId)
                        .taskInstanceMeta(TaskInstanceMeta.builder()
                                .taskMetaList(taskMetaInformationList)
                                .build())
                        .build())
                .build();
    }

    @Test(expected = Test.None.class)
    public void testCreateMerchantTask() {
        taskManagementService.command(testCreateSetup(EntityType.MERCHANT), "jakshat");
    }

    @Test(expected = Test.None.class)
    public void testCreateStoreTask() {
        when(odinService.getMerchantTransactionData(any())).thenReturn(CompleteTransactionMetaDataResponse.builder()
                        .competitionTransactionData(Collections.emptyList())
                                .phonepeTransactionData(Collections.emptyList())
                .build());
        taskManagementService.command(testCreateSetup(STORE), "jakshat");
    }

    @Test
    public void testCreateStoreTaskWithMerchantAttributes() {
        MerchantTransactionAttributeData data = MerchantTransactionAttributeData.builder()
                .merchantId("MERCHANT")
                .month(5)
                .year(2023)
                .transactionCount(100L)
                .transactionValue(100.0)
                .build();
        MerchantTransactionAttributeData data1 = MerchantTransactionAttributeData.builder()
                .merchantId("MERCHANT")
                .month(LocalDate.now().minusMonths(1).getMonthValue())
                .year(LocalDate.now().minusMonths(1).getYear())
                .transactionCount(100L)
                .transactionValue(100.0)
                .build();
        when(odinService.getMerchantTransactionData(any())).thenReturn(CompleteTransactionMetaDataResponse.builder()
                        .phonepeTransactionData(List.of(data,data1))
                        .competitionTransactionData(List.of(data,data1))
                        .build());
        taskManagementService.command(testCreateSetup(STORE), "jakshat");
    }

    @Test
    public void testCreateStoreTaskWithMerchantAttributesWithNullTaskMeta() {
        MerchantTransactionAttributeData data = MerchantTransactionAttributeData.builder()
                .merchantId("MERCHANT")
                .month(5)
                .year(2023)
                .transactionCount(100L)
                .transactionValue(100.0)
                .build();
        MerchantTransactionAttributeData data1 = MerchantTransactionAttributeData.builder()
                .merchantId("MERCHANT")
                .month(LocalDate.now().minusMonths(1).getMonthValue())
                .year(LocalDate.now().minusMonths(1).getYear())
                .transactionCount(100L)
                .transactionValue(100.0)
                .build();
        when(odinService.getMerchantTransactionData(any())).thenReturn(CompleteTransactionMetaDataResponse.builder()
                .phonepeTransactionData(List.of(data,data1))
                .competitionTransactionData(List.of(data,data1))
                .build());
        TaskCreateRequest request = testCreateSetup(STORE);
        request.getTaskInstance().setTaskInstanceMeta(null);
        taskManagementService.command(request, "jakshat");
    }

    @Test
    public void testCreateStoreTaskWithMerchantAttributesWithNullTaskMetaList() {
        MerchantTransactionAttributeData data = MerchantTransactionAttributeData.builder()
                .merchantId("MERCHANT")
                .month(5)
                .year(2023)
                .transactionCount(100L)
                .transactionValue(100.0)
                .build();
        MerchantTransactionAttributeData data1 = MerchantTransactionAttributeData.builder()
                .merchantId("MERCHANT")
                .month(LocalDate.now().minusMonths(1).getMonthValue())
                .year(LocalDate.now().minusMonths(1).getYear())
                .transactionCount(100L)
                .transactionValue(100.0)
                .build();
        when(odinService.getMerchantTransactionData(any())).thenReturn(CompleteTransactionMetaDataResponse.builder()
                .phonepeTransactionData(List.of(data,data1))
                .competitionTransactionData(List.of(data,data1))
                .build());
        TaskCreateRequest request = testCreateSetup(STORE);
        request.getTaskInstance().getTaskInstanceMeta().setTaskMetaList(null);
        taskManagementService.command(request, "jakshat");
    }

    @Test(expected = Test.None.class)
    public void testCreateTaskTask() {
        taskManagementService.command(testCreateSetup(EntityType.TASK), "jakshat");
    }

    @Test(expected = Test.None.class)
    public void testCreateSectorTask() {
        taskManagementService.command(testCreateSetup(EntityType.SECTOR), "jakshat");
    }

    @Test(expected = LegionException.class)
    public void testCreateInvalidEntity1() {

        StoredTaskDefinition storedTask = StoredTaskDefinition.builder()
                .taskDefinitionId("TASK_DEFINITION_ID_2")
                .actionId("ACTION_ID_1")
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .name("FIRST TASK DEFINITION")
                .createdBy("SurajTestUser")
                .priority(Priority.P1)
                .build();
        taskDefinitionRepository.save(storedTask);

        StoredTaskAction storedTaskAction = StoredTaskAction.builder()
                .actionId("ACTION_ID_1")
                .entityType(STORE)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .description("just describing")
                .createdBy("suraj")
                .build();

        taskActionRepository.save(storedTaskAction);

        when(taskEngine.create(any())).thenCallRealMethod();

        when(entityStore.getById(EntityStoreRequest.builder()
                .entityType(STORE)
                .referenceId("ENTITY_2")
                .build()))
                .thenThrow(LegionException.error(CoreErrorCode.NOT_FOUND));

        when(taskActionService.getFromDB(any())).thenReturn(TaskActionInstance.builder()
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId("ACTION_ID_1")
                .verificationStrategy(SysSyncVerificationStrategy.builder()
                        .verifiers(verifiers)
                        .build())
                .entityType(STORE)
                .build());

    createTaskLegionTransition = new CreateTaskLegionTransition(taskInstanceManagementService
            , taskDefinitionRepository, taskActionService, campaignService, transitionValidator, mock(TagService.class));
    when(legionTransitionManager.getProcessor(any())).thenReturn(
            Optional.of(createTaskLegionTransition)
    );

        taskManagementService.command(TaskCreateRequest.builder()
                .taskInstance(CreateTaskInstanceRequest.builder()
                        .createdBy("jakshat")
                        .taskDefinitionId("TASK_DEFINITION_ID_2")
                        .entityId("ENTITY_2")
                        .build())
                .build(), "jakshat");
    }

    @Test(expected = LegionException.class)
    public void testCreateInvalidEntity2() {

        StoredTaskDefinition storedTask = StoredTaskDefinition.builder()
                .taskDefinitionId("TASK_DEFINITION_ID_3")
                .actionId("ACTION_ID_2")
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .name("FIRST TASK DEFINITION")
                .createdBy("SurajTestUser")
                .priority(Priority.P1)
                .build();
        taskDefinitionRepository.save(storedTask);

        StoredTaskAction storedTaskAction = StoredTaskAction.builder()
                .actionId("ACTION_ID_2")
                .entityType(STORE)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .description("just describing")
                .createdBy("suraj")
                .build();

        taskActionRepository.save(storedTaskAction);

        when(taskEngine.create(any())).thenCallRealMethod();

        when(entityStore.getById(eq(EntityStoreRequest.builder()
                .entityType(STORE)
                .referenceId("ENTITY_3")
                .build()))).thenReturn(Optional.of(MerchantEntity.builder().build()));

        when(taskActionService.getFromDB(any())).thenReturn(TaskActionInstance.builder()
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId("ACTION_ID_2")
                .verificationStrategy(SysSyncVerificationStrategy.builder()
                        .verifiers(verifiers)
                        .build())
                .entityType(STORE)
                .build());

    createTaskLegionTransition = new CreateTaskLegionTransition(taskInstanceManagementService
            , taskDefinitionRepository, taskActionService, campaignService, transitionValidator, mock(TagService.class));
    when(legionTransitionManager.getProcessor(any())).thenReturn(
            Optional.of(createTaskLegionTransition)
    );

        taskManagementService.command(TaskCreateRequest.builder()
                .taskInstance(CreateTaskInstanceRequest.builder()
                        .createdBy("jakshat")
                        .taskDefinitionId("TASK_DEFINITION_ID_3")
                        .entityId("ENTITY_3")
                        .build())
                .build(), "jakshat");
    }

    @Test(expected = LegionException.class)
    public void testUserTaskCreationException() {
        UserTaskCreationRequest request = UserTaskCreationRequest.builder()
                .merchantId("mid12")
                .storeId("sid12")
                .userGenTaskType(USER_CREATED_SS_DEPLOYMENT)
                .remark("Remark")
                .rescheduleAt(1795099081000L)
                .entityType(STORE)
                .leadIntent("HOT")
                .build();

        when(validationService.validateAndGetTaskDefinition("definition_id12")).thenReturn(TaskDefinitionInstance.builder().actionId("action_id12").build());

        when(validationService.validateAndGetCampaign("campaign_id12")).thenReturn(Campaign.builder().build());

        when(transitionValidator.getDuplicateTasks(STORE, "mid12_sid12", "action_id12", Campaign.builder().build())).thenReturn(List.of());

        StoredTaskInstance createdTask = StoredTaskInstance.builder()
                .taskInstanceId("TI1234")
                .taskDefinitionId("definition_id12")
                .campaignId("campaign_id12")
                .actionId("action_id12")
                .entityType(STORE)
                .entityId("mid_sid12")
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .active(true)
                .curState(CREATED)
                .createdBy("Saransh")
                .updatedBy("Saransh")
                .dueDate(new Date(1699099081000L))
                .build();
        when(taskEngineMock.create(any())).thenReturn(createdTask);

        taskInstanceRepository.save(createdTask);

        when(taskEngineMock.markAvailable(createdTask, TaskMarkAvailableRequest.builder()
                .markedBy("saransh")
                .taskInstanceId("TI1234")
                .build())).thenReturn(createdTask);

        when(taskInstanceManagementServiceMock.rescheduleTask(any(), any())).thenReturn(anyBoolean());
        taskManagementServiceMock.command(request, "AGENT");
    }

    @Test
    public void testUserTaskCreation() {
        UserTaskCreationRequest request = UserTaskCreationRequest.builder()
                .merchantId("mid")
                .storeId("sid")
                .userGenTaskType(USER_CREATED_SS_DEPLOYMENT)
                .remark("Remark")
                .rescheduleAt(1695099081000L)
                .entityType(STORE)
                .leadIntent("HOT")
                .build();

        when(validationService.validateAndGetTaskDefinition("definition_id")).thenReturn(TaskDefinitionInstance.builder().actionId("action_id").build());

        when(validationService.validateAndGetCampaign("campaign_id")).thenReturn(Campaign.builder().build());

        when(transitionValidator.getDuplicateTasks(STORE, "mid_sid", "action_id", Campaign.builder().build())).thenReturn(List.of());

        StoredTaskInstance createdTask = StoredTaskInstance.builder()
                .taskInstanceId("TI1234")
                .taskDefinitionId("definition_id")
                .campaignId("campaign_id")
                .actionId("action_id")
                .entityType(STORE)
                .entityId("mid_sid")
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .active(true)
                .curState(CREATED)
                .createdBy("Saransh")
                .updatedBy("Saransh")
                .dueDate(new Date(1699099081000L))
                .build();
        when(taskEngineMock.create(any())).thenReturn(createdTask);

        taskInstanceRepository.save(createdTask);

        when(taskEngineMock.markAvailable(createdTask, TaskMarkAvailableRequest.builder()
                        .markedBy("saransh")
                        .taskInstanceId("TI1234")
                .build())).thenReturn(createdTask);

        when(taskInstanceManagementServiceMock.rescheduleTask(any(), any())).thenReturn(anyBoolean());
        taskManagementServiceMock.command(request, "AGENT");
    }

    @Test(expected = LegionException.class)
    public void testUserTaskCreationWithoutLeadConfig() {
        UserTaskCreationRequest request = UserTaskCreationRequest.builder()
                .merchantId("mid")
                .storeId("sid")
                .userGenTaskType(USER_CREATED_SS_DEPLOYMENT)
                .remark("Remark")
                .rescheduleAt(1695099081000L)
                .entityType(STORE)
                .leadIntent("HOT")
                .build();

        when(validationService.validateAndGetTaskDefinition("definition_id")).thenReturn(TaskDefinitionInstance.builder().actionId("action_id").build());

        when(validationService.validateAndGetCampaign("campaign_id")).thenReturn(Campaign.builder().build());

        when(transitionValidator.getDuplicateTasks(STORE, "mid_sid", "action_id", Campaign.builder().build())).thenReturn(List.of());

        StoredTaskInstance createdTask = StoredTaskInstance.builder()
                .taskInstanceId("TI1234")
                .taskDefinitionId("definition_id")
                .campaignId("campaign_id")
                .actionId("action_id")
                .entityType(STORE)
                .entityId("mid_sid")
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .active(true)
                .curState(CREATED)
                .createdBy("Saransh")
                .updatedBy("Saransh")
                .dueDate(new Date(1699099081000L))
                .build();
        when(taskEngineMock.create(any())).thenReturn(createdTask);

        taskInstanceRepository.save(createdTask);

        when(taskEngineMock.markAvailable(createdTask, TaskMarkAvailableRequest.builder()
                .markedBy("saransh")
                .taskInstanceId("TI1234")
                .build())).thenReturn(createdTask);

        when(taskInstanceManagementServiceMock.rescheduleTask(any(), any())).thenReturn(anyBoolean());
        taskManagementServiceMockWithoutLeadConfig.command(request, "AGENT");
    }

    @Test(expected = LegionException.class)
    public void testUserTaskCreationForLending() {
        UserTaskCreationRequest request = UserTaskCreationRequest.builder()
                .merchantId("mxn")
                .storeId("str")
                .userGenTaskType(USER_CREATED_LENDING_DEPLOYMENT)
                .remark("Remark")
                .rescheduleAt(1695099081000L)
                .entityType(STORE)
                .leadIntent("HOT")
                .build();
        taskManagementServiceLendingMock.command(request, "AGENT");
    }

    @Test(expected = Exception.class)
    public void testUserTaskCreationReturnNull() {
        UserTaskCreationRequest request = UserTaskCreationRequest.builder()
                .merchantId("mxn")
                .storeId("str")
                .userGenTaskType(USER_CREATED_DUE_COLLECTION)
                .remark("Remark")
                .rescheduleAt(1695099081000L)
                .entityType(STORE)
                .leadIntent("HOT")
                .build();
        taskManagementServiceLendingMock.command(request, "AGENT");
    }

    @Test(expected = Test.None.class)
    public void testAvailable() {
        StoredTaskInstance save = taskInstanceRepository.save(StoredTaskInstance.builder()
                .secondaryIndexSyncRequired(true)
                .taskInstanceId("TASK_AVAILABLE")
                .taskDefinitionId("taskDefintionID")
                .campaignId("campaignId")
                .entityType(EntityType.SECTOR)
                .curState(CREATED)
                .entityId("entityId")
                .createdBy("Saransh")
                .updatedBy("Saransh")
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId("actionId")
                .rescheduledAt(new Date())
                .build());

        taskTransitionRepository.save(
                StoredTaskTransition.builder()
                        .toState(CREATED)
                        .fromState(LegionTaskStateMachineState.INITIATED)
                        .event(LegionTaskStateMachineEvent.CREATED)
                        .taskInstanceId("TASK_AVAILABLE")
                        .actor("suraj")
                        .transitionId(1)
                        .taskInstance(StoredTaskInstance.builder()
                                .secondaryIndexSyncRequired(true)
                                .taskInstanceId("TASK_AVAILABLE")
                                .taskDefinitionId("taskDefintionID")
                                .entityType(EntityType.SECTOR)
                                .entityId("entityId")
                                .namespace(Namespace.MERCHANT_ONBOARDING)
                                .actionId("actionId")
                                .build())
                        .build()
        );

        availableTaskLegionTransition = new AvailableTaskLegionTransition(taskInstanceManagementService);
        when(legionTransitionManager.getProcessor(any())).thenReturn(
                Optional.of(availableTaskLegionTransition)
        );

        taskManagementService.command(TaskMarkAvailableRequest.builder()
                .taskInstanceId("TASK_AVAILABLE")
                .markedBy("suraj")
                .build(), "jakshat");
    }

    @Test(expected = LegionException.class)
    public void testAssign() {
        StoredTaskInstance save = taskInstanceRepository.save(StoredTaskInstance.builder()
                .secondaryIndexSyncRequired(true)
                .taskInstanceId("TASK_ASSIGNMENT")
                .taskDefinitionId("taskDefintionID")
                .entityType(EntityType.SECTOR)
                .curState(LegionTaskStateMachineState.INITIATED)
                .entityId("entityId")
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId("actionId")
                .build());

        taskTransitionRepository.save(
                StoredTaskTransition.builder()
                        .toState(CREATED)
                        .fromState(LegionTaskStateMachineState.INITIATED)
                        .event(LegionTaskStateMachineEvent.CREATED)
                        .taskInstanceId("TASK_ASSIGNMENT")
                        .actor("suraj")
                        .transitionId(1)
                        .taskInstance(StoredTaskInstance.builder()
                                .secondaryIndexSyncRequired(true)
                                .taskInstanceId("TASK_ASSIGNMENT")
                                .taskDefinitionId("taskDefintionID")
                                .entityType(EntityType.SECTOR)
                                .entityId("string")
                                .namespace(Namespace.MERCHANT_ONBOARDING)
                                .actionId("actionId")
                                .build())
                        .build()
        );

        selfAssignTaskLegionTransition = new SelfAssignTaskLegionTransition(taskInstanceManagementService, transitionValidator);
        when(legionTransitionManager.getProcessor(any())).thenReturn(
                Optional.of(selfAssignTaskLegionTransition)
        );

        when(esRepository.get("TASK_ASSIGNMENT", TASK_INDEX, DiscoveryTaskInstance.class))
                .thenReturn(getDiscoveryTaskInstance(CREATED));


        taskManagementService.command(TaskAssignRequest.builder()
                .assignedTo("suraj")
                .taskInstanceId("TASK_ASSIGNMENT")
                .build(), "jakshat");
    }

  @Test(expected = Test.None.class)
  public void testDelete() {
    StoredTaskInstance save = taskInstanceRepository.save(StoredTaskInstance.builder()
            .secondaryIndexSyncRequired(true)
            .taskInstanceId("TIPSB1")
            .taskDefinitionId("TDPSB1")
            .createdBy("Saransh")
            .updatedBy("Saransh")
            .entityType(STORE)
            .curState(LegionTaskStateMachineState.INITIATED)
            .entityId("Mer_stor")
            .namespace(Namespace.MERCHANT_ONBOARDING)
            .actionId("actionId")
            .build());

    taskTransitionRepository.save(
            StoredTaskTransition.builder()
                    .toState(AVAILABLE)
                    .fromState(INITIATED)
                    .event(LegionTaskStateMachineEvent.MARK_TASK_DELETED)
                    .taskInstanceId("TIPSB1")
                    .actor("suraj")
                    .transitionId(1)
                    .taskInstance(StoredTaskInstance.builder()
                            .secondaryIndexSyncRequired(true)
                            .taskInstanceId("TIPSB1")
                            .taskDefinitionId("TDPSB1")
                            .entityType(STORE)
                            .entityId("Mer_stor")
                            .namespace(Namespace.MERCHANT_ONBOARDING)
                            .actionId("actionId")
                            .build())
                    .build()
    );

    deletedTaskLegionTransition = new DeletedTaskLegionTransition(taskInstanceManagementService);
    when(legionTransitionManager.getProcessor(any())).thenReturn(
            Optional.of(deletedTaskLegionTransition)
    );

    when(esRepository.get("TASK_ASSIGNMENT",TASK_INDEX,DiscoveryTaskInstance.class))
            .thenReturn(getDiscoveryTaskInstance(AVAILABLE));
    taskManagementService.deleteTask(ClientTaskDeleteRequest.builder().taskInstanceId("TIPSB1").deletedBy("psb").build());

  }

  @Test(expected = Test.None.class)
  public void testManualVerify() {
    StoredTaskInstance save = taskInstanceRepository.save(StoredTaskInstance.builder()
            .secondaryIndexSyncRequired(true)
            .taskInstanceId("TIPSB")
            .taskDefinitionId("TDPSB")
            .entityType(STORE)
            .curState(SELF_ASSIGNED)
            .entityId("Mer_stor")
            .createdBy("Saransh")
            .updatedBy("Saransh")
            .namespace(Namespace.MERCHANT_ONBOARDING)
            .actionId("actionId")
            .build());

    taskTransitionRepository.save(
            StoredTaskTransition.builder()
                    .toState(COMPLETED)
                    .fromState(SELF_ASSIGNED)
                    .event(LegionTaskStateMachineEvent.MARK_TASK_DELETED)
                    .taskInstanceId("TIPSB")
                    .actor("suraj")
                    .transitionId(1)
                    .taskInstance(StoredTaskInstance.builder()
                            .secondaryIndexSyncRequired(true)
                            .taskInstanceId("TIPSB")
                            .taskDefinitionId("TDPSB")
                            .entityType(STORE)
                            .entityId("Mer_stor")
                            .namespace(Namespace.MERCHANT_ONBOARDING)
                            .actionId("actionId")
                            .build())
                    .build()
    );

    manualVerificationTaskLegionTransition = new ManualVerificationTaskLegionTransition(taskInstanceManagementService, taskRecreationService);
    when(legionTransitionManager.getProcessor(any())).thenReturn(
            Optional.of(manualVerificationTaskLegionTransition)
    );

    when(esRepository.get("TASK_ASSIGNMENT",TASK_INDEX,DiscoveryTaskInstance.class))
            .thenReturn(getDiscoveryTaskInstance(COMPLETED));
    taskManagementService.verifyTask(TaskManualVerificationRequest.builder().taskInstanceId("TIPSB").verifiedBy("psb").taskState(VERIFICATION_SUCCESS).build());
  }

  @Test(expected = Test.None.class)
  public void taskExpire() {
    StoredTaskInstance save = taskInstanceRepository.save(StoredTaskInstance.builder()
            .secondaryIndexSyncRequired(true)
            .taskInstanceId("TIPSB3")
            .taskDefinitionId("TDPSB3")
            .entityType(STORE)
            .curState(AVAILABLE)
            .createdBy("Saransh")
            .updatedBy("Saransh")
            .entityId("Mer_stor")
            .createdBy("Saransh")
            .updatedBy("Saransh")
            .namespace(Namespace.MERCHANT_ONBOARDING)
            .actionId("actionId")
            .build());

    taskTransitionRepository.save(
            StoredTaskTransition.builder()
                    .toState(SELF_ASSIGNED)
                    .fromState(AVAILABLE)
                    .event(LegionTaskStateMachineEvent.MARK_TASK_EXPIRED)
                    .taskInstanceId("TIPSB3")
                    .actor("suraj")
                    .transitionId(1)
                    .taskInstance(StoredTaskInstance.builder()
                            .secondaryIndexSyncRequired(true)
                            .taskInstanceId("TIPSB3")
                            .taskDefinitionId("TDPSB3")
                            .entityType(STORE)
                            .entityId("Mer_stor")
                            .namespace(Namespace.MERCHANT_ONBOARDING)
                            .actionId("actionId")
                            .build())
                    .build()
    );

    expiredTaskLegionTransition = new ExpiredTaskLegionTransition(taskInstanceManagementService, transitionValidator);
    when(legionTransitionManager.getProcessor(any())).thenReturn(
            Optional.of(expiredTaskLegionTransition)
    );

    when(esRepository.get("TIPSB3",TASK_INDEX,DiscoveryTaskInstance.class))
            .thenReturn(getDiscoveryTaskInstance(SELF_ASSIGNED));
    taskManagementService.expireTask(TaskExpireRequest.builder().taskInstanceId("TIPSB3").markedBy("psb").build());
  }


  @Test(expected = LegionException.class)
  public void testAssignException() {
    taskManagementService.command(TaskAssignRequest.builder()
            .assignedTo("")
            .taskInstanceId("TASK_ASSIGNMENT")
            .build(), "jakshat");
  }

    @Test(expected = Test.None.class)
    public void testBoundedAssign() {
        StoredTaskInstance save = taskInstanceRepository.save(StoredTaskInstance.builder()
                .secondaryIndexSyncRequired(true)
                .taskInstanceId("TASK_BOUNDED_ASSIGNMENT")
                .campaignId("campaignId")
                .taskDefinitionId("TASK_DEFINITION")
                .entityType(EntityType.SECTOR)
                .curState(CREATED)
                .entityId("entityId")
                .createdBy("Saransh")
                .updatedBy("Saransh")
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId("ActionId")
                .build());

        taskTransitionRepository.save(
                StoredTaskTransition.builder()
                        .toState(CREATED)
                        .fromState(LegionTaskStateMachineState.INITIATED)
                        .event(LegionTaskStateMachineEvent.CREATED)
                        .taskInstanceId("TASK_BOUNDED_ASSIGNMENT")
                        .actor("suraj")
                        .transitionId(1)
                        .taskInstance(StoredTaskInstance.builder()
                                .secondaryIndexSyncRequired(true)
                                .taskInstanceId("TASK_BOUNDED_ASSIGNMENT")
                                .taskDefinitionId("taskDefintionID")
                                .entityType(EntityType.SECTOR)
                                .entityId("entityId")
                                .namespace(Namespace.MERCHANT_ONBOARDING)
                                .actionId("actionId")
                                .build())
                        .build()
        );

        BoundedAssignTaskLegionTransition boundedAssignTaskLegionTransition =
                new BoundedAssignTaskLegionTransition(taskInstanceManagementService, transitionValidator);

        when(legionTransitionManager.getProcessor(any())).thenReturn(
                Optional.of(boundedAssignTaskLegionTransition)
        );

        taskManagementService.command(TaskAssignRequest.builder()
                .assignedTo("hey yo")
                .taskInstanceId("TASK_BOUNDED_ASSIGNMENT")
                .build(), "jakshat");
    }

    @Test
    public void testCompleteAssign() {

        VerificationStrategy verificationStrategy = SysSyncVerificationStrategy.builder()
                .verifiers(verifiers)
                .build();

        TaskActionInstance taskActionInstance = TaskActionInstance.builder()
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId(IdGenerator.generate("ACTION_ID").getId())
                .verificationStrategy(verificationStrategy)
                .build();

        when(taskActionService.getFromCache(any())).thenReturn(taskActionInstance);

        StoredTaskInstance save = taskInstanceRepository.save(StoredTaskInstance.builder()
                .secondaryIndexSyncRequired(true)
                .taskInstanceId("TASK_COMPLETED_ASSIGNMENT")
                .taskDefinitionId("TASK_DEFINITION")
                .entityType(EntityType.SECTOR)
                .curState(CREATED)
                .entityId("entityId")
                .createdBy("Saransh")
                .updatedBy("Saransh")
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId("ACTION_ID")
                .build());

        when(esRepository.get(eq(save.getTaskInstanceId()), any(), eq(DiscoveryTaskInstance.class)))
                .thenReturn(DiscoveryTaskInstance.builder()
                        .location(EsLocationRequest.builder()
                                .lon(0.0)
                                .lat(0.0)
                                .build())
                        .dueDate(Long.valueOf("2546845713000"))
                        .build());

        taskTransitionRepository.save(
                StoredTaskTransition.builder()
                        .toState(CREATED)
                        .fromState(LegionTaskStateMachineState.INITIATED)
                        .event(LegionTaskStateMachineEvent.CREATED)
                        .taskInstanceId("TASK_COMPLETED_ASSIGNMENT")
                        .actor("suraj")
                        .transitionId(1)
                        .taskInstance(StoredTaskInstance.builder()
                                .secondaryIndexSyncRequired(true)
                                .taskInstanceId("TASK_COMPLETED_ASSIGNMENT")
                                .taskDefinitionId("taskDefintionID")
                                .entityType(EntityType.SECTOR)
                                .entityId("entityId")
                                .namespace(Namespace.MERCHANT_ONBOARDING)
                                .actionId("actionId")
                                .build())
                        .build()
        );

        BoundedAssignTaskLegionTransition boundedAssignTaskLegionTransition =
                new BoundedAssignTaskLegionTransition(taskInstanceManagementService, transitionValidator);

        when(legionTransitionManager.getProcessor(any())).thenReturn(
                Optional.of(boundedAssignTaskLegionTransition)
        );

        taskManagementService.command(TaskAssignRequest.builder()
                .assignedTo("suraj")
                .taskInstanceId("TASK_COMPLETED_ASSIGNMENT")
                .build(), "jakshat");

        VerificationInitTaskLegionTransition verificationInitTaskLegionTransition =
                new VerificationInitTaskLegionTransition(taskVerificationService,
                        taskInstanceManagementService, transitionValidator);

        when(legionTransitionManager.getProcessor(any())).thenReturn(
                Optional.of(verificationInitTaskLegionTransition)
        );

        Optional<StoredTaskInstance> storedTaskInstance =
                taskInstanceRepository.get("TASK_COMPLETED_ASSIGNMENT");

        TaskCompleteRequest taskCompleteRequest = TaskCompleteRequest.builder()
                .taskInstanceId("TASK_COMPLETED_ASSIGNMENT")
                .completedBy("suraj")
                .build();
        taskCompleteRequest.setStoredTaskInstance(storedTaskInstance.get());
        taskManagementService.command(taskCompleteRequest, "jakshat");


        storedTaskInstance = taskInstanceRepository.get("TASK_COMPLETED_ASSIGNMENT");

        if (storedTaskInstance.isPresent()) {
            Assert.assertTrue(storedTaskInstance.get().getCurState().equals(COMPLETED));
        }
    }

  private StoredTaskInstance executeVerificationTestSetup(List<VerificationConfig> verifiers, boolean retry, ValidationStrategy validationStrategy, EsLocationRequest agentLocation, EntityType entityType) {

        Entity entity = getEntity(entityType);
        when(entityStore.getById(any()))
                .thenReturn(Optional.of(entity));

        VerificationStrategy verificationStrategy = SysSyncVerificationStrategy.builder()
                .verifiers(verifiers)
                .build();

        if (retry) {
            verificationStrategy.setRetryable(true);
            verificationStrategy.setMaxRetries(1);
            verificationStrategy.setNextScheduleInterval(10);
        }

        TaskActionInstance taskActionInstance = TaskActionInstance.builder()
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId("DEPLOY_PHONEPE_SMARTSPEAKER")
                .verificationStrategy(verificationStrategy)
                .completionValidationStrategy(validationStrategy)
                .entityType(entityType)
                .build();

        when(taskActionService.getFromCache(any())).thenReturn(taskActionInstance);

        StoredTaskDefinition storedTaskDefinition = StoredTaskDefinition.builder()
                .taskDefinitionId(IdGenerator.generate("TD").getId())
                .actionId(taskActionInstance.getActionId())
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .name("TEST TASK DEFINITION")
                .createdBy("SurajTestUser")
                .priority(Priority.P1)
                .build();

        taskDefinitionRepository.save(storedTaskDefinition);

        List<TaskMetaInformation> taskMetaInformationList = new ArrayList<>();
      taskMetaInformationList.add(TaskMetaInformation.builder().type(TaskMetaType.LEAD_INTENT).displayInformation(true).value("NOT_INTERESTED").build());
      taskMetaInformationList.add(TaskMetaInformation.builder().type(TaskMetaType.WORKFLOW_ID).value("workflowId").build());
        StoredTaskInstance save = taskInstanceRepository.save(StoredTaskInstance.builder()
                .secondaryIndexSyncRequired(true)
                .taskInstanceId(IdGenerator.generate("T").getId())
                .taskDefinitionId(storedTaskDefinition.getTaskDefinitionId())
                .entityType(entityType)
                .curState(CREATED)
                .dueDate(new Date())
                .entityId("entity_Id")
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId(taskActionInstance.getActionId())
                .active(true)
                .createdBy("Saransh")
                .updatedBy("Saransh")
                .instanceMeta(SerDe.writeValueAsBytes(TaskInstanceMeta.builder().taskMetaList(taskMetaInformationList).build()))
                .build());
        when(esRepository.get(eq(save.getTaskInstanceId()), any(), eq(DiscoveryTaskInstance.class)))
                .thenReturn(DiscoveryTaskInstance.builder()
                        .taskInstanceId(save.getTaskInstanceId())
                        .location(EsLocationRequest.builder()
                                .lon(0.0)
                                .lat(0.0)
                                .build())
                        .taskInstanceMeta(TaskInstanceMeta.builder()
                                .taskMetaList(taskMetaInformationList)
                                .build())
                        .dueDate(Long.valueOf("2546845713000"))
                        .actionId("DEPLOY_PHONEPE_SMARTSPEAKER")
                        .build());

        taskTransitionRepository.save(
                StoredTaskTransition.builder()
                        .toState(CREATED)
                        .fromState(LegionTaskStateMachineState.INITIATED)
                        .event(LegionTaskStateMachineEvent.CREATED)
                        .taskInstanceId(save.getTaskInstanceId())
                        .actor("suraj")
                        .transitionId(1)
                        .taskInstance(StoredTaskInstance.builder()
                                .secondaryIndexSyncRequired(true)
                                .taskInstanceId(save.getTaskInstanceId())
                                .taskDefinitionId(save.getTaskDefinitionId())
                                .entityType(save.getEntityType())
                                .entityId(save.getEntityId())
                                .namespace(save.getNamespace())
                                .instanceMeta(SerDe.writeValueAsBytes(TaskInstanceMeta.builder().taskMetaList(taskMetaInformationList).build()))
                                .actionId(save.getActionId())
                                .build())
                        .build()
        );

        BoundedAssignTaskLegionTransition boundedAssignTaskLegionTransition =
                new BoundedAssignTaskLegionTransition(taskInstanceManagementService, transitionValidator);

        when(legionTransitionManager.getProcessor(any())).thenReturn(
                Optional.of(boundedAssignTaskLegionTransition)
        );

        taskManagementService.command(TaskAssignRequest.builder()
                .assignedTo("suraj")
                .taskInstanceId(save.getTaskInstanceId())
                .build(), "suraj");

        VerificationInitTaskLegionTransition verificationInitTaskLegionTransition =
                new VerificationInitTaskLegionTransition(taskVerificationService,
                        taskInstanceManagementService, transitionValidator);

        when(legionTransitionManager.getProcessor(any())).thenReturn(
                Optional.of(verificationInitTaskLegionTransition)
        );

        TaskCompleteRequest taskCompleteRequest = TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("suraj")
                .actorCurrentLocation(agentLocation)
                .build();
        taskCompleteRequest.setStoredTaskInstance(save);
        taskManagementService.command(taskCompleteRequest, "suraj");

        ExecuteVerificationLegionTransition executeVerificationLegionTransition =
                new ExecuteVerificationLegionTransition(taskVerificationService, taskESRepository, taskRecreationService);

        when(legionTransitionManager.getProcessor(any())).thenReturn(
                Optional.of(executeVerificationLegionTransition)
        );

        return save;
    }

    private StoredTaskInstance executeVerificationTestSetupForLeadTask(List<VerificationConfig> verifiers, boolean retry, ValidationStrategy validationStrategy, EsLocationRequest agentLocation, EntityType entityType, String intent, boolean includeIntent) {

        Entity entity = getEntity(entityType);
        when(entityStore.getById(any()))
                .thenReturn(Optional.of(entity));

        VerificationStrategy verificationStrategy = SysSyncVerificationStrategy.builder()
                .verifiers(verifiers)
                .build();

        if (retry) {
            verificationStrategy.setRetryable(true);
            verificationStrategy.setMaxRetries(1);
            verificationStrategy.setNextScheduleInterval(10);
        }

        TaskActionInstance taskActionInstance = TaskActionInstance.builder()
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId(IdGenerator.generate("A").getId())
                .verificationStrategy(verificationStrategy)
                .completionValidationStrategy(validationStrategy)
                .entityType(entityType)
                .build();

        when(taskActionService.getFromCache(any())).thenReturn(taskActionInstance);

        StoredTaskDefinition storedTaskDefinition = StoredTaskDefinition.builder()
                .taskDefinitionId(IdGenerator.generate("TD").getId())
                .actionId(taskActionInstance.getActionId())
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .name("TEST TASK DEFINITION")
                .createdBy("SurajTestUser")
                .priority(Priority.P1)
                .build();

        taskDefinitionRepository.save(storedTaskDefinition);

        List<TaskMetaInformation> taskMetaInformationList = new ArrayList<>();
        taskMetaInformationList.add(TaskMetaInformation.builder().type(TaskMetaType.LEAD_INTENT).displayInformation(true).value(intent).build());
        taskMetaInformationList.add(TaskMetaInformation.builder().type(TaskMetaType.WORKFLOW_ID).value("workflowId").build());
        StoredTaskInstance save = taskInstanceRepository.save(StoredTaskInstance.builder()
                .secondaryIndexSyncRequired(true)
                .taskInstanceId(IdGenerator.generate("T").getId())
                .taskDefinitionId(storedTaskDefinition.getTaskDefinitionId())
                .entityType(entityType)
                .curState(CREATED)
                .dueDate(new Date())
                .entityId("entity_Id")
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId(taskActionInstance.getActionId())
                .active(true)
                .createdBy("Saransh")
                .updatedBy("Saransh")
                .instanceMeta(SerDe.writeValueAsBytes(TaskInstanceMeta.builder().taskMetaList(taskMetaInformationList).build()))
                .build());

        if(includeIntent) {
            when(esRepository.get(eq(save.getTaskInstanceId()), any(), eq(DiscoveryTaskInstance.class)))
                    .thenReturn(DiscoveryTaskInstance.builder()
                            .location(EsLocationRequest.builder()
                                    .lon(0.0)
                                    .lat(0.0)
                                    .build())
                            .taskInstanceMeta(TaskInstanceMeta.builder()
                                    .taskMetaList(List.of(
                                            TaskMetaInformation.builder()
                                                    .value(intent)
                                                    .displayInformation(true)
                                                    .type(TaskMetaType.LEAD_INTENT)
                                                    .build()
                                    ))
                                    .build())
                            .dueDate(Long.valueOf("2546845713000"))
                            .build());
        }
        else{
            when(esRepository.get(eq(save.getTaskInstanceId()), any(), eq(DiscoveryTaskInstance.class)))
                    .thenReturn(DiscoveryTaskInstance.builder()
                            .location(EsLocationRequest.builder()
                                    .lon(0.0)
                                    .lat(0.0)
                                    .build())
                            .dueDate(Long.valueOf("2546845713000"))
                            .build());
        }

        taskTransitionRepository.save(
                StoredTaskTransition.builder()
                        .toState(CREATED)
                        .fromState(LegionTaskStateMachineState.INITIATED)
                        .event(LegionTaskStateMachineEvent.CREATED)
                        .taskInstanceId(save.getTaskInstanceId())
                        .actor("suraj")
                        .transitionId(1)
                        .taskInstance(StoredTaskInstance.builder()
                                .secondaryIndexSyncRequired(true)
                                .taskInstanceId(save.getTaskInstanceId())
                                .taskDefinitionId(save.getTaskDefinitionId())
                                .entityType(save.getEntityType())
                                .entityId(save.getEntityId())
                                .namespace(save.getNamespace())
                                .actionId(save.getActionId())
                                .build())
                        .build()
        );

        BoundedAssignTaskLegionTransition boundedAssignTaskLegionTransition =
                new BoundedAssignTaskLegionTransition(taskInstanceManagementService, transitionValidator);

        when(legionTransitionManager.getProcessor(any())).thenReturn(
                Optional.of(boundedAssignTaskLegionTransition)
        );

        taskManagementService.command(TaskAssignRequest.builder()
                .assignedTo("suraj")
                .taskInstanceId(save.getTaskInstanceId())
                .build(), "suraj");

        VerificationInitTaskLegionTransition verificationInitTaskLegionTransition =
                new VerificationInitTaskLegionTransition(taskVerificationService,
                        taskInstanceManagementService, transitionValidator);

        when(legionTransitionManager.getProcessor(any())).thenReturn(
                Optional.of(verificationInitTaskLegionTransition)
        );

        TaskCompleteRequest taskCompleteRequest = TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("suraj")
                .actorCurrentLocation(agentLocation)
                .build();
        taskCompleteRequest.setStoredTaskInstance(save);
        taskManagementService.command(taskCompleteRequest, "suraj");

        ExecuteVerificationLegionTransition executeVerificationLegionTransition =
                new ExecuteVerificationLegionTransition(taskVerificationService, taskESRepository, taskRecreationService);

        when(legionTransitionManager.getProcessor(any())).thenReturn(
                Optional.of(executeVerificationLegionTransition)
        );

        return save;
    }

    @Test
    public void testExecuteStoreCheckinVerifyFailure() {

        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new StoreCheckInVerifierConfig(12_000));

        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, false, null, null, STORE);

        when(merchantOnboardingService.getStoreDoc(any()))
                .thenReturn(ElasticSearchRequest.builder()
                        .lastServiceAt(getNDaysAgo(3))
                        .servicerId("suraj")
                        .build());

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("suraj")
                .build());

        Assert.assertEquals(VERIFICATION_FAILED, response.getCurState());
    }

    @Test(expected = Test.None.class)
    public void testExecuteOqcVerifyFailure() {

        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new OqcValidationVerifierConfig());

        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, false, null, null, STORE);

        when(merchantOnboardingService.getStoreDoc(any()))
                .thenReturn(ElasticSearchRequest.builder()
                        .businessProofStatus(DisplayState.PENDING)
                        .servicerId("suraj")
                        .build());

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("suraj")
                .build());

        Assert.assertEquals(VERIFICATION_FAILED, response.getCurState());
    }

    @Test(expected = Test.None.class)
    public void testExecuteTaskQcVerifyFailure() {

        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new TaskQcVerifierConfig());

        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, false, null, null, STORE);

        TaskCompletionNoteMeta taskCompletionNoteMeta = new TaskCompletionNoteMeta();
        taskCompletionNoteMeta.setCompletionNote(VERIFICATION_FAILED.getText());

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("suraj")
                .meta(taskCompletionNoteMeta)
                .build());

        Assert.assertEquals(VERIFICATION_FAILED, response.getCurState());
    }

    @Test(expected = Test.None.class)
    public void testExecuteStoreQcVerifyFailure() {

        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new StoreQcVerifierConfig());

        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, false, null, null, STORE);

        when(merchantOnboardingService.getStoreDoc(any()))
                .thenReturn(ElasticSearchRequest.builder()
                        .verificationStatus(VerificationStatus.PENDING)
                        .storeStatus(DisplayState.PENDING)
                        .servicerId("suraj")
                        .build());

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("suraj")
                .build());

        Assert.assertEquals(VERIFICATION_FAILED, response.getCurState());
    }

    @Test
    public void merchantKycTaskVerifyFailure() {

        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new MerchantKycVerifierConfig());

        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, false, null, null, STORE);

        when(merchantOnboardingService.getStoreDoc(any()))
                .thenReturn(ElasticSearchRequest.builder()
                        .merchant(MerchantMeta.builder()
                                .kycState(KycState.REJECTED).build())
                        .build());

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("suraj")
                .build());

        Assert.assertEquals(VERIFICATION_FAILED, response.getCurState());
    }

    @Test
    public void p2mlMigrationVerifyFailure() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new P2MLmigrationVerifierConfig());

        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, false, null, null, STORE);

        when(merchantOnboardingService.getStoreDoc(any()))
                .thenReturn(ElasticSearchRequest.builder()
                        .merchant(MerchantMeta.builder().merchantType(MerchantType.P2P_MERCHANT).build())
                        .build());

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("suraj")
                .build());

        Assert.assertEquals(VERIFICATION_FAILED, response.getCurState());
    }

    @Test
    public void p2mlMigrationVerifySuccess() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new P2MLmigrationVerifierConfig());

        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, false, null, null, STORE);

        when(merchantOnboardingService.getStoreDoc(any()))
                .thenReturn(ElasticSearchRequest.builder()
                        .merchant(MerchantMeta.builder().merchantType(MerchantType.P2M_LIMITED).build())
                        .build());

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("suraj")
                .build());

        Assert.assertEquals(VERIFICATION_SUCCESS, response.getCurState());
    }

    @Test()
    public void validateTaskCreationForsbTroubleshooting() {
        List<TaskMetaInformation> metaInformationList = new ArrayList<>();
        metaInformationList.add(TaskMetaInformation.builder().value("xyz_123").type(TaskMetaType.WORKFLOW_ID).build());
        metaInformationList.add(TaskMetaInformation.builder().value("xzz_123").type(TaskMetaType.DEVICE_ID).build());
        CreateTaskInstanceRequest createTaskInstanceRequest = CreateTaskInstanceRequest.builder()
                .taskDefinitionId("taskDefinitionId")
                .campaignId("saransh_active_campaign")
                .createdBy("me")
                .taskInstanceMeta(TaskInstanceMeta.builder().taskMetaList(metaInformationList).build())
                .build();
        validateTaskMetaAttribute(createTaskInstanceRequest, TaskMetaType.WORKFLOW_ID);
        validateTaskMetaAttribute(createTaskInstanceRequest, TaskMetaType.DEVICE_ID);
        boolean actualResponse = smartspeakerTroubleshootVerifier.validateTaskCreation();
        Assert.assertTrue(actualResponse);
    }

    @Test
    public void sbTroubleshootingVerifyFailed() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new SmartspeakerTroubleshootVerifierConfig());

        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, false, null, null, STORE);

        when(paradoxService.getTroubleshootingStatus(any()))
                .thenReturn(TroubleShootDetails.builder()
                        .deviceId("xyz")
                        .workflowId("abcc")
                        .merchantId("saransh_merchant")
                        .requestorId("id123")
                        .deviceId("Ios123")
                        .ticketId("Gladius")
                        .storeId("my_store")
                        .ticketStatus(TicketStatus.FAILED).build());

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("saransh")
                .build());

        Assert.assertEquals(VERIFICATION_FAILED, response.getCurState());
    }

    @Test
    public void eventExistenceVerifyUndeterministicRetry() {

        List<Filter> filters = new ArrayList<>();
        filters.add(new EqualsFilter());
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        List<SingleEventVerificationConfig> singleEventVerificationConfigs = new ArrayList<>();
        singleEventVerificationConfigs.add(SingleEventVerificationConfig.builder()
                .foxtrotRequest(FoxtrotRequest.builder()
                        .table("legion")
                        .filters(filters)
                        .build())
                .secondsBeforeTaskCompletion(1000l)
                .entityIdFoxtrotColumns(Arrays.asList("entityIdFieldName"))
                .build());
        verificationConfigs.add(new EventExistenceVerifierConfig(singleEventVerificationConfigs));

        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, true, null, null, STORE);

        when(foxtrotService.getFoxtrotEventCount(any()))
                .thenReturn(0l);
        when(clockWorkService.scheduleClockworkForProvider(any(), any())).thenReturn(
                ClockworkResponse.<SchedulingResponse>builder()
                        .data(SchedulingResponse.builder()
                                .jobId("jobId")
                                .build()).build());
        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("suraj")
                .build());

        Assert.assertEquals(COMPLETED, response.getCurState());
    }

    @Test
    public void eventExistenceVerifyFailure() {

        List<Filter> filters = new ArrayList<>();
        filters.add(new EqualsFilter());
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        List<SingleEventVerificationConfig> singleEventVerificationConfigs = new ArrayList<>();
        singleEventVerificationConfigs.add(SingleEventVerificationConfig.builder()
                .foxtrotRequest(FoxtrotRequest.builder()
                        .table("legion")
                        .filters(filters)
                        .build())
                .failIfEventExists(true)
                .secondsBeforeTaskCompletion(1000l)
                .entityIdFoxtrotColumns(Arrays.asList("entityIdFieldName"))
                .build());
        verificationConfigs.add(new EventExistenceVerifierConfig(singleEventVerificationConfigs));

        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, true, null, null, STORE);

        when(foxtrotService.getFoxtrotEventCount(any()))
                .thenReturn(1l);

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("suraj")
                .build());

        Assert.assertEquals(VERIFICATION_FAILED, response.getCurState());
    }

    @Test
    public void eventExistenceVerifyUndeterministic() {

        List<Filter> filters = new ArrayList<>();
        filters.add(new EqualsFilter());
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        List<SingleEventVerificationConfig> singleEventVerificationConfigs = new ArrayList<>();
        singleEventVerificationConfigs.add(SingleEventVerificationConfig.builder()
                .foxtrotRequest(FoxtrotRequest.builder()
                        .table("legion")
                        .filters(filters)
                        .build())
                .secondsBeforeTaskCompletion(1000l)
                .entityIdFoxtrotColumns(Arrays.asList("entityIdFieldName"))
                .build());
        verificationConfigs.add(new EventExistenceVerifierConfig(singleEventVerificationConfigs));

        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, false, null, null, STORE);

        when(foxtrotService.getFoxtrotEventCount(any()))
                .thenReturn(0l);

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("suraj")
                .build());

        Assert.assertEquals(COMPLETED, response.getCurState());
    }

    @Test
    public void merchantKycTaskVerifyUndeterministic() {

        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new MerchantKycVerifierConfig());

        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, false, null, null, STORE);

        when(merchantOnboardingService.getStoreDoc(any()))
                .thenReturn(ElasticSearchRequest.builder()
                        .merchant(MerchantMeta.builder()
                                .kycState(KycState.UPLOADED).build())
                        .build());

        TaskCompleteRequest taskCompleteRequest = TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("suraj")
                .build();
        taskCompleteRequest.setRetriedTimes(1);
        StoredTaskInstance response = taskManagementService.verify(taskCompleteRequest);

        Assert.assertEquals(COMPLETED, response.getCurState());
    }

    @Test
    public void merchantKycTaskVerifyWithRetry() {

        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new MerchantKycVerifierConfig());

        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, true, null, null, STORE);

        when(merchantOnboardingService.getStoreDoc(any()))
                .thenReturn(ElasticSearchRequest.builder()
                        .merchant(MerchantMeta.builder()
                                .kycState(KycState.UPLOADED).build())
                        .build());

        TaskCompleteRequest taskCompleteRequest = TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("suraj")
                .build();
        taskCompleteRequest.setRetriedTimes(1);
        when(clockWorkService.scheduleClockworkForProvider(any(), any())).thenReturn(
                ClockworkResponse.<SchedulingResponse>builder()
                        .data(SchedulingResponse.builder()
                                .jobId("jobId")
                                .build()).build());
        StoredTaskInstance response = taskManagementService.verify(taskCompleteRequest);

        Assert.assertEquals(COMPLETED, response.getCurState());
    }

    @Test
    public void testExecuteTaskQcVerifyUndeterministic() {

        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new TaskQcVerifierConfig());

        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, false, null, null, STORE);

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("suraj")
                .build());

        Assert.assertEquals(COMPLETED, response.getCurState());
    }

    @Test
    public void testExecuteBrickbatFormFillVerifyFailure() {

        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new BrickbatFormFillVerifierConfig(false));

        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, false, null, null, STORE);
        when(foxtrotService.getFoxtrotEventCount(any()))
                .thenReturn(0l);

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("suraj")
                .build());

        Assert.assertEquals(VERIFICATION_FAILED, response.getCurState());
    }

    @Test
    public void testExecuteBrickbatFormFillVerifyUndeterministic() {

        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new BrickbatFormFillVerifierConfig(false));

        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, false, null, null, STORE);
        when(foxtrotService.getFoxtrotEventCount(any()))
                .thenReturn(1l);

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("suraj")
                .build());

        Assert.assertEquals(COMPLETED, response.getCurState());
    }

    @Test(expected = LegionException.class)
    public void testExecuteBrickbatFormFillValidatorFailureCase1() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new NoActionVerifierConfig());

        ValidationStrategy validationStrategy = new ValidationStrategy();
        validationStrategy.setValidationConfig(new LeafContainer(new BrickbatFormFillValidatorConfig()));

        when(foxtrotService.getFoxtrotEventCount(any()))
                .thenReturn(0l);
        executeVerificationTestSetup(verificationConfigs, false, validationStrategy, null, STORE);
    }

    @Test(expected = LegionException.class)
    public void testExecuteBrickbatFormFillValidatorFailureCase2() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new NoActionVerifierConfig());

        ValidationStrategy validationStrategy = new ValidationStrategy();
        validationStrategy.setValidationConfig(new LeafContainer(new BrickbatFormFillValidatorConfig("ABCD")));

        when(brickbatService.getSurveyResult(any())).thenThrow(LegionException.error(FEEDBACK_NOT_FOUND));
        executeVerificationTestSetup(verificationConfigs, false, validationStrategy, null, STORE);
    }

    @Test(expected = LegionException.class)
    public void testExecuteBrickbatFormFillValidatorFailureCase3() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new NoActionVerifierConfig());

        ValidationStrategy validationStrategy = new ValidationStrategy();
        validationStrategy.setValidationConfig(new LeafContainer(new BrickbatFormFillValidatorConfig("ABCD")));

        when(brickbatService.getSurveyResult(any())).thenThrow(new IllegalAccessException());
        executeVerificationTestSetup(verificationConfigs, false, validationStrategy, null, STORE);
    }

    @Test(expected = LegionException.class)
    public void testExecuteAgentLocationValidatorFailure() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new NoActionVerifierConfig());

        ValidationStrategy validationStrategy = new ValidationStrategy();
        validationStrategy.setValidationConfig(new LeafContainer(new AgentLocationValidatorConfig()));

        executeVerificationTestSetup(verificationConfigs, false, validationStrategy,
                EsLocationRequest.builder()
                        .lon(70.0)
                        .lat(70.0)
                        .build(), STORE);
    }

    @Test
    public void flDriveVerifierUndeterministic() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new FlDriveVerifierConfig(1, 1));
        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, false, null, null, EntityType.AGENT);
        List<MerchantOnboardedByAgent> merchants = new ArrayList<>();
        merchants.add(MerchantOnboardedByAgent.builder()
                .merchantId("MID")
                .build());
        when(merchantOnboardingService.getMerchantsOnboardedByAgent(any(), anyLong(), anyLong()))
                .thenReturn(merchants);
        when(foxtrotService.getFoxtrotEventCount(any()))
                .thenReturn(0l);
        TaskCompleteRequest taskCompleteRequest = TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("suraj")
                .build();
        StoredTaskInstance response = taskManagementService.verify(taskCompleteRequest);
        Assert.assertEquals(COMPLETED, response.getCurState());
    }


    @Test
    public void flDriveVerifierFailure1() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new FlDriveVerifierConfig(1, 1));
        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, true, null, null, EntityType.AGENT);
        List<MerchantOnboardedByAgent> merchants = new ArrayList<>();
        merchants.add(MerchantOnboardedByAgent.builder()
                .merchantId("MID")
                .build());
        when(merchantOnboardingService.getMerchantsOnboardedByAgent(any(), anyLong(), anyLong()))
                .thenReturn(merchants);
        when(foxtrotService.getFoxtrotEventCount(any()))
                .thenReturn(0l);
        TaskCompleteRequest taskCompleteRequest = TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("suraj")
                .build();
        taskCompleteRequest.setRetriedTimes(2);
        StoredTaskInstance response = taskManagementService.verify(taskCompleteRequest);
        Assert.assertEquals(VERIFICATION_FAILED, response.getCurState());
    }

    @Test
    public void flDriveVerifierFailure2() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new FlDriveVerifierConfig(1, 1));
        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, true, null, null, EntityType.AGENT);
        List<MerchantOnboardedByAgent> merchants = new ArrayList<>();
        when(merchantOnboardingService.getMerchantsOnboardedByAgent(any(), anyLong(), anyLong()))
                .thenReturn(merchants);
        TaskCompleteRequest taskCompleteRequest = TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("suraj")
                .build();
        taskCompleteRequest.setRetriedTimes(2);
        StoredTaskInstance response = taskManagementService.verify(taskCompleteRequest);
        Assert.assertEquals(VERIFICATION_FAILED, response.getCurState());
    }

    @Test
    public void flDriveVerifierSuccess() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new FlDriveVerifierConfig(1, 1));
        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, false, null, null, EntityType.AGENT);
        List<MerchantOnboardedByAgent> merchants = new ArrayList<>();
        merchants.add(MerchantOnboardedByAgent.builder()
                .merchantId("MID")
                .build());
        when(merchantOnboardingService.getMerchantsOnboardedByAgent(any(), anyLong(), anyLong()))
                .thenReturn(merchants);
        when(foxtrotService.getFoxtrotEventCount(any()))
                .thenReturn(1l);
        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("suraj")
                .build());
        Assert.assertEquals(VERIFICATION_SUCCESS, response.getCurState());
    }

    @Test
    public void testExecutePoaVerifyFailure() {

        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new PoaVerifierConfig());

        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, false, null, null, STORE);

        when(merchantOnboardingService.getFilteredMerchantDetails(anyString(), any()))
                .thenReturn(MerchantDetails.builder()
                        .isPOAKycRequired(true)
                        .kycState(KycState.VERIFIED)
                        .build());

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("suraj")
                .build());

        Assert.assertEquals(VERIFICATION_FAILED, response.getCurState());
    }

    /**
     * We need to add Verifier in the constructor too
     */
    @Test
    public void testExecuteMultipleVerifiersAndValidatorsSuccess() {
        transitionValidator = mock(TransitionValidator.class);
        List<Filter> filters = new ArrayList<>();
        filters.add(new EqualsFilter());
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new OqcValidationVerifierConfig());
        verificationConfigs.add(new StoreCheckInVerifierConfig(12_000));
        verificationConfigs.add(new TaskQcVerifierConfig());
        verificationConfigs.add(new NoActionVerifierConfig());
        verificationConfigs.add(new MerchantKycVerifierConfig());
        verificationConfigs.add(new StoreQcVerifierConfig());
        verificationConfigs.add(new BrickbatFormFillVerifierConfig(true));
        verificationConfigs.add(new P2MLmigrationVerifierConfig());
        verificationConfigs.add(new SmartspeakerTroubleshootVerifierConfig());
        verificationConfigs.add(new PoaVerifierConfig());
        verificationConfigs.add(new PobVerifierConfig());
        verificationConfigs.add(new EdcDeploymentVerifierConfig());
        verificationConfigs.add(new EdcReversePickupVerifierConfig());

        List<ValidationContainer> validationContainers = new ArrayList<>();
        validationContainers.add(new LeafContainer(new BrickbatFormFillValidatorConfig("ABCD")));
        validationContainers.add(new LeafContainer(new AgentLocationValidatorConfig()));
        validationContainers.add(new LeafContainer(new ImageQcActionValidtorConfig()));
        validationContainers.add(new LeafContainer(new LeadIntentValidatorConfig()));
        AndOperationContainer andOperationContainer = new AndOperationContainer(validationContainers);
        ValidationStrategy validationStrategy = new ValidationStrategy();
        validationStrategy.setValidationConfig(andOperationContainer);

        reset(brickbatService);
        when(brickbatService.getSurveyResult(anyString())).thenReturn(GenericResponse.<StoredFeedback>builder()
                .success(true)
                .build());

        reset(legionService);
        when(legionService.isImageVerificationStatusPending(anyString())).thenReturn(GenericResponse.<VerificationMeta>builder().success(false).build());

        List<SingleEventVerificationConfig> singleEventVerificationConfigs = new ArrayList<>();
        singleEventVerificationConfigs.add(SingleEventVerificationConfig.builder()
                .entityIdFoxtrotColumns(Arrays.asList("entityIdFieldName"))
                .secondsBeforeTaskCompletion(1000l)
                .foxtrotRequest(FoxtrotRequest.builder()
                        .table("legion")
                        .filters(filters)
                        .build())
                .build());
        verificationConfigs.add(new EventExistenceVerifierConfig(singleEventVerificationConfigs));

        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, false, validationStrategy,
                EsLocationRequest.builder()
                        .lat(0.0)
                        .lon(0.0)
                        .build(), STORE);

        when(paradoxService.getTroubleshootingStatus(any()))
                .thenReturn(TroubleShootDetails.builder()
                        .deviceId("xyz")
                        .workflowId("abcc")
                        .merchantId("saransh_merchant")
                        .requestorId("id123")
                        .deviceId("Ios123")
                        .ticketId("Gladius")
                        .storeId("my_store")
                        .ticketStatus(TicketStatus.RESOLVED).build());

        when(paradoxService.getParadoxTaskStatus(anyString(), anyString(), anyString()))
                .thenReturn(TaskStatus.COMPLETED);

        when(paradoxService.getParadoxTaskStatus(anyString(), anyString(), anyString()))
                .thenReturn(TaskStatus.COMPLETED);

        when(merchantOnboardingService.getStoreDoc(any()))
                .thenReturn(ElasticSearchRequest.builder()
                        .businessProofStatus(DisplayState.APPROVED)
                        .storeStatus(DisplayState.APPROVED)
                        .verificationStatus(VerificationStatus.VERIFIED)
                        .lastServiceAt(new Date())
                        .servicerId("suraj")
                        .merchant(MerchantMeta.builder()
                                .kycState(KycState.VERIFIED).merchantType(MerchantType.P2M_LIMITED).build())
                        .build());
        when(foxtrotService.getFoxtrotEventCount(any()))
                .thenReturn(1l);
        when(merchantOnboardingService.getFilteredMerchantDetails(anyString(), any()))
                .thenReturn(MerchantDetails.builder()
                        .isPOAKycRequired(false)
                        .kycState(KycState.VERIFIED)
                        .build());

        TaskCompletionNoteMeta taskCompletionNoteMeta = new TaskCompletionNoteMeta();
        taskCompletionNoteMeta.setCompletionNote(VERIFICATION_SUCCESS.getText());

//        when(taskESRepository.get(save.getTaskInstanceId())).thenReturn(DiscoveryTaskInstance.builder()
//                        .actionId("DEPLOY_PHONEPE_SMARTSPEAKER")
//                .build());
        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("suraj")
                .meta(taskCompletionNoteMeta)
                .build());

        Assert.assertEquals(VERIFICATION_SUCCESS, response.getCurState());
    }


    @Test(expected = LegionException.class)
    public void testLeadIntentValidation() {

        List<Filter> filters = new ArrayList<>();
        filters.add(new EqualsFilter());
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new NoActionVerifierConfig());

        List<ValidationContainer> validationContainers = new ArrayList<>();
        validationContainers.add(new LeafContainer(new LeadIntentValidatorConfig()));
        AndOperationContainer andOperationContainer = new AndOperationContainer(validationContainers);
        ValidationStrategy validationStrategy = new ValidationStrategy();
        validationStrategy.setValidationConfig(andOperationContainer);

        StoredTaskInstance save = executeVerificationTestSetupForLeadTask(verificationConfigs, false, validationStrategy,
                EsLocationRequest.builder()
                        .lat(0.0)
                        .lon(0.0)
                        .build(), STORE, "Intent", true);

        TaskCompletionNoteMeta taskCompletionNoteMeta = new TaskCompletionNoteMeta();
        taskCompletionNoteMeta.setCompletionNote(VERIFICATION_SUCCESS.getText());

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("saransh")
                .meta(taskCompletionNoteMeta)
                .build());

        Assert.assertEquals(VERIFICATION_SUCCESS, response.getCurState());
    }

    @Test(expected = LegionException.class)
    public void testLeadIntentValidation2() {

        List<Filter> filters = new ArrayList<>();
        filters.add(new EqualsFilter());
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new NoActionVerifierConfig());

        List<ValidationContainer> validationContainers = new ArrayList<>();
        validationContainers.add(new LeafContainer(new LeadIntentValidatorConfig()));
        AndOperationContainer andOperationContainer = new AndOperationContainer(validationContainers);
        ValidationStrategy validationStrategy = new ValidationStrategy();
        validationStrategy.setValidationConfig(andOperationContainer);

        StoredTaskInstance save = executeVerificationTestSetupForLeadTask(verificationConfigs, false, validationStrategy,
                EsLocationRequest.builder()
                        .lat(0.0)
                        .lon(0.0)
                        .build(), STORE, "INTENT", true);

        TaskCompletionNoteMeta taskCompletionNoteMeta = new TaskCompletionNoteMeta();
        taskCompletionNoteMeta.setCompletionNote(VERIFICATION_SUCCESS.getText());

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("saransh")
                .meta(taskCompletionNoteMeta)
                .build());

        Assert.assertEquals(VERIFICATION_SUCCESS, response.getCurState());
    }

    @Test(expected = LegionException.class)
    public void testLeadIntentValidation3() {

        List<Filter> filters = new ArrayList<>();
        filters.add(new EqualsFilter());
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new NoActionVerifierConfig());

        List<ValidationContainer> validationContainers = new ArrayList<>();
        validationContainers.add(new LeafContainer(new LeadIntentValidatorConfig()));
        AndOperationContainer andOperationContainer = new AndOperationContainer(validationContainers);
        ValidationStrategy validationStrategy = new ValidationStrategy();
        validationStrategy.setValidationConfig(andOperationContainer);

        StoredTaskInstance save = executeVerificationTestSetupForLeadTask(verificationConfigs, false, validationStrategy,
                EsLocationRequest.builder()
                        .lat(0.0)
                        .lon(0.0)
                        .build(), STORE, "INTENT", false);

        TaskCompletionNoteMeta taskCompletionNoteMeta = new TaskCompletionNoteMeta();
        taskCompletionNoteMeta.setCompletionNote(VERIFICATION_SUCCESS.getText());

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("saransh")
                .meta(taskCompletionNoteMeta)
                .build());

        Assert.assertEquals(VERIFICATION_SUCCESS, response.getCurState());
    }

    @Test
    public void sbDeploymentVerifierSuccess() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new SoundboxDeploymentVerifierConfig(180000));
        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, true, null, null, STORE);
        when(paradoxService.getParadoxTaskStatus(anyString(), anyString(),anyString()))
                .thenReturn(TaskStatus.COMPLETED);
        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("shiv")
                .build());
        Assert.assertEquals(VERIFICATION_SUCCESS, response.getCurState());
    }

    @Test
    public void businessCategoryUpdateVerifierSuccess() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new BusinessCategoryUpdateVerifierConfig());
        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, true, null, null, STORE);
        when(merchantService.getCategoryNodeDetails(anyString()))
                .thenReturn(CategoryTreeNodeDetailResponse.builder()
                        .node(CategoryTreeNode.builder()
                                .value(anyString())
                                .categoryType(CategoryType.CATEGORY)
                                .build())
                        .build());
        MerchantEntity expectedResponse = MerchantEntity.builder().category("RANDOM").subCategory("SUBCATEGORYY").superCategory("SUPERCATEGORYY").build();
        MerchantProfile merchantProfile = DiscoveryTestUtils.toMerchantProfile(expectedResponse);
        when(merchantService.getMerchantDetails(merchantProfile.getMerchantId())).thenReturn(merchantProfile);

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("SARANSH_AGARWAL")
                .build());
        Assert.assertEquals(VERIFICATION_SUCCESS, response.getCurState());
    }


    @Test
    public void pobVerifierFailure() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new PobVerifierConfig());
        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, true, null, null, STORE);
        when(merchantOnboardingService.getFilteredMerchantDetails(anyString(), any()))
                .thenReturn(MerchantDetails.builder()
                        .pobkycCollectionRequired(true)
                        .migrationStatus("REJECTED")
                        .build());

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("SARANSH_AGARWAL")
                .build());
        Assert.assertEquals(VERIFICATION_FAILED, response.getCurState());
    }

    @Test
    public void pobVerifierSuccess() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new PobVerifierConfig());
        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, true, null, null, STORE);
        when(merchantOnboardingService.getFilteredMerchantDetails(anyString(), any()))
                .thenReturn(MerchantDetails.builder()
                        .pobkycCollectionRequired(false)
                        .migrationStatus("SUCCESS")
                        .build());

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("SARANSH_AGARWAL")
                .build());
        Assert.assertEquals(VERIFICATION_SUCCESS, response.getCurState());
    }

    @Test(expected = Exception.class)
    public void businessCategoryVerfierFailedAsMerchantNotFound() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new BusinessCategoryUpdateVerifierConfig());
        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, true, null, null, STORE);
        when(merchantService.getCategoryNodeDetails(anyString()))
                .thenReturn(CategoryTreeNodeDetailResponse.builder()
                        .node(CategoryTreeNode.builder()
                                .value(anyString())
                                .categoryType(CategoryType.SUBCATEGORY)
                                .build())
                        .build());
        when(merchantService.getMerchantDetails("RANDOM_MERCHANT_ID")).thenCallRealMethod();

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("saransh")
                .build());
        Assert.assertEquals(VERIFICATION_FAILED, response.getCurState());
    }

    @Test(expected = Exception.class)
    public void businessCategoryUpdateVerifierFail() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new BusinessCategoryUpdateVerifierConfig());
        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, true, null, null, STORE);
        when(merchantService.getCategoryNodeDetails(anyString()))
                .thenReturn(CategoryTreeNodeDetailResponse.builder()
                        .node(CategoryTreeNode.builder()
                                .value("Miscellaneous")
                                .categoryType(CategoryType.SUBCATEGORY)
                                .build())
                        .build());
        MerchantEntity expectedResponse = MerchantEntity.builder().category("CT1234").subCategory("CT123").superCategory("CT012").build();
        MerchantProfile merchantProfile = DiscoveryTestUtils.toMerchantProfile(expectedResponse);
        when(merchantService.getMerchantDetails(merchantProfile.getMerchantId())).thenReturn(merchantProfile);

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("SARANSH12")
                .build());
        Assert.assertEquals(VERIFICATION_FAILED, response.getCurState());
    }

    @Test(expected = LegionException.class)
    public void imageQcActionValidatorSuccess() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new NoActionVerifierConfig());

        ValidationStrategy validationStrategy = new ValidationStrategy();
        validationStrategy.setValidationConfig(new LeafContainer(new ImageQcActionValidtorConfig()));

        when(legionService.isImageVerificationStatusPending(anyString())).thenReturn(GenericResponse.<VerificationMeta>builder().success(true).build());

        executeVerificationTestSetup(verificationConfigs, false, validationStrategy, null, EntityType.AGENT);
    }


    @Test(expected = Test.None.class)
    public void testStartTask() {



        List<ValidationContainer> validationContainers = new ArrayList<>();
        validationContainers.add(new LeafContainer(new AgentLocationValidatorConfig()));
        AndOperationContainer andOperationContainer = new AndOperationContainer(validationContainers);
        ValidationStrategy validationStrategy = new ValidationStrategy();
        validationStrategy.setValidationConfig(andOperationContainer);

        TaskActionInstance taskActionInstance = TaskActionInstance.builder()
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId(IdGenerator.generate("ACTION_ID").getId())
                .startTaskValidationStrategy(validationStrategy)
                .build();

        when(taskActionService.getFromCache(any())).thenReturn(taskActionInstance);

        StoredTaskInstance save = taskInstanceRepository.save(StoredTaskInstance.builder()
                .secondaryIndexSyncRequired(true)
                .taskInstanceId("TIPSB11")
                .taskDefinitionId("TDPSB11")
                .entityType(STORE)
                .curState(SELF_ASSIGNED)
                .createdBy("Saransh")
                .updatedBy("Saransh")
                .entityId("Mer_stor")
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .actionId("ACTION_ID")
                .build());

        when(esRepository.get(eq(save.getTaskInstanceId()), any(), eq(DiscoveryTaskInstance.class)))
                .thenReturn(DiscoveryTaskInstance.builder()
                        .location(EsLocationRequest.builder()
                                .lon(0.0)
                                .lat(0.0)
                                .build())
                        .dueDate(Long.valueOf("2546845713000"))
                        .build());

        taskTransitionRepository.save(
                StoredTaskTransition.builder()
                        .toState(STARTED)
                        .fromState(SELF_ASSIGNED)
                        .event(LegionTaskStateMachineEvent.MARK_STARTED)
                        .taskInstanceId("TIPSB11")
                        .actor("psb")
                        .transitionId(1)
                        .taskInstance(StoredTaskInstance.builder()
                                .secondaryIndexSyncRequired(true)
                                .taskInstanceId("TIPSB11")
                                .taskDefinitionId("TDPSB11")
                                .entityType(STORE)
                                .entityId("Mer_stor")
                                .namespace(Namespace.MERCHANT_ONBOARDING)
                                .actionId("ACTION_ID")
                                .build())
                        .build()
        );

        startTaskLegionTransition = new StartTaskLegionTransition(transitionValidator, taskInstanceManagementService);
        when(legionTransitionManager.getProcessor(any())).thenReturn(
                Optional.of(startTaskLegionTransition)
        );

        when(esRepository.get("TASK_ASSIGNMENT", TASK_INDEX,DiscoveryTaskInstance.class))
                .thenReturn(getDiscoveryTaskInstance(COMPLETED));
        taskManagementService.command(TaskStartRequest.builder().taskInstanceId("TIPSB11").isForced(false).startedBy("psb").build(), "psb");
    }

    @Test
    public void OnboardExternalEntityAsPhonepeMxnValidatorSuccess() {
        String merchantId = "MID";
        String storeId = "SID";

        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new NoActionVerifierConfig());

        ValidationStrategy validationStrategy = new ValidationStrategy();
        validationStrategy.setValidationConfig(new LeafContainer(new OnboardExternalEntityAsPhonepeMxnValidatorConfig()));

        when(externalEntityService.get(any()))
                .thenReturn(ExternalEntity.builder()
                        .clientProvidedId(merchantId + "_" + storeId)
                        .build());
        when(merchantOnboardingService.getStoreDoc(merchantId + "_" + storeId))
                .thenReturn(ElasticSearchRequest.builder()
                        .onboardingDate(new Date())
                        .build());
        executeVerificationTestSetup(verificationConfigs, false, validationStrategy, null, EntityType.EXTERNAL);
        verify(eventIngestionService, times(0))
                .ingestSuccessfulExternalStoreOnboarding(any(), eq(merchantId), eq(storeId));
    }

    @Test(expected = LegionException.class)
    public void OnboardExternalEntityAsPhonepeMxnValidatorFailure() {
        String merchantId = "MID";
        String storeId = "SID";

        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new NoActionVerifierConfig());

        ValidationStrategy validationStrategy = new ValidationStrategy();
        validationStrategy.setValidationConfig(new LeafContainer(new OnboardExternalEntityAsPhonepeMxnValidatorConfig()));

        when(externalEntityService.get(any()))
                .thenReturn(ExternalEntity.builder()
                        .clientProvidedId(null)
                        .build());
        executeVerificationTestSetup(verificationConfigs, false, validationStrategy, null, EntityType.EXTERNAL);
    }

  @Test
  public void dueCollectionTaskVerifyFailure() {

        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new SmartspeakerDueCollectionVerifierConfig());

        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, false, null, null, STORE);

        when(fortunaService.getPendingChargesForMerchant(anyString()))
                .thenReturn(MerchantPendingChargeResponse.builder()
                        .chargePending(true)
                        .build());
        when(paradoxService.getActiveDevicesOfMerchant(anyString(), anyString(), any()))
                .thenReturn(PaginatedResponse.<MerchantDeviceDetails>builder()
                        .pageSize(10)
                        .pageNo(1)
                        .results(Collections.emptyList())
                        .build());

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("suraj")
                .build());

        Assert.assertEquals(VERIFICATION_SUCCESS, response.getCurState());
    }

    @Test
    public void smartspeakerReversePickupVerifierTest() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new SmartspeakerReversePickupVerifierConfig());

        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, false, null, null, STORE);

        when(paradoxService.getActiveDevicesOfMerchant(anyString(), anyString(), any()))
                .thenReturn(PaginatedResponse.<MerchantDeviceDetails>builder()
                        .pageSize(10)
                        .pageNo(1)
                        .results(Collections.emptyList())
                        .build());

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("prabh")
                .build());

        Assert.assertEquals(VERIFICATION_SUCCESS, response.getCurState());
    }

    @Test
    public void selfOnboardingVerifierTest() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new SelfOnboardingMerchantVerifierConfig());

        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, false, null, null, STORE);

        when(merchantService.getMerchantFactDetails(anyString()))
                .thenReturn(MerchantProfile.builder().merchantId("123").disabled(false).build());

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("prabh")
                .build());

        Assert.assertEquals(VERIFICATION_SUCCESS, response.getCurState());
    }

    @Test(expected = LegionException.class)
    public void ssReversePickupEntityMismatch() {
        SmartSpeakerReversePickupVerifier smartSpeakerReversePickupVerifier = new SmartSpeakerReversePickupVerifier(paradoxService);
        smartSpeakerReversePickupVerifier.validate(EntityType.SECTOR, new EdcDeploymentVerifierConfig());

    }

    @Test(expected = LegionException.class)
    public void selfOnboardingEntityMismatch() {
        SelfOnboardingMerchantVerifier selfOnboardingMerchantVerifierConfig = new SelfOnboardingMerchantVerifier(merchantService);
        selfOnboardingMerchantVerifierConfig.validate(EntityType.SECTOR, new EdcDeploymentVerifierConfig());
    }

    @Test
    public void edcReversePickupEntityMatch() {
        EdcReversePickupVerifier edcReversePickupVerifier = new EdcReversePickupVerifier(paradoxService);
        edcReversePickupVerifier.validate(EntityType.STORE, new EdcDeploymentVerifierConfig());

    }

    @Test(expected = LegionException.class)
    public void edcReversePickupEntityMismatch() {
        EdcReversePickupVerifier edcReversePickupVerifier = new EdcReversePickupVerifier(paradoxService);
        edcReversePickupVerifier.validate(EntityType.SECTOR, new EdcDeploymentVerifierConfig());

    }

    @Test
    public void onboardexternalverifier() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new OnboardExternalEntityAsPhonepeMxnVerifierConfig());

        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, false, null, null, EntityType.EXTERNAL);


        when(externalEntityService.get(any())).thenReturn(ExternalEntity.builder().clientProvidedId("prabh_new").build());
        when(merchantOnboardingService.getStoreDoc(any())).thenReturn(ElasticSearchRequest.builder().onboardingDate(new Date()).build());
        doNothing().when(eventIngestionService).ingestSuccessfulExternalStoreOnboarding(any(), any(), anyString());

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("suraj")
                .build());
        Assert.assertEquals(VERIFICATION_SUCCESS, response.getCurState());
    }

    @Test
    public void edcDeployment() {

        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new EdcDeploymentVerifierConfig());

        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, false, null, null, STORE);

        when(paradoxService.getParadoxTaskStatus(anyString(), anyString(),anyString()))
                .thenReturn(TaskStatus.COMPLETED);

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("suraj")
                .build());

        Assert.assertEquals(VERIFICATION_SUCCESS, response.getCurState());
    }

    @Test
    public void edcDeploymentFailure() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new EdcDeploymentVerifierConfig());

        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, false, null, null, STORE);

        when(paradoxService.getParadoxTaskStatus(anyString(), anyString(), anyString()))
                .thenReturn(TaskStatus.NOT_COMPLETED);

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("suraj")
                .build());

        Assert.assertEquals(VERIFICATION_FAILED, response.getCurState());
    }

    @Test
    public void sbDeploymentFailure() {
        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new SoundboxDeploymentVerifierConfig());

        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, false, null, null, STORE);

        when(paradoxService.getParadoxTaskStatus(anyString(), anyString(), anyString()))
                .thenReturn(TaskStatus.NOT_COMPLETED);

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("suraj")
                .build());

        Assert.assertEquals(VERIFICATION_FAILED, response.getCurState());
    }

    @Test
    public void edcReversePickup() {

        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new EdcReversePickupVerifierConfig());

        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, false, null, null, EntityType.STORE);

        when(paradoxService.getParadoxTaskStatus(anyString(), anyString(), anyString()))
                .thenReturn(TaskStatus.COMPLETED);

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("prabh")
                .build());

        Assert.assertEquals(VERIFICATION_SUCCESS, response.getCurState());
    }

    @Test
    public void multiEdcDeployment() {

        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new MultiEdcDeploymentVerifierConfig());

        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, false, null, null, EntityType.STORE);

        when(tmsService.mappingConfirmation(anyString(), anyString()))
                .thenReturn(true);

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("prabh")
                .build());

        Assert.assertEquals(VERIFICATION_SUCCESS, response.getCurState());
    }

    @Test
    public void multiEdcDeploymentFail() {

        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new MultiEdcDeploymentVerifierConfig());

        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, false, null, null, EntityType.STORE);

        when(tmsService.mappingConfirmation(anyString(), anyString()))
                .thenReturn(false);

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("prabh")
                .build());

        Assert.assertEquals(VERIFICATION_FAILED, response.getCurState());
    }

    @Test
    public void edcReversePickupFailure() {

        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new EdcReversePickupVerifierConfig());

        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, false, null, null, EntityType.STORE);

        when(paradoxService.getParadoxTaskStatus(anyString(), anyString(), anyString()))
                .thenReturn(TaskStatus.NOT_COMPLETED);

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("prabh")
                .build());

    }

    @Test(expected = LegionException.class)
    public void edcDeploymentEntityMismatch() {
        EdcDeploymentVerifier edcDeploymentVerifier = new EdcDeploymentVerifier(paradoxService);
        edcDeploymentVerifier.validate(EntityType.SECTOR, new EdcDeploymentVerifierConfig());
    }

    @Test(expected = LegionException.class)
    public void multiEdcDeploymentEntityMismatch() {
        MultiEdcDeploymentVerifier edcDeploymentVerifier = new MultiEdcDeploymentVerifier(tmsService);
        edcDeploymentVerifier.validate(EntityType.SECTOR, new EdcDeploymentVerifierConfig());
    }

    @Test(expected = LegionException.class)
    public void agentDeboardingEntityMismatch() {
        AgentDeboardingVerifier agentDeboardingVerifier = new AgentDeboardingVerifier(legionService);
        agentDeboardingVerifier.validate(EntityType.SECTOR, new AgentDeboardingVerifierConfig());
    }


    @Test
    public void lendingTaskVerification() {

        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new MerchantLendingCompletionVerifierConfig());

        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, false, null, null, STORE);

        when(hermodService.getLendingTaskVerificationStatus(anyString(), anyString()))
                .thenReturn(Boolean.TRUE);

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("suraj")
                .build());

        Assert.assertEquals(VERIFICATION_SUCCESS, response.getCurState());
    }

    @Test
    public void agentDeboardingTaskVerification() {

        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new AgentDeboardingVerifierConfig());

        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, false, null, null, STORE);

        AgentProfile agentProfile = AgentProfile.builder().agentId("agent").agentType(AgentType.AGENT)
                .status(AgentStatus.DEACTIVATED)
                .managerId("manager").sectors(List.of("sector")).attributes(new ArrayList<>()).build();
        when(legionService.getAgentProfile(any())).thenReturn(agentProfile);
        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("shreya")
                .build());
        Assert.assertEquals(VERIFICATION_SUCCESS, response.getCurState());
    }

    @Test
    public void agentDeboardingTaskVerificationFailure() {

        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new AgentDeboardingVerifierConfig());

        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, false, null, null, STORE);

        AgentProfile agentProfile = AgentProfile.builder().agentId("agent").agentType(AgentType.AGENT)
                .status(AgentStatus.CREATED)
                .managerId("manager").sectors(List.of("sector")).attributes(new ArrayList<>()).build();
        when(legionService.getAgentProfile(any())).thenReturn(agentProfile);
        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("shreya")
                .build());
        Assert.assertEquals(VERIFICATION_FAILED, response.getCurState());
    }

    @Test
    public void multiEdcVerifierTest() {

        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new MultiEdcDeploymentVerifierConfig());

        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, false, null, null, STORE);

        when(tmsService.mappingConfirmation(anyString(), anyString()))
                .thenReturn(Boolean.TRUE);

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("prabh")
                .build());

        Assert.assertEquals(VERIFICATION_SUCCESS, response.getCurState());
    }

    @Test
    public void multiEdcVerifierTestFail() {

        List<VerificationConfig> verificationConfigs = new ArrayList<>();
        verificationConfigs.add(new MultiEdcDeploymentVerifierConfig());

        StoredTaskInstance save = executeVerificationTestSetup(verificationConfigs, false, null, null, STORE);

        when(tmsService.mappingConfirmation(anyString(), anyString()))
                .thenReturn(Boolean.FALSE);

        StoredTaskInstance response = taskManagementService.verify(TaskCompleteRequest.builder()
                .taskInstanceId(save.getTaskInstanceId())
                .completedBy("prabh")
                .build());

        Assert.assertEquals(VERIFICATION_FAILED, response.getCurState());
    }

    @Test(expected = LegionException.class)
    public void lendingTaskEntityMismatch() {
        MerchantLendingCompletionVerifier merchantLendingCompletionVerifier = new MerchantLendingCompletionVerifier(hermodService);
        merchantLendingCompletionVerifier.validate(EntityType.SECTOR, new EdcDeploymentVerifierConfig());
    }

    @Test
    public void updateInstanceMetaTest() {
        DiscoveryTaskInstance esTask = DiscoveryTaskInstance.builder()
                .taskInstanceId("TI123")
                .dueDate(1695012681000L)
                .taskInstanceMeta(TaskInstanceMeta.builder()
                        .taskMetaList(List.of(TaskMetaInformation.builder()
                                        .value("WARM")
                                        .type(TaskMetaType.LEAD_INTENT)
                                        .displayInformation(true)
                                .build()))
                        .build())
                .build();
        StoredTaskInstance storedTaskInstance = StoredTaskInstance.builder()
                .taskInstanceId("TI123")
                .secondaryIndexSyncRequired(false)
                .entityId("my_mxn")
                .entityType(EntityType.SECTOR)
                .actionId("actionId")
                .curState(AVAILABLE)
                .namespace(Namespace.MERCHANT_ONBOARDING)
                .partitionId(1)
                .createdBy("Saransh")
                .updatedBy("Saransh")
                .taskDefinitionId("TASK_DEFINITION_ID")
                .build();
        taskInstanceRepository.save(storedTaskInstance);
        when(taskESRepository.get("TI123")).thenReturn(esTask);

        TaskMetaUpdateRequest updateRequest = TaskMetaUpdateRequest.builder()
                .taskInstanceId("TI123")
                .rescheduleAt(1695185481000L)
                .userTaskCreationMeta(List.of(UserTaskCreationMeta.builder()
                                .displayInformation(true)
                                .type(TaskMetaType.LEAD_INTENT)
                                .value("HOT")
                        .build()))
                .build();
        taskInstanceManagementService.updateTaskInstanceMeta(updateRequest);
        Optional<StoredTaskInstance> taskInstance = taskInstanceRepository.get("TI123");
        Assert.assertNotNull(taskInstance);
    }

    @Test(expected = LegionException.class)
    public void testUpdateMetaFailed() {
        when(taskESRepository.get("TI1234")).thenThrow(LegionException.class);
        taskInstanceManagementService.updateTaskInstanceMeta(TaskMetaUpdateRequest.builder()
                        .taskInstanceId("TI1234")
                        .userTaskCreationMeta(List.of(UserTaskCreationMeta.builder()
                                .displayInformation(true)
                                .type(TaskMetaType.LEAD_INTENT)
                                .value("HOT")
                                .build()))
                .build());
    }

    @Test
    public void testCommentsCreation() {
        String commentId = taskInstanceManagementService.createCommentOnTask("actor", "taskInstanceId", "comment");
        Assertions.assertEquals("commentId", commentId);
    }

}