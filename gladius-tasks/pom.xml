<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>gladius</artifactId>
        <groupId>com.phonepe.merchant</groupId>
        <version>1.0.366</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>gladius-tasks</artifactId>

    <properties>
        <maven.deploy.skip>false</maven.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.phonepe.merchant</groupId>
            <artifactId>gladius-models</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.phonepe.merchant</groupId>
            <artifactId>gladius-core</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.phonepe.merchant</groupId>
            <artifactId>gladius-external</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>io.appform.dropwizard.actors</groupId>
            <artifactId>dropwizard-rabbitmq-actors</artifactId>
        </dependency>
        <dependency>
            <groupId>com.rabbitmq</groupId>
            <artifactId>amqp-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.phonepe.merchants</groupId>
            <artifactId>odin-ace-models</artifactId>
        </dependency>
        <dependency>
            <groupId>com.phonepe.gemini</groupId>
            <artifactId>gemini-model</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>io.appform.dropwizard.sharding</groupId>
            <artifactId>db-sharding-bundle</artifactId>
        </dependency>

        <dependency>
            <groupId>com.phonepe.merchant</groupId>
            <artifactId>filtercraft-client</artifactId>
            <version>${filtercraft.version}</version>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-params</artifactId>
            <version>${junit.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>ru.vyarus</groupId>
            <artifactId>dropwizard-guicey</artifactId>
        </dependency>
        <dependency>
            <groupId>com.phonepe.models</groupId>
            <artifactId>phonepe-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.phonepe.models</groupId>
            <artifactId>merchant-service-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.phonepe.payments</groupId>
            <artifactId>upi-client-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.jaxrs</groupId>
            <artifactId>jackson-jaxrs-json-provider</artifactId>
        </dependency>
        <dependency>
            <groupId>com.phonepe.growth</groupId>
            <artifactId>neuron-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.phonepe.platform.filters</groupId>
            <artifactId>api-killer-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.phonepe.platform.filters</groupId>
            <artifactId>api-killer-killswitch</artifactId>
        </dependency>
        <dependency>
            <groupId>com.phonepe.growth</groupId>
            <artifactId>neuron-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.phonepe.merchant</groupId>
            <artifactId>legion-models</artifactId>
        </dependency>
        <dependency>
            <groupId>com.phonepe.merchant</groupId>
            <artifactId>gladius-hotspots</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
</project>