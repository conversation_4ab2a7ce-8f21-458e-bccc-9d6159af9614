<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>gladius</artifactId>
        <groupId>com.phonepe.merchant</groupId>
        <version>1.0.366</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>gladius-server</artifactId>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.phonepe.merchant</groupId>
            <artifactId>gladius-models</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.phonepe.merchant</groupId>
            <artifactId>gladius-core</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.phonepe.merchant</groupId>
            <artifactId>gladius-tasks</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.phonepe.merchant</groupId>
            <artifactId>gladius-external</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mariadb.jdbc</groupId>
            <artifactId>mariadb-java-client</artifactId>
        </dependency>
        <dependency>
            <groupId>io.dropwizard.metrics</groupId>
            <artifactId>metrics-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.phonepe.platform.http.server.metrics</groupId>
            <artifactId>api-metrics</artifactId>
        </dependency>
        <dependency>
            <groupId>io.appform.dropwizard.actors</groupId>
            <artifactId>dropwizard-rabbitmq-actors</artifactId>
        </dependency>
        <dependency>
            <groupId>com.rabbitmq</groupId>
            <artifactId>amqp-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.jaxrs</groupId>
            <artifactId>jackson-jaxrs-json-provider</artifactId>
        </dependency>

        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>mariadb</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>

        <dependency>
            <groupId>com.phonepe.platform</groupId>
            <artifactId>rosey-standanlone-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.phonepe.growth</groupId>
            <artifactId>neuron-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.phonepe.platform.filters</groupId>
            <artifactId>api-killer-killswitch</artifactId>
        </dependency>

        <dependency>
            <groupId>com.phonepe.platform.killswitch</groupId>
            <artifactId>killswitch-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.appform.hope</groupId>
                    <artifactId>hope-lang</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.phonepe.merchant</groupId>
            <artifactId>gladius-hotspots</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>

    <profiles>
        <profile>
            <id>docker</id>
            <activation>
                <property>
                    <name>docker.build</name>
                    <value>true</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-deploy-plugin</artifactId>
                        <configuration>
                            <skip>true</skip>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>com.phonepe</groupId>
                        <artifactId>docker-maven-plugin</artifactId>
                        <version>0.2</version>
                        <configuration>
                            <images>
                                <image>
                                    <name>docker.phonepe.com/${project.artifactId}:${project.version}</name>
                                    <build>
                                        <contextDir>${project.basedir}</contextDir>
                                        <dockerFile>${project.basedir}/Dockerfile</dockerFile>
                                        <args>
                                            <VERSION>${project.version}</VERSION>
                                        </args>
                                    </build>
                                </image>
                            </images>
                        </configuration>
                        <executions>
                            <execution>
                                <id>build-image</id>
                                <phase>package</phase>
                                <goals><goal>build</goal></goals>
                            </execution>
                            <execution>
                                <id>push-image</id>
                                <phase>deploy</phase>
                                <goals>
                                    <goal>push</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-version}</version>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.3.0</version>
                <configuration>
                    <createDependencyReducedPom>false</createDependencyReducedPom>
                    <filters>
                        <filter>
                            <artifact>*:*</artifact>
                            <excludes>
                                <exclude>META-INF/*.SF</exclude>
                                <exclude>META-INF/*.DSA</exclude>
                                <exclude>META-INF/*.RSA</exclude>
                            </excludes>
                        </filter>
                    </filters>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <transformers>
                                <transformer implementation="org.apache.maven.plugins.shade.resource.ServicesResourceTransformer" />
                                <transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                    <manifestEntries>
                                        <Main-Class>com.phonepe.merchant.legion.server.App</Main-Class>
                                        <Build-Number>${build.number}</Build-Number>
                                        <Git-Commit>${build.revision}</Git-Commit>
                                    </manifestEntries>
                                </transformer>
                            </transformers>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>