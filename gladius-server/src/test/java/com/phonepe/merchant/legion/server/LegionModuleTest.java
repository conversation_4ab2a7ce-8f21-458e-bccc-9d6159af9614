package com.phonepe.merchant.legion.server;


import com.codahale.metrics.MetricRegistry;
        import com.phonepe.dataplatform.EventIngestorClientConfig;
import com.phonepe.merchant.legion.core.AppConfig;
import com.phonepe.platform.http.v2.discovery.HttpDiscoveryBundle;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import io.appform.ranger.discovery.bundle.ServiceDiscoveryBundle;
import io.appform.ranger.discovery.bundle.ServiceDiscoveryConfiguration;
import io.dropwizard.setup.Environment;

        import org.apache.curator.framework.CuratorFramework;
        import org.junit.jupiter.api.BeforeEach;
        import org.junit.jupiter.api.Test;
        import org.mockito.Mock;
import org.mockito.Mockito;

import static org.junit.jupiter.api.Assertions.assertNotNull;
        import static org.mockito.Mockito.mock;
        import static org.mockito.Mockito.when;

public class LegionModuleTest {
    @Mock
    private ServiceDiscoveryBundle<AppConfig> mockServiceDiscoveryBundle;

    @Mock
    private MetricRegistry mockMetricRegistry;

    @Mock
    private HttpDiscoveryBundle<AppConfig> mockHttpDiscoveryBundle;

    private LegionModule legionModule;

    @BeforeEach
    public void setup() {
         mockServiceDiscoveryBundle = Mockito.mock(ServiceDiscoveryBundle.class);
        mockMetricRegistry = Mockito.mock(MetricRegistry.class);
        mockHttpDiscoveryBundle = Mockito.mock(HttpDiscoveryBundle.class);
        legionModule = new LegionModule(mockServiceDiscoveryBundle, mockMetricRegistry, mockHttpDiscoveryBundle);
    }

    @Test
    public void testProvideCuratorFramework() {
        CuratorFramework mockCuratorFramework = mock(CuratorFramework.class);
        when(mockServiceDiscoveryBundle.getCurator()).thenReturn(mockCuratorFramework);

        CuratorFramework curatorFramework = legionModule.provideCuratorFramework();

        assertNotNull(curatorFramework);
    }

    @Test
    public void testGetMetricRegistry() {
        MetricRegistry metricRegistry = legionModule.getMetricRegistry();

        assertNotNull(metricRegistry);
    }

    @Test
    public void testProvidesEventIngestionConfig() {
        AppConfig mockAppConfig = mock(AppConfig.class);
        EventIngestorClientConfig mockEventIngestorClientConfig = mock(EventIngestorClientConfig.class);
        when(mockAppConfig.getEventIngestor()).thenReturn(mockEventIngestorClientConfig);

        EventIngestorClientConfig eventIngestorClientConfig = legionModule.providesEventIngestionConfig(mockAppConfig);

        assertNotNull(eventIngestorClientConfig);
    }

    @Test
    public void testProvideRequestEnv() {
        AppConfig mockAppConfig = mock(AppConfig.class);
        ServiceDiscoveryConfiguration mockServiceDiscoveryConfiguration = mock(ServiceDiscoveryConfiguration.class);
        when(mockAppConfig.getServiceDiscovery()).thenReturn(mockServiceDiscoveryConfiguration);

        ServiceDiscoveryConfiguration serviceDiscoveryConfiguration = legionModule.provideRequestEnv(mockAppConfig);

        assertNotNull(serviceDiscoveryConfiguration);
    }

    @Test
    public void testEndpointProviderFactory() {
        AppConfig mockCoreConfig = mock(AppConfig.class);
        Environment mockEnvironment = mock(Environment.class);
        ServiceEndpointProviderFactory serviceEndpointProviderFactory = mock(ServiceEndpointProviderFactory.class);
        when(mockHttpDiscoveryBundle.getEndpointProviderFactory()).thenReturn(serviceEndpointProviderFactory);

        ServiceEndpointProviderFactory serviceEndpointProviderFactoryRe = legionModule.endpointProviderFactory(mockCoreConfig, mockEnvironment);

        assertNotNull(serviceEndpointProviderFactoryRe);
    }
}
