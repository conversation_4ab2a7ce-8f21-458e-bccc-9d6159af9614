package com.phonepe.merchant.legion.server;

import com.phonepe.merchant.legion.tasks.utils.LeadManagementConfiguration;
import java.io.IOException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import static com.phonepe.merchant.legion.server.LeadConfigManager.LOCAL_CONFIG;
import static org.mockito.Mockito.mock;

public class LeadConfigManagerTest extends LegionServerBaseTest{

    @InjectMocks
    private LeadConfigManager leadConfigManager;

    private LeadManagementConfiguration leadManagementConfiguration;

    @BeforeEach
    public void setUp() {
        leadManagementConfiguration = mock(LeadManagementConfiguration.class);
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testRefreshLocalConfig() {
        // Arrange
        System.setProperty(LOCAL_CONFIG, "true");
        // Act
        LeadConfigManager.refresh();
        Assertions.assertEquals("true", System.getProperty(LOCAL_CONFIG));
    }

    @Test
    public void testRefreshRemoteConfig() throws IOException {
        // Arrange
        System.clearProperty(LOCAL_CONFIG);
        System.setProperty(LOCAL_CONFIG, "false");
        // Act
        LeadConfigManager.refresh();
        Assertions.assertEquals("false", System.getProperty(LOCAL_CONFIG));
    }

    @Test
    public void startTest() {
        System.clearProperty(LOCAL_CONFIG);
        leadConfigManager.start();
        Assertions.assertNull(System.getProperty(LOCAL_CONFIG));
    }

    @Test
    public void stopTest() {
        System.clearProperty(LOCAL_CONFIG);
        leadConfigManager.stop();
        Assertions.assertNull(System.getProperty(LOCAL_CONFIG));
    }
}
