package com.phonepe.merchant.legion.server;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.google.common.base.Strings;
import com.google.inject.Injector;
import com.google.inject.Stage;
import com.hystrix.configurator.core.HystrixConfigurationFactory;
import com.phonepe.dataplatform.EventIngestorClient;
import com.phonepe.frontend.chimera.client.ChimeraClientConfig;
import com.phonepe.frontend.chimera.lite.ChimeraLiteBundle;
import com.phonepe.gandalf.client.GandalfBundle;
import com.phonepe.gandalf.models.client.GandalfClientConfig;
import com.phonepe.growth.neuron.NeuronBundle;
import com.phonepe.growth.neuron.model.NeuronPulseHandler;
import com.phonepe.merchant.gladius.models.external.storage.StoredExternalClient;
import com.phonepe.merchant.gladius.models.external.storage.StoredExternalEntity;
import com.phonepe.merchant.gladius.models.hotspots.storage.StoredHotspot;
import com.phonepe.merchant.gladius.models.hotspots.storage.StoredHotspotConfig;
import com.phonepe.merchant.gladius.models.survey.storage.StoredFeedback;
import com.phonepe.merchant.gladius.models.survey.storage.StoredFormConfig;
import com.phonepe.merchant.gladius.models.tags.StoreTag;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredActionAttributeMappings;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredAuditRevision;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredCampaign;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredCommentsOnTask;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredDefinitionAttributeMappings;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskAction;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskAttribute;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskDefinition;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskInstance;
import com.phonepe.merchant.gladius.models.tasks.storage.StoredTaskTransition;
import com.phonepe.merchant.gladius.models.tasks.utils.CustomEnverListenerUtils;
import com.phonepe.merchant.legion.client.LegionBundle;
import com.phonepe.merchant.legion.client.config.LegionClientConfig;
import com.phonepe.merchant.legion.core.AppConfig;
import com.phonepe.merchant.legion.core.CoreModule;
import com.phonepe.merchant.legion.core.eventingestion.EventIngestionProvider;
import com.phonepe.merchant.legion.core.eventingestion.FoxtrotEventIngestionService;
import com.phonepe.merchant.legion.core.exceptions.LegionExceptionMapper;
import com.phonepe.merchant.legion.core.utils.AuthHeaderProviderUtil;
import com.phonepe.merchant.legion.core.utils.EsUtil;
import com.phonepe.merchant.legion.core.utils.SerDe;
import com.phonepe.merchant.legion.external.ExternalModule;
import com.phonepe.merchant.legion.tasks.TaskModule;
import com.phonepe.merchant.legion.tasks.auth.resolver.TaskCompletionByTypeResolver;
import com.phonepe.merchant.legion.tasks.auth.resolver.TaskCompletionRequestResolver;
import com.phonepe.merchant.legion.tasks.auth.resolver.TaskCreationRequestResolver;
import com.phonepe.merchant.legion.tasks.auth.resolver.TaskDeletionRequestResolver;
import com.phonepe.merchant.legion.tasks.cache.TaskDefinitionIdsByTypeCache;
import com.phonepe.merchant.legion.tasks.cache.TaskDefinitionIdsCache;
import com.phonepe.merchant.legion.tasks.eventbasedlogic.QCTaskCreationPulseHandler;
import com.phonepe.merchant.legion.tasks.eventbasedlogic.TaggingMxnToExternalEntityPulseHandler;
import com.phonepe.merchant.legion.tasks.eventbasedlogic.TaggingStoreToExternalEntityPulseHandler;
import com.phonepe.merchant.legion.tasks.eventbasedlogic.TaskCreationPulseHandler;
import com.phonepe.merchant.legion.tasks.search.query.SectorGeofenceQueryBuilder;
import com.phonepe.merchant.legion.tasks.services.ClientTaskService;
import com.phonepe.merchant.legion.tasks.services.impl.TaskInstanceManagementServiceImpl;
import com.phonepe.merchant.legion.tasks.utils.TaskEsUtils;
import com.phonepe.merchants.rmq.helper.core.bundle.RmqHelperBundle;
import com.phonepe.merchants.rmq.helper.core.events.ingestion.handler.EventIngestionHandler;
import com.phonepe.merchants.rmq.helper.core.models.RmqManagementServiceConfig;
import com.phonepe.olympus.im.client.OlympusIMBundle;
import com.phonepe.olympus.im.client.OlympusIMGuiceInjectionContext;
import com.phonepe.olympus.im.client.config.OlympusIMClientConfig;
import com.phonepe.platform.client.config.ReporterConfig;
import com.phonepe.platform.filters.ApiKiller;
import com.phonepe.platform.filters.FilterEvaluator;
import com.phonepe.platform.filters.impl.KillswitchFilterEvaluator;
import com.phonepe.platform.http.server.metrics.bundle.MetricBundle;
import com.phonepe.platform.http.v2.common.hub.RangerHubConfiguration;
import com.phonepe.platform.http.v2.discovery.HttpDiscoveryBundle;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import com.phonepe.platform.metrics.bundle.MetricIngestionBundle;
import com.phonepe.platform.requestinfo.bundle.RequestInfoBundle;
import com.phonepe.rosey.dwconfig.RoseyConfigSourceProvider;
import com.platform.validation.ValidationBundle;
import com.platform.validation.ValidationConfig;
import in.vectorpro.dropwizard.swagger.SwaggerBundle;
import in.vectorpro.dropwizard.swagger.SwaggerBundleConfiguration;
import io.appform.dropwizard.actors.RabbitmqActorBundle;
import io.appform.dropwizard.actors.TtlConfig;
import io.appform.dropwizard.actors.config.RMQConfig;
import io.appform.dropwizard.sharding.DBShardingBundle;
import io.appform.dropwizard.sharding.config.ShardedHibernateFactory;
import io.appform.functionmetrics.FunctionMetricsManager;
import io.appform.ranger.discovery.bundle.ServiceDiscoveryBundle;
import io.appform.ranger.discovery.bundle.ServiceDiscoveryConfiguration;
import io.dropwizard.Application;
import io.dropwizard.configuration.EnvironmentVariableSubstitutor;
import io.dropwizard.configuration.SubstitutingSourceProvider;
import io.dropwizard.oor.OorBundle;
import io.dropwizard.primer.model.PrimerBundleConfiguration;
import io.dropwizard.setup.Bootstrap;
import io.dropwizard.setup.Environment;
import killswitch.KillSwitchServiceClient;
import lombok.val;
import org.glassfish.jersey.logging.LoggingFeature;
import org.zapodot.hystrix.bundle.HystrixBundle;
import ru.vyarus.dropwizard.guice.GuiceBundle;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Supplier;
import java.util.logging.Level;
import java.util.logging.Logger;

import static com.phonepe.merchant.legion.core.utils.EventConstants.EVENT_APP_NAME;
import static ru.vyarus.dropwizard.guice.module.context.Disables.inPackage;


public class App extends Application<AppConfig> {

    private OlympusIMBundle<AppConfig> olympusIMBundle;
    private GuiceBundle guiceBundle;
    private HttpDiscoveryBundle<AppConfig> httpDiscoveryBundle;
    private static final String SERVICE_RANGER_NAME = "SERVICE_RANGER_NAME";

    @Override
    public void initialize(Bootstrap<AppConfig> bootstrap) {

        boolean localConfig = Boolean.parseBoolean(System.getProperty("localConfig", "false"));
        if (localConfig) {
            bootstrap.setConfigurationSourceProvider(
                    new SubstitutingSourceProvider(
                            bootstrap.getConfigurationSourceProvider(),
                            new EnvironmentVariableSubstitutor()));
        } else {
            final String teamId = System.getenv("TEAM_ID");
            final String configName = System.getenv("CONFIG_NAME");
            bootstrap.setConfigurationSourceProvider(
                    new SubstitutingSourceProvider(
                            new RoseyConfigSourceProvider(Strings.isNullOrEmpty(teamId) ? "merchant-management" : teamId,
                                    Strings.isNullOrEmpty(configName) ? "gladius" : configName),
                            new EnvironmentVariableSubstitutor()));
        }

        bootstrap.addBundle(new OorBundle<AppConfig>() {
            public boolean withOor() {
                return false;
            }
        });


        ServiceDiscoveryBundle<AppConfig> serviceDiscoveryBundle = new ServiceDiscoveryBundle<AppConfig>() {
            @Override
            protected ServiceDiscoveryConfiguration getRangerConfiguration(AppConfig appConfig) {
                return appConfig.getServiceDiscovery();
            }

            @Override
            protected String getServiceName(AppConfig appConfig) {
                final String envDiscoveredName = System.getenv(SERVICE_RANGER_NAME);
                return Strings.isNullOrEmpty(envDiscoveredName) ? "gladius" : envDiscoveredName;
            }

            @Override
            protected int getPort(AppConfig appConfig) {
                return appConfig.getServiceDiscovery().getPublishedPort();
            }

        };
        bootstrap.addBundle(serviceDiscoveryBundle);


        this.httpDiscoveryBundle = new HttpDiscoveryBundle<AppConfig>() {

            @Override
            protected RangerHubConfiguration getHubConfiguration(AppConfig configuration) {
                return configuration.getRangerHubConfiguration();
            }
        };

        bootstrap.addBundle(httpDiscoveryBundle);

        DBShardingBundle<AppConfig> dbShardingBundle = new DBShardingBundle<AppConfig>(
                StoredTaskInstance.class,
                StoredTaskAction.class,
                StoredAuditRevision.class,
                StoredTaskDefinition.class,
                StoredTaskTransition.class,
                StoredCampaign.class,
                StoredExternalEntity.class,
                StoredExternalClient.class,
                StoreTag.class,
                StoredTaskAttribute.class,
                StoredDefinitionAttributeMappings.class,
                StoredActionAttributeMappings.class,
                StoredCommentsOnTask.class,
                StoredHotspot.class,
                StoredFormConfig.class,
                StoredFeedback.class,
                StoredHotspotConfig.class
        ) {
            @Override
            protected ShardedHibernateFactory getConfig(AppConfig appConfig) {
                return appConfig.getShards();
            }
        };

        bootstrap.addBundle(dbShardingBundle);

        bootstrap.addBundle(HystrixBundle.builder()
                .disableStreamServletInAdminContext()
                .withApplicationStreamPath("/hystrix.stream")
                .build()
        );

        bootstrap.addBundle(new ValidationBundle<AppConfig>() {

            @Override
            public ValidationConfig getValidationConfig(AppConfig apiConfiguration) {
                return apiConfiguration.getValidationConfig();
            }
        });

        ChimeraLiteBundle<AppConfig> chimeraLiteBundle = new ChimeraLiteBundle<>() {
            @Override
            protected ChimeraClientConfig configuration(AppConfig configuration) {
                return configuration.getChimeraLiteConfig().getChimeraClientConfig();
            }

            @Override
            protected ServiceEndpointProviderFactory serviceEndpointProviderFactory(AppConfig config) {
                return httpDiscoveryBundle.getEndpointProviderFactory();
            }

            @Override
            protected String team(AppConfig configuration) {
                return configuration.getChimeraLiteConfig().getTeamName();
            }
            @Override
            protected Supplier<String> authSupplier(AppConfig appConfig) {
                return AuthHeaderProviderUtil::getSystemAuthHeader;
            }
        };
        bootstrap.addBundle(chimeraLiteBundle);

        GandalfBundle<AppConfig> gandalfBundle = new GandalfBundle<AppConfig>() {

            @Override
            protected ServiceEndpointProviderFactory getEndpointProviderFactory() {
                return httpDiscoveryBundle.getEndpointProviderFactory();
            }

            @Override
            protected GandalfClientConfig getGandalfClientConfig(AppConfig appConfig) {
                return appConfig.getGandalfConfig();
            }

            @Override
            protected PrimerBundleConfiguration getGandalfPrimerConfig(AppConfig appConfig) {
                return appConfig.getPrimerBundleConfiguration();
            }
        };

        this.olympusIMBundle = new OlympusIMBundle<AppConfig>() {

            @Override
            protected OlympusIMClientConfig getOlympusIMClientConfig(AppConfig configuration) {
                return configuration.getOlympusIMClientConfig();
            }

            @Override
            protected Supplier<Injector> getGuiceInjector() {
                return guiceBundle::getInjector;
            }

            @Override
            protected Supplier<EventIngestorClient> getEventIngestorClient() {
                return ()-> guiceBundle.getInjector().getInstance(EventIngestionProvider.class).getEventIngestorClient();
            }
        };


        bootstrap.addBundle(olympusIMBundle);

        bootstrap.addBundle(gandalfBundle);

        RabbitmqActorBundle<AppConfig> rabbitmqActorBundle = new RabbitmqActorBundle<AppConfig>() {
            @Override
            protected TtlConfig ttlConfig() {
                return null;
            }

            @Override
            protected RMQConfig getConfig(AppConfig appConfig) {
                return appConfig.getActorRmqConfig();
            }
        };
        bootstrap.addBundle(rabbitmqActorBundle);
        bootstrap.addBundle(new RequestInfoBundle());


        bootstrap.addBundle(new SwaggerBundle<AppConfig>() {
            @Override
            protected SwaggerBundleConfiguration getSwaggerBundleConfiguration(AppConfig configuration) {
                return configuration.getSwagger();
            }
        });

        guiceBundle = GuiceBundle.builder()
                .enableAutoConfig("com.phonepe.merchant.legion")
                .modules(
                        new LegionModule(serviceDiscoveryBundle, bootstrap.getMetricRegistry(), httpDiscoveryBundle),
                        new CoreModule(),
                        new ExternalModule(dbShardingBundle) {},
                        new TaskModule(dbShardingBundle, rabbitmqActorBundle, chimeraLiteBundle) {}
                )
                .disable(inPackage("com.phonepe.merchant.legion.location"))
                .build(Stage.DEVELOPMENT);

        MetricIngestionBundle<AppConfig> metricIngestionBundle =new  MetricIngestionBundle<AppConfig>() {
                @Override
                public ReporterConfig reporterConfig(AppConfig configuration) {
                    return configuration.getReporterConfig();
                }

                @Override
                public Supplier<String> authTokenSupplier(AppConfig configuration) {
                    return olympusIMBundle.getOlympusIMClient()::getSystemAuthHeader;
                }

                @Override
                public ServiceEndpointProviderFactory getServiceEndpointProviderFactory(AppConfig configuration) {
                    return httpDiscoveryBundle.getEndpointProviderFactory();
                }
                @Override
                public boolean registerMicrometer() {
                    return false;
                }
            };
        bootstrap.addBundle(metricIngestionBundle);
        bootstrap.addBundle(new MetricBundle<>());

        bootstrap.addBundle(guiceBundle);

        NeuronBundle<AppConfig> neuronBundle = new NeuronBundle<AppConfig>() {
            @Override
            public List<NeuronPulseHandler> getRegisteredPulseHandlers() {
                List<NeuronPulseHandler> pulseHandlers = new ArrayList<>();
                Injector injector = guiceBundle.getInjector();

                ClientTaskService clientTaskService = injector.getInstance(ClientTaskService.class);

                pulseHandlers.add(new TaggingMxnToExternalEntityPulseHandler(clientTaskService));
                pulseHandlers.add(new TaggingStoreToExternalEntityPulseHandler(clientTaskService));
                pulseHandlers.add(new TaskCreationPulseHandler(clientTaskService));
                pulseHandlers.add(new QCTaskCreationPulseHandler(clientTaskService));

                return pulseHandlers;
            }
        };
        bootstrap.addBundle(neuronBundle);

        bootstrap.addBundle(new LegionBundle<AppConfig>() {
            @Override
            protected LegionClientConfig getLegionClientConfig(AppConfig appConfig) {
                return appConfig.getLegionClientConfig();
            }
            @Override
            public Supplier<String> authTokenSupplier() {
                return olympusIMBundle.getOlympusIMClient()::getSystemAuthHeader;
            }
            @Override
            protected ServiceEndpointProviderFactory getServiceEndpointProviderFactory() {
                return httpDiscoveryBundle.getEndpointProviderFactory();
            }
        });

        RmqHelperBundle<AppConfig> rmqHelperBundle = new RmqHelperBundle<>() {
            @Override
            protected RMQConfig getRmqConfig(AppConfig appConfig) {
                return appConfig.getActorRmqConfig();
            }

            @Override
            protected RmqManagementServiceConfig getRmqManagementServiceConfig(AppConfig appConfig) {
                return null;
            }

            @Override
            protected EventIngestionHandler getEventIngestionHandler() {
                Injector injector = guiceBundle.getInjector();
                EventIngestionProvider eventIngestionProvider = injector.getInstance(EventIngestionProvider.class);
                return new EventIngestionHandler(eventIngestionProvider::getEventIngestorClient, EVENT_APP_NAME);
            }
        };
        bootstrap.addBundle(rmqHelperBundle);

    }

    @Override
    public void run(AppConfig appConfig, Environment environment) {
        val injector = guiceBundle.getInjector();
        AuthHeaderProviderUtil.init(olympusIMBundle.getOlympusIMClient());
        environment.getObjectMapper().setSerializationInclusion(JsonInclude.Include.NON_EMPTY);
        environment.getObjectMapper().setSerializationInclusion(JsonInclude.Include.NON_NULL);
        environment.getObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        environment.getObjectMapper().configure(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_USING_DEFAULT_VALUE, true);

        SerDe.init(environment.getObjectMapper());
        EsUtil.init(appConfig.getEsTaskIndexName());
        TaskEsUtils.init(injector.getInstance(SectorGeofenceQueryBuilder.class));

        CustomEnverListenerUtils.setAuditableTaskActionIds(appConfig.getAuditableTaskActionIds());

        HystrixConfigurationFactory.init(appConfig.getHystrix());

        environment.jersey().register(new LegionExceptionMapper());

        if (appConfig.isLoggingEnabled()) {
            environment.jersey().register(new LoggingFeature(
                    Logger.getLogger(getClass().getName()), Level.INFO, null, null
            ));
        }
        val metrics = environment.metrics();
        FunctionMetricsManager.initialize("commands", metrics);
        KillSwitchServiceClient killSwitchServiceClient = injector.getInstance(KillSwitchServiceClient.class);
        FilterEvaluator filterEvaluator = new KillswitchFilterEvaluator(killSwitchServiceClient.getClient());
        environment.jersey().register(new ApiKiller(filterEvaluator));
        OlympusIMGuiceInjectionContext.OLYMPUS_IM_GUICE_INJECTION_CONTEXT
                .set(TaskDeletionRequestResolver.class, new TaskDeletionRequestResolver(()->olympusIMBundle.getOlympusIMClient(), injector.getInstance(TaskDefinitionIdsCache.class), injector.getInstance(TaskInstanceManagementServiceImpl.class), injector.getInstance(FoxtrotEventIngestionService.class), injector.getInstance(TaskDefinitionIdsByTypeCache.class)));
        OlympusIMGuiceInjectionContext.OLYMPUS_IM_GUICE_INJECTION_CONTEXT
                .set(TaskCreationRequestResolver.class, new TaskCreationRequestResolver(()->olympusIMBundle.getOlympusIMClient(), injector.getInstance(TaskDefinitionIdsCache.class), injector.getInstance(TaskInstanceManagementServiceImpl.class), injector.getInstance(FoxtrotEventIngestionService.class), injector.getInstance(TaskDefinitionIdsByTypeCache.class)));
        OlympusIMGuiceInjectionContext.OLYMPUS_IM_GUICE_INJECTION_CONTEXT
                .set(TaskCompletionRequestResolver.class, new TaskCompletionRequestResolver(()->olympusIMBundle.getOlympusIMClient(), injector.getInstance(TaskDefinitionIdsCache.class), injector.getInstance(TaskInstanceManagementServiceImpl.class),injector.getInstance(FoxtrotEventIngestionService.class), injector.getInstance(TaskDefinitionIdsByTypeCache.class)));
        OlympusIMGuiceInjectionContext.OLYMPUS_IM_GUICE_INJECTION_CONTEXT
                .set(TaskCompletionByTypeResolver.class, new TaskCompletionByTypeResolver(()->olympusIMBundle.getOlympusIMClient(), injector.getInstance(TaskDefinitionIdsCache.class), injector.getInstance(TaskInstanceManagementServiceImpl.class),injector.getInstance(FoxtrotEventIngestionService.class), injector.getInstance(TaskDefinitionIdsByTypeCache.class)));

    }

    public static void main(String[] args) throws Exception {
        App gladiusService = new App();
        gladiusService.run(args);
    }

}
