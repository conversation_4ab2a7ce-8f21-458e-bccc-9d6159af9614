package com.phonepe.merchant.legion.server;

import com.codahale.metrics.MetricRegistry;
import com.google.inject.AbstractModule;
import com.google.inject.Inject;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import com.phonepe.dataplatform.EventIngestorClientConfig;
import com.phonepe.merchant.filtercraft.filters.FilterCraftModule;
import com.phonepe.merchant.legion.core.AppConfig;
import com.phonepe.merchant.legion.hotspots.HotspotModule;
import com.phonepe.platform.http.v2.discovery.HttpDiscoveryBundle;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import io.appform.ranger.discovery.bundle.ServiceDiscoveryBundle;
import io.appform.ranger.discovery.bundle.ServiceDiscoveryConfiguration;
import io.dropwizard.setup.Environment;
import org.apache.curator.framework.CuratorFramework;

public class LegionModule extends AbstractModule {
    private final ServiceDiscoveryBundle<AppConfig> serviceDiscoveryBundle;
    private final MetricRegistry metricRegistry;
    private final HttpDiscoveryBundle<AppConfig> httpDiscoveryBundle;

    @Inject
    public LegionModule(ServiceDiscoveryBundle<AppConfig> serviceDiscoveryBundle,
                        MetricRegistry metricRegistry, HttpDiscoveryBundle httpDiscoveryBundle){
        this.serviceDiscoveryBundle = serviceDiscoveryBundle;
        this.metricRegistry = metricRegistry;
        this.httpDiscoveryBundle = httpDiscoveryBundle;
    }

    @Override
    protected void configure() {
        install(new FilterCraftModule());
        install(new HotspotModule());
    }

    @Provides
    @Singleton
    public CuratorFramework provideCuratorFramework() {
        return this.serviceDiscoveryBundle.getCurator();
    }

    @Provides
    @Singleton
    public MetricRegistry getMetricRegistry(){
        return this.metricRegistry;
    }

    @Provides
    @Singleton
    public EventIngestorClientConfig providesEventIngestionConfig(AppConfig appConfig) {
        return appConfig.getEventIngestor();
    }

    @Provides
    @Singleton
    public ServiceDiscoveryConfiguration provideRequestEnv(AppConfig appConfig) {
        return appConfig.getServiceDiscovery();
    }

    @Singleton
    @Provides
    public ServiceEndpointProviderFactory endpointProviderFactory(AppConfig coreConfig,
                                                                  final Environment environment) {
        return httpDiscoveryBundle.getEndpointProviderFactory();
    }
}
