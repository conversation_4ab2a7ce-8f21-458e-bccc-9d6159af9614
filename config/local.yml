gladiusConfig:
  namespace: phonepe
  rangerName: gladius
  env: stage

server:
  maxThreads: 256
  minThreads: 256
  adminMaxThreads: 4
  adminMinThreads: 4
  applicationConnectors:
    - type: http
      port: 7078
      acceptorThreads: 4
      selectorThreads: 1
  adminConnectors:
    - type: http
      port: 7079
      acceptorThreads: 1
      selectorThreads: 1
  applicationContextPath: /
  requestLog:
    appenders:
      - type: console
        timeZone: IST

gandalfConfig:
  secureCookies: true
  httpConfig:
    clientId: GandalfClient
    usingZookeeper: false
    host: gandalf.nixy.stg-drove.phonepe.nb6
    port: 443
    secure: true
    serviceName: gandalf
    environment: stage
    connections: 10
    idleTimeOutSeconds: 30
  authConfig:
    clientId: gladius
    clientKey:  1234
    namespace:  gladius
  permissionCacheConfig:
    ttlSeconds: 900
    enabled: true

docstoreService:
  clientId: docstore
  usingZookeeper: false
  serviceName: docstore
  environment: stage
  host: stg-docstore.phonepe.nb6
  port: 80
  connections: 10
  idleTimeOutSeconds: 30
  connectTimeoutMs: 10000
  opTimeoutMs: 10000

logging:
  level: DEBUG
  # Logger-specific levels.O
  loggers:
    com.phonepe.legion.profile.resource: DEBUG

    org.hibernate.SQL: INFO
    org.hibernate: WARN
  appenders:
    - type: console
      threshold: INFO
      timeZone: IST
      logFormat: "%(%-5level) [%date] [%thread] [%logger{0}]: %message%n"

loggingEnabled: true

serviceDiscovery:
  namespace: phonepe
  environment: local
  zookeeper: localhost:2181
  publishedHost: localhost
  publishedPort: 8080
  connectionRetryIntervalMillis: 5000

swagger:
  resourcePackage: com.phonepe.merchant.legion.external.resources,com.phonepe.merchant.legion.tasks.resources,com.phonepe.merchants.rmq.helper.core.resources
  title: Gladius Service
  description: Gladius Service

hystrix:
  defaultConfig:
    threadPool:
      timeout: 15000
      concurrency: 16

eventIngestor:
  farmId: NB6
  clientId: EventIngestionService
  usingZookeeper: false
  host: events.nixy.stg-drove.phonepe.nb6
  port: 80
  environment: stage
  serviceName: dp-ingestion-api
  namespace: phonepe
  clientType: queued
  queuePath: /var/tmp

shards:
  shards:
    - driverClass: org.mariadb.jdbc.Driver
      user: aceuser_rw
      password: Koh9mydia&iorts
      url: *************************************************************
      properties:
        charSet: UTF-8
        hibernate.dialect: org.hibernate.dialect.MySQLDialect
        hibernate.envers.autoRegisterListeners: false
        # hibernate.hbm2ddl.auto: update
      maxWaitForConnection: 1s
      validationQuery: "/* MyApplication Health Check */ SELECT 1"
      minSize: 8
      maxSize: 8
      initialSize: 8
      logAbandonedConnections: true
      logValidationErrors: true
      checkConnectionWhileIdle: true
      checkConnectionOnConnect: true
      validationQueryTimeout: 1s
      removeAbandoned: true
      evictionInterval: 30s
      minIdleTime: 1m
    - driverClass: org.mariadb.jdbc.Driver
      user: aceuser_rw
      password: Koh9mydia&iorts
      url: *************************************************************
      properties:
        charSet: UTF-8
        hibernate.dialect: org.hibernate.dialect.MySQLDialect
        hibernate.envers.autoRegisterListeners: false
        # hibernate.hbm2ddl.auto: update
      maxWaitForConnection: 1s
      validationQuery: "/* MyApplication Health Check */ SELECT 1"
      minSize: 8
      maxSize: 8
      initialSize: 8
      logAbandonedConnections: true
      logValidationErrors: true
      checkConnectionWhileIdle: true
      checkConnectionOnConnect: true
      validationQueryTimeout: 1s
      removeAbandoned: true
      evictionInterval: 30s
      minIdleTime: 1m

primerBundleConfiguration:
  enabled: true
  authTypesEnabled:
    CONFIG: false
    ANNOTATION: true
  absentTokenStatus: UNAUTHORIZED
  endpoint:
    clientId: primer
    type: simple
    host: primer.nixy.stg-drove.phonepe.nb6
    clientId: primer
    port: 80
  cacheExpiry: 60
  cacheMaxSize: 100000
  clockSkew: 60
  prefix: Bearer
  privateKey: TwhjV5ujkvb41frpmqCve7ZfhqwSDMqOXe01DeDIsb2xCrW4bwfFnax9bi2uC9Kn

validationConfig:
  headerValidationConfig:
    validateRelativePathValue: false
    headersVsForbiddenRegexList:
      X-DEVICE-FINGERPRINT:
        - \+.\/
        - \.+\\
  pathValidationConfig:
    validateHttpRelativePath: true
    forbiddenRegexList:
      - \.+\/
      - \.+\\

esConfig:
  embedded: false
  cluster: stg-tekes7xx
  replicas: 1
  shards: 5
  port: 9300
  userName: dev_user_rw
  password: 8E9mD54NCS
  secure: false
  maxConnTotal: 50
  maxConnPerRoute: 20
  hosts:
   - stg-es709.phonepe.nb6


#actorRmqConfig:
#  brokers:
#    - host: localhost
#      port: 5671
#  threadPoolSize: 100
#  userName: guest
#  password: guest
#  secure: false

actorRmqConfig:
  brokers:
    - host: stg-rmq101.phonepe.nb6
      port: 5671
    - host: stg-rmq103.phonepe.nb6
      port: 5671
  threadPoolSize: 10
  userName: admin
  password: admin
  secure: true

conduitClientConfig:
  host: http://conduit-service.traefik.stg.phonepe.nb6
  port: 80
  jwtToken: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

agentActionsConfig:
  config:
    AGENT_TODO:
      points: 12

formConfigs:
  NOC:
    -  merchantBusinessUnits:
         - NKA
       agentRoles:
         - AGENT
       formConfig:
        url: https://surveys-stg.phonepe.com/v1_resume/campaign/C2111171154383026316520/index.html
        locationValidation: false
        radius:
        feedbackAuditConfigured: false

  STORE_SURVEY:
    - merchantBusinessUnits:
        - NKA
      agentRoles:
      formConfig:
        url: https://surveys-stg.phonepe.com/v1_resume/campaign/C2404031356228751414840/index.html
        locationValidation: true
        radius: 100
        feedbackAuditConfigured: false

    - merchantBusinessUnits:
        - ENTERPRISE
        - NKA
        - LKA
        - TRANSIT_AND_GOVT
        - OFFLINE_AGGREGATOR
        - ONLINE_AGGREGATOR
      agentRoles:
      formConfig:
        url: https://surveys-stg.phonepe.com/v1_resume/campaign/C2404031356228751414840/index.html
        locationValidation: true
        radius: 100
        feedbackAuditConfigured: false

  STORE_AUDIT:
  - merchantBusinessUnits:
      - NKA
    agentRoles:
      - TL
      - TSM
      - ASM
      - SH
      - ZSM
      - NSH
    formConfig:
      url: https://surveys-stg.phonepe.com/v1_resume/campaign/C2111171757578108420174/index.html
      locationValidation: true
      radius: 100
      feedbackAuditConfigured: false

  EDC_MERCHANT_FORM_TD:
    - merchantBusinessUnits:
      agentRoles:
      formConfig:
        url: https://surveys-stg.phonepe.com/v2/campaign/index.html?campaignId=C2506161823028110656484
        locationValidation: false
        radius:
        feedbackAuditConfigured: true

  SS_MERCHANT_FORM_TD:
    - merchantBusinessUnits:
      agentRoles:
      formConfig:
        url: https://surveys-stg.phonepe.com/v2/campaign/index.html?campaignId=C2506271305458255990396
        locationValidation: false
        radius:
        feedbackAuditConfigured: true

  LENDING_MERCHANT_FORM_TD:
    - merchantBusinessUnits:
      agentRoles:
      formConfig:
        url:
        locationValidation: false
        radius:
        feedbackAuditConfigured: false

  AQR_CHECKLIST:
    - merchantBusinessUnits:
      agentRoles:
      formConfig:
        url: https://surveys-stg.phonepe.com/v1_resume/campaign/C2110181539497818607978/index.html
        locationValidation: false
        radius:
        feedbackAuditConfigured: false

  NO_COMPETITION:
    - merchantBusinessUnits:
      agentRoles:
      formConfig:
        url: https://surveys-stg.phonepe.com/v1_resume/campaign/C2111171150398226316481/index.html
        locationValidation: false
        radius:
        feedbackAuditConfigured: false

  SB_DEPLOY:
    - merchantBusinessUnits:
      agentRoles:
      formConfig:
        url: https://surveys-stg.phonepe.com/v1/campaign/C2505061613202480388066/index.html
        locationValidation: false
        radius:
        feedbackAuditConfigured: false

actorConfigs:
  TASK_VERIFICATION:
    exchange: legion_task_verification
    delayed: false
    prefix: legion.task
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_EXPONENTIAL_BACKOFF
      maxAttempts: 6
      multipier: 50
      maxTimeBetweenRetries: 30s

  TASK_ES_UPDATE_RETRY:
    exchange: legion_task_es_update_retry
    delayed: false
    prefix: legion.task
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_EXPONENTIAL_BACKOFF
      maxAttempts: 6
      multipier: 50
      maxTimeBetweenRetries: 30s

  TASK_SCHEDULE_VERIFICATION:
    exchange: legion_task_schedule_verification
    delayed: false
    prefix: legion.task
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_EXPONENTIAL_BACKOFF
      maxAttempts: 6
      multipier: 50
      maxTimeBetweenRetries: 30s

  TASK_ES_CHANGE_EVENT:
    exchange: legion_task_es_change_event
    delayed: false
    prefix: legion.task
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_EXPONENTIAL_BACKOFF
      maxAttempts: 6
      multipier: 50
      maxTimeBetweenRetries: 30s

  QC_TASK_CREATE_EVENT:
    exchange: legion_qc_task_create_event
    delayed: false
    prefix: legion.task
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_EXPONENTIAL_BACKOFF
      maxAttempts: 6
      multipier: 50
      maxTimeBetweenRetries: 30s

  TASK_CREATION_FROM_EVENT:
    exchange: legion_create_task_from_event
    delayed: false
    prefix: legion.task
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_EXPONENTIAL_BACKOFF
      maxAttempts: 6
      multipier: 50
      maxTimeBetweenRetries: 30s

  TASK_RECREATION:
    exchange: legion_recreate_task
    delayed: false
    prefix: legion.task
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_EXPONENTIAL_BACKOFF
      maxAttempts: 3
      multipier: 50
      maxTimeBetweenRetries: 30s

  CLIENT_TASK_CREATE_AND_ASSIGN:
    exchange: client_task_create_and_assign
    delayed: false
    prefix: legion.task
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_EXPONENTIAL_BACKOFF
      maxAttempts: 1
      multipier: 50
      maxTimeBetweenRetries: 30s

  CLIENT_TASK_DELETE:
    exchange: client_task_delete
    delayed: false
    prefix: legion.task
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_EXPONENTIAL_BACKOFF
      maxAttempts: 1
      multipier: 50
      maxTimeBetweenRetries: 30s

  CLIENT_TASK_MANUAL_VERIFICATION:
    exchange: client_task_manual_verification
    delayed: false
    prefix: legion.task
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_EXPONENTIAL_BACKOFF
      maxAttempts: 1
      multipier: 50
      maxTimeBetweenRetries: 30s

  HOTSPOT_SYNC:
    exchange: hotspot_sync
    delayed: false
    prefix: legion.task
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_EXPONENTIAL_BACKOFF
      maxAttempts: 3
      multipier: 50
      maxTimeBetweenRetries: 30s

caches:
  AGENT_ACTION:
    expiryInSeconds: 1800
  CAMPAIGN_QUESTIONS:
    expiryInSeconds: 3600
  TASK_FILTERS:
    expiryInSeconds: 21600
  TASK_ATTRIBUTES:
    expiryInSeconds: 21600
  TASK_DEFINITION:
    expiryInSeconds: 1800
  TASK_ACTION_IDS:
    expiryInSeconds: 1800
  FILTERCRAFT_BUILDER:
    expiryInSeconds: 1800
  GENERAL_PURPOSE:
    expiryInSeconds: 1800
  ALL_TASK_DEFINITION:
    expiryInSeconds: 1800
  TASK_DEFINITION_TYPE_MAPPING:
    expiryInSeconds: 1800
  ACTION_DETAILS:
    expiryInSeconds: 1800
  HOTSPOT_CONFIG:
    expiryInSeconds: 1800
  FORM_CONFIG:
    expiryInSeconds: 3600
  LEAD_INTENTS:
    expiryInSeconds: 1800
  HOTSPOT_TASK_STAT:
    expiryInSeconds: 86400


storeCheckInConfig:
  diffInCheckInMillisSecs: 8640000

entitySourceMetaMapping:
  STORE:
    source: storeDetails
  MERCHANT:
    source: merchantDetails

eventIngestionEnabled: true

docstoreFolderConfig:
  folderInfo:
    TASK_REPORT:
        folderName: "TnP reporting"
        folderId: "FC4wLnoB-p02LmDCB8Jj"
    CLIENT_DATA_REPORT:
      folderName: "MCC Report"
      folderId: "T5z5KnsBzhfrCpmSPP-g"

clientQcTaskConfig:
  qcTaskCampaignId: "QC_TASK_CAMPAIGN"
  mapping:
    TD2106281728150081220985:
      taskDefinitionId: "TD2107011943348304694200"
      description: "Qc task"
    TD2107071621327684915447:
      taskDefinitionId: "TD2107011943348304694200"
      description: "Qc task"
    TD2107081348442489731001:
      taskDefinitionId: "TD2107011943348304694200"
      description: "Qc task"
    TD2107081346363129731680:
      taskDefinitionId: "TD2107011943348304694200"
      description: "Qc task"

reporterConfig:
  prefix: phonepe.stage.legion
  pollingInterval: 30
  attributes:
  cluster: echosystem-riemann   # This is requried for ingestion to influx - reach out to slack channel #metrics-ext for knowing which value to use here
  metricServiceConfig:
    namespace: phonepe
    logLevel: BASIC
    zookeeper: <connectionString>
    httpConfiguration:
      clientId: MetricsIngestion
      usingZookeeper: false
      host: metricingestion.nixy.stg-drove.phonepe.nb6
      serviceName: metricingestion
      environment: prod
      connections: 10
      idleTimeOutSeconds: 30
  farm: nb6                 #inject FARM_ID as env variable from marathon/rosey

miscellaneous:
  maxGeoSortDistance: 5
  rescheduleOffsetInDays: 10
  maxPossibleRescheduleOffsetInDays: 90
  formSubmitLocationRadiusInMeters: 100
  maxAllowedDistanceForSelfAssign: 50

olympusIMClientConfig:
  httpConfig:
    clientId: olympusClient
    usingZookeeper: false
    host: olympus-im-stage.phonepe.com
    port: 443
    serviceName: olympusIM
    environment: stage
    connections: 10
    idleTimeOutSeconds: 30
    secure: true
  authConfig:
    componentId: GLADIUS
    componentInstanceId: GLADIUS_NB6
    clientId: GLADIUS_NB6
    clientKey: ca6c6137-7dce-4fb1-8e45-884c806aecad
  publicEndpoint: http://localhost:10000
  authEndpoint: https://olympus-im-stage.phonepe.com
  resourcePrefix: /olympus/im

rangerHubConfiguration:
  type: HTTP
  namespace: phonepe
  nodeRefreshTimeMs: 5000
  hubHost: rangerzk.nixy.stg-drove.phonepe.nb6
  port: 80
  secure: false
  registry:
    host: zeus.nixy.stg-drove.phonepe.nb6
    port: 80
    scheme: HTTP
  services:
    - clientId: HermodService
      usingZookeeper: false
      serviceName: hermod
      environment: stage
      host: hermod.nixy.stg-drove.phonepe.nb6
      port: 80
      connections: 10
      idleTimeOutSeconds: 30
    - clientId: FortunaService
      usingZookeeper: false
      serviceName: fortuna
      environment: stage
      host: fortuna.traefik.stg.phonepe.nb6
      port: 80
      connections: 10
      idleTimeOutSeconds: 30
    - clientId: ParadoxService
      usingZookeeper: false
      serviceName: paradox
      environment: stage
      host: paradox.nixy.stg-drove.phonepe.nb6
      port: 80
      connections: 10
      idleTimeOutSeconds: 30
    - clientId: OdinService
      usingZookeeper: false
      serviceName: odin
      environment: stage
      host: odin.nixy.stg-drove.phonepe.nb6
      port: 80
      connections: 10
      idleTimeOutSeconds: 30
    - clientId: LegionService
      usingZookeeper: false
      serviceName: legion
      environment: stage
      host: legion.nixy.stg-drove.phonepe.nb6
      port: 80
      connections: 10
      idleTimeOutSeconds: 30
    - clientId: TmsService
      usingZookeeper: false
      serviceName: tmsservice
      environment: stage
      host: tmsservice.nixy.stg-drove.phonepe.nb6
      port: 80
      connections: 10
      idleTimeOutSeconds: 30
    - clientId: BrickbatService
      usingZookeeper: false
      serviceName: brickbat
      environment: stage
      host: brickbat.nixy.stg-drove.phonepe.nb6
      port: 80
      connections: 10
      idleTimeOutSeconds: 30
    - clientId: FoxtrotService
      usingZookeeper: false
      serviceName: foxtrot
      environment: stage
      host: moses.nixy.stg-drove.phonepe.nb6
      port: 80
      connections: 10
      idleTimeOutSeconds: 30
    - clientId: ClockWorkService
      usingZookeeper: false
      serviceName: clockwork-server
      environment: stage
      host: clockwork.traefik.stg.phonepe.nb6
      port: 80
      connections: 10
      idleTimeOutSeconds: 30
    - clientId: MerchantOnboardingService
      usingZookeeper: false
      serviceName: merchantOnboarding
      environment: stage
      host: merchant-onboarding.traefik.stg.phonepe.nb6
      port: 80
      connections: 10
      idleTimeOutSeconds: 30
    - clientId: MerchantService
      usingZookeeper: false
      serviceName: merchant-service
      environment: stage
      host: merchant-service.nixy.stg-drove.phonepe.nb6
      port: 80
      connections: 10
      idleTimeOutSeconds: 30
    - clientId: AtlasService
      usingZookeeper: false
      serviceName: atlas
      environment: stage
      host: atlas.nixy.stg-drove.phonepe.nb6
      port: 80
      connections: 10
      idleTimeOutSeconds: 30
    - clientId: ScoutService
      usingZookeeper: false
      serviceName: scout
      environment: stage
      host: scout.nixy.stg-drove.phonepe.nb6
      port: 80
      connections: 10
      idleTimeOutSeconds: 30
    - clientId: IntelService
      usingZookeeper: false
      serviceName: intel
      environment: stage
      host: intel.nixy.stg-drove.phonepe.nb6
      port: 80
      connections: 10
      idleTimeOutSeconds: 30
    - clientId: KillswitchService
      usingZookeeper: false
      serviceName: killswitch
      environment: stage
      host: killswitch-server.nixy.stg-drove.phonepe.nb6
      port: 80
      connections: 10
      idleTimeOutSeconds: 30
    - clientId: GeminiService
      usingZookeeper: false
      serviceName: gemini
      environment: stage
      host: gemini.nixy.stg-drove.phonepe.nb6
      port: 80
      connections: 10
      idleTimeOutSeconds: 30

legionClientConfig:
  httpConfig:
    clientId: LegionClient
    serviceName: legion
    environment: stage
    usingZookeeper: false
    host: legion.nixy.stg-drove.phonepe.nb6
    port: 80
    connections: 10
    idleTimeOutSeconds: 30

olympusMigrationConfig:
  olympusMigratedServices:
    - FortunaService
    - FoxtrotService
    - LegionService
    - MerchantOnboardingService
    - MerchantService
    - EventIngestorClient
    - ParadoxService
    - EventIngestorClient
    - ParadoxService
    - GandalfClient
    - ClockWorkService
    - ChimeraLiteBundle
    - AtlasService
    - IntelService
    - OdinService

userTaskCreationConfigs:
  taskCreationParametersList:
    - taskType: "USER_CREATED_SS_DEPLOYMENT"
      definitionId: TD2309141458517097843563
      campaignId: DEFAULT
    - taskType: "USER_CREATED_EDC_DEPLOYMENT"
      definitionId: TD2211180220455327912339
      campaignId: DEFAULT

atlasClientConfig:
  httpConfig:
    usingZookeeper: true
    serviceName: atlas
    clientId: atlas
    secure: false
    environment: stage
    connections: 10
    idleTimeOutSeconds: 30


chimeraLiteConfig:
  chimeraClientConfig:
    namespace: phonepe
    logLevel: BASIC
    httpClientConfiguration:
      clientId: Chimera
      usingRegistry: true
      serviceComponentId: CHIMERA_CONSUMER_CIG
      environment: prod
      connections: 10
      idleTimeOutSeconds: 30
  teamName: ACE
  usecaseKeyMap:
    STATIC_FILTERS: staticFilters
    STATIC_FILTERS_V2: staticFiltersV2
    SHRESHTH: gladius_OlympusMissingAPIAuthEventEnabled

commentCreationLimitPerDay: 5

auditConfig:
  auditedFields:
    TaskInstance:
      - fieldName: "REMARKS"
        jsonPath: "$['instanceMeta']['taskMetaAsMap']['REMARK']"
      - fieldName: "RESCHEDULED_AT"
        jsonPath: "$['rescheduledAt']"
      - fieldName: "LEAD_INTENT"
        jsonPath: "$['instanceMeta']['taskMetaAsMap']['LEAD_INTENT']"
      - fieldName: "CURRENT_STATE"
        jsonPath: "$['curState']"
        conditions:
          eligibleValues: ["CREATED", "DELETED", "COMPLETED", "EXPIRED"]
    StoredCommentsOnTask:
      - fieldName: "COMMENT"
        jsonPath: "$['content']"

attributeInfo:
  Points:
    icon : https://imgstatic.phonepe.com/images/ace/assets/64/64/star.png
    unit : Pts
  Distance:
    icon : https://imgstatic.phonepe.com/images/ace/assets/64/64/location.png
    unit : kms
  StartDueDate:
    icon: https://imgstatic.phonepe.com/images/ace/assets/64/64/event_later.png
    unit:
  CompletedOn:
    icon: https://imgstatic.phonepe.com/images/ace/assets/64/64/location.png
    unit:


auditableTaskActionIds:
  - PHOTO_QR_REQUEST_GENERATION


discoveryViewRestrictionConfig:
  sectorProfileTags:
  - EXCLUSIVE_LENDING
  - DBR_UPI_MANDATE
  - RDD_UPI_MANDATE
  chimeraKey: gladius_restrictions_config_v3

taskTypes:
  - SS_DEPLOYMENTS
  - VALID_SS_DEPLOYMENTS,
  - EDC_DEPLOYMENTS,
  - VALID_EDC_DEPLOYMENTS,
  - UNKNOWN

leadCreationConfig:
  leadTaskConfigs:
    - taskType: USER_CREATED_SS_DEPLOYMENT
      displayText: Smart Speaker Deployment
      definitionId: TD2309141458517097843563
      campaignId: DEFAULT
    - taskType: USER_CREATED_EDC_DEPLOYMENT
      displayText: EDC Order Closure
      definitionId: TD2211180220455327912339
      campaignId: DEFAULT
    - taskType: USER_CREATED_EDC_DEPLOYMENT
      displayText: EDC Order Placement Lead
      definitionId: TD2211180220455327912339
      campaignId: DEFAULT


esTaskIndexName: agent_tasks_v6

whitelistedDefinitions:
  []

