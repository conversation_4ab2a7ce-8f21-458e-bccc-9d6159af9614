gladiusConfig:
  namespace: phonepe
  rangerName: ${SERVICE_RANGER_NAME}
  env: stage

# Legion V1 to V2 Migration Configuration
legionMigrationConfig:
  useV2ForReads: false
  useV2ForWrites: false
  fallbackToV1OnError: true
  enableOrgLevelLogic: false
  enableV2Endpoints: false
  v2TrafficPercentage: 0
  v2TestAgentIds: []
  migratedOrgIds: []
  enableOrgRoleValidation: false
  enableOrgSectorValidation: false
  enableOrgHierarchyValidation: false

server:
  maxThreads: 256
  minThreads: 256
  adminMaxThreads: 4
  adminMinThreads: 4
  applicationConnectors:
    - type: http
      port: 9090
      acceptorThreads: 4
      selectorThreads: 1
  adminConnectors:
    - type: http
      port: 9091
      acceptorThreads: 1
      selectorThreads: 1
  applicationContextPath: /
  requestLog:
    appenders:
      - type: console
        threshold: ALL
        timeZone: IST

logging:
  level: DEBUG
  # Logger-specific levels.
  loggers:
    com.phonepe.merchant.gladius.tasks.resource: DEBUG

    # org.hibernate.SQL: DEBUG
    org.hibernate: WARN
  appenders:
    - type: console
      threshold: INFO
      timeZone: IST
      logFormat: "%(%-5level) [%date] [%thread] [%logger{0}]: %message%n"

loggingEnabled: true

serviceDiscovery:
  namespace: phonepe
  environment: stage
  zookeeper: ${ZK_CONNECTION_STRING}
  publishedHost: ***********
  publishedPort: ${PORT_8080}
  connectionRetryIntervalMillis: 5000

swagger:
  resourcePackage: com.phonepe.merchant.legion.discovery.resources,com.phonepe.merchant.legion.tasks.resources,com.phonepe.merchant.legion.external.resources,com.phonepe.merchants.rmq.helper.core.resources
  title: Gladius Service
  description: Task n Points

hystrix:
  defaultConfig:
    threadPool:
      timeout: 15000
      concurrency: 16

eventIngestor:
  farmId: NB6
  clientId: EventIngestorClient
  usingZookeeper: false
  host: events.nixy.stg-drove.phonepe.nb6
  port: 80
  environment: stage
  serviceName: dp-ingestion-api
  namespace: phonepe
  clientType: queued
  queuePath: /var/tmp

shards:
  shards:
    - driverClass: org.mariadb.jdbc.Driver
      user: ${DB_USER}
      password: ${DB_PASSWORD}
      url: ${SHARD1_CONNECTION_STRING}
      properties:
        charSet: UTF-8
        hibernate.dialect: org.hibernate.dialect.MySQLDialect
        hibernate.envers.autoRegisterListeners: false
        # hibernate.hbm2ddl.auto: update
        hibernate.show_sql: true
      maxWaitForConnection: 1s
      validationQuery: "/* MyApplication Health Check */ SELECT 1"
      minSize: 8
      maxSize: 8
      initialSize: 8
      logAbandonedConnections: true
      logValidationErrors: true
      checkConnectionWhileIdle: true
      checkConnectionOnConnect: true
      validationQueryTimeout: 1s
      removeAbandoned: true
      evictionInterval: 30s
      minIdleTime: 1m


    - driverClass: org.mariadb.jdbc.Driver
      user: ${DB_USER}
      password: ${DB_PASSWORD}
      url: ${SHARD2_CONNECTION_STRING}
      properties:
        charSet: UTF-8
        hibernate.dialect: org.hibernate.dialect.MySQLDialect
        hibernate.envers.autoRegisterListeners: false
        # hibernate.hbm2ddl.auto: update
        hibernate.show_sql: true
      maxWaitForConnection: 1s
      validationQuery: "/* MyApplication Health Check */ SELECT 1"
      minSize: 8
      maxSize: 8
      initialSize: 8
      logAbandonedConnections: true
      logValidationErrors: true
      checkConnectionWhileIdle: true
      checkConnectionOnConnect: true
      validationQueryTimeout: 1s
      removeAbandoned: true
      evictionInterval: 30s
      minIdleTime: 1m

gandalfConfig:
  secureCookies: true
  httpConfig:
    clientId: GandalfClient
    usingZookeeper: true
    host: gandalf.traefik.stg.phonepe.com
    port: 80
    serviceName: gandalf
    environment: stage
    connections: 10
    idleTimeOutSeconds: 30
  authConfig:
    clientId: gladius
    clientKey:  1234
    namespace:  gladius
  permissionCacheConfig:
    ttlSeconds: 900
    enabled: true

primerBundleConfiguration:
  enabled: true
  authTypesEnabled:
    CONFIG: false
    ANNOTATION: true
  absentTokenStatus: UNAUTHORIZED
  endpoint:
    clientId: primer
    type: simple
    host: primer.nixy.stg-drove.phonepe.nb6
    port: 80
  cacheExpiry: 60
  cacheMaxSize: 100000
  clockSkew: 60
  prefix: Bearer
  privateKey: TwhjV5ujkvb41frpmqCve7ZfhqwSDMqOXe01DeDIsb2xCrW4bwfFnax9bi2uC9Kn

validationConfig:
  headerValidationConfig:
    validateRelativePathValue: false
    headersVsForbiddenRegexList:
      X-DEVICE-FINGERPRINT:
        - \+.\/
        - \.+\\
  pathValidationConfig:
    validateHttpRelativePath: true
    forbiddenRegexList:
      - \.+\/
      - \.+\\

esConfig:
  embedded: false
  maxConnTotal: 50
  maxConnPerRoute: 20
  cluster: stg-tekes7xx
  replicas: 1
  shards: 5
  port: 9300
#  userName: {{ES_USER}}
#  password: {{ES_PASSWORD}}
  userName: "root"
  password: "root"
  secure: false
  hosts:
    - stg-es709.phonepe.nb6

actorRmqConfig:
  brokers:
    - host: stg-rmq101.phonepe.nb6
      port: 5671
    - host: stg-rmq102.phonepe.nb6
      port: 5671
    - host: stg-rmq103.phonepe.nb6
      port: 5671
  threadPoolSize: 10
  userName: admin
  password: admin
  secure: true

agentActionsConfig:
  config:
    AGENT_TODO:
      points: 12

actorConfigs:

  TASK_VERIFICATION:
    exchange: gladius_task_verification
    delayed: false
    prefix: gladius.task.stage
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_EXPONENTIAL_BACKOFF
      maxAttempts: 6
      multipier: 50
      maxTimeBetweenRetries: 30s

  TASK_ES_UPDATE_RETRY:
    exchange: gladius_task_es_update_retry
    delayed: false
    prefix: gladius.task.stage
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_EXPONENTIAL_BACKOFF
      maxAttempts: 6
      multipier: 50
      maxTimeBetweenRetries: 30s

  TASK_SCHEDULE_VERIFICATION:
    exchange: gladius_task_schedule_verification
    delayed: false
    prefix: gladius.task.stage
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_EXPONENTIAL_BACKOFF
      maxAttempts: 6
      multipier: 50
      maxTimeBetweenRetries: 30s

  TASK_ES_CHANGE_EVENT:
    exchange: gladius_task_es_change_event
    delayed: false
    prefix: gladius.task.stage
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_EXPONENTIAL_BACKOFF
      maxAttempts: 6
      multipier: 50
      maxTimeBetweenRetries: 30s

  QC_TASK_CREATE_EVENT:
    exchange: gladius_qc_task_create_event
    delayed: false
    prefix: gladius.task.stage
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_EXPONENTIAL_BACKOFF
      maxAttempts: 6
      multipier: 50
      maxTimeBetweenRetries: 30s

  TASK_CREATION_FROM_EVENT:
    exchange: gladius_create_task_from_event
    delayed: false
    prefix: gladius.task.stage
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_EXPONENTIAL_BACKOFF
      maxAttempts: 6
      multipier: 50
      maxTimeBetweenRetries: 30s

  TASK_RECREATION:
    exchange: gladius_recreate_task
    delayed: false
    prefix: gladius.task.stage
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_EXPONENTIAL_BACKOFF
      maxAttempts: 3
      multipier: 50
      maxTimeBetweenRetries: 30s

  BULK_TASK_SECTOR_UPDATE:
    exchange: bulk_task_sector_update
    delayed: false
    prefix: gladius.task.stage
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_EXPONENTIAL_BACKOFF
      maxAttempts: 1
      multipier: 50
      maxTimeBetweenRetries: 30s

  CLIENT_TASK_CREATE_AND_ASSIGN:
    exchange: client_task_create_and_assign
    delayed: false
    prefix: gladius.task.stage
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_EXPONENTIAL_BACKOFF
      maxAttempts: 1
      multipier: 50
      maxTimeBetweenRetries: 30s

  CLIENT_TASK_MANUAL_VERIFICATION:
    exchange: client_task_manual_verification
    delayed: false
    prefix: gladius.task.stage
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_EXPONENTIAL_BACKOFF
      maxAttempts: 1
      multipier: 50
      maxTimeBetweenRetries: 30s

  CLIENT_TASK_DELETE:
    exchange: client_task_delete
    delayed: false
    prefix: gladius.task.stage
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_EXPONENTIAL_BACKOFF
      maxAttempts: 1
      multipier: 50
      maxTimeBetweenRetries: 30s
  HOTSPOT_SYNC:
    exchange: hotspot_sync
    delayed: false
    prefix: gladius.task.stage
    concurrency: 3
    prefetchCount: 1
    retryConfig:
      type: COUNT_LIMITED_EXPONENTIAL_BACKOFF
      maxAttempts: 3
      multipier: 50
      maxTimeBetweenRetries: 30s

entityStoreConfigs:
  MERCHANT:
    index: onboarding
    mappingType: onboardingType

caches:
  AGENT_ACTION:
    expiryInSeconds: 1800
  CAMPAIGN_QUESTIONS:
    expiryInSeconds: 3600
  TASK_FILTERS:
    expiryInSeconds: 21600
  TASK_ATTRIBUTES:
    expiryInSeconds: 21600
  TASK_DEFINITION:
    expiryInSeconds: 1800
  TASK_ACTION_IDS:
    expiryInSeconds: 1800
  FILTERCRAFT_BUILDER:
    expiryInSeconds: 1800
  GENERAL_PURPOSE:
    expiryInSeconds: 1800
  ACTION_DETAILS:
    expiryInSeconds: 1800
  HOTSPOT_CONFIG:
    expiryInSeconds: 1800
  FORM_CONFIG:
    expiryInSeconds: 3600
  ALL_TASK_DEFINITION:
    expiryInSeconds: 25200
  TASK_DEFINITION_TYPE_MAPPING:
    expiryInSeconds: 21600

storeCheckInConfig:
  diffInCheckInMillisSecs: 8640000

entitySourceMetaMapping:
  STORE:
    source: storeDetails
  MERCHANT:
    source: merchantDetails

eventIngestionEnabled: true

docstoreService:
  clientId: docstore
  usingZookeeper: false
  serviceName: docstore
  environment: stage
  host: stg-docstore.phonepe.nb6
  port: 80
  connections: 10
  idleTimeOutSeconds: 30
  connectTimeoutMs: 10000
  opTimeoutMs: 10000

conduitClientConfig:
  host: http://conduit-service.traefik.stg.phonepe.nb6
  port: 80
  jwtToken: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

docstoreFolderConfig:
  folderInfo:
    TASK_REPORT:
      folderName: "TnP reporting"
      folderId: "FC4wLnoB-p02LmDCB8Jj"

clientQcTaskConfig:
  qcTaskCampaignId: "QC_TASK_CAMPAIGN"
  mapping:
    TD2203041531320690338197:
      taskDefinitionId: "TD2202211318289510338750"
      description: "MCC QC task"
    TD2108201102244889902591:
      taskDefinitionId: "TD2108191254201739902296"
      description: "1MG QC task"
    TD2108241807216067123293:
      taskDefinitionId: "TD2108191254201739902296"
      description: "Oye Rickshaw QC task"
    TD2108241821449677123583:
      taskDefinitionId: "TD2108191254201739902296"
      description: "Instakart QC task"
    TD2108311338235376971080:
      taskDefinitionId: "TD2109021320464400108245"
      description: "Ninjakart QC task"
    TD2109131317339234280773:
      taskDefinitionId: "TD2108191254201739902296"
      description: "Rapido QC task"
    TD2109021946123775850158:
      taskDefinitionId: "TD2109022010204845850982"
      description: "Dummy User Onboarding QC Task"
    TD2109291100066444587808:
      taskDefinitionId: "TD2108191254201739902296"
      description: "Aarzoo Kyc Task"
    TD2109301517240585137773:
      taskDefinitionId: "TD2108191254201739902296"
      description: "Metro form test"
    TD2109301519000515137404:
      taskDefinitionId: "TD2109021320464400108245"
      description: "B2B form test"
    TD2109301518222605137836:
      taskDefinitionId: "TD2109022010204845850982"
      description: "User form test"
    TD2110181050130279087476:
      taskDefinitionId: "TD2108191254201739902296"
      description: "Vyapar QC task"
    TD2110271202168441607918:
      taskDefinitionId: "TD2108191254201739902296"
      description: "Zunroof QC task"
    TD2110271648473389441106:
      taskDefinitionId: "TD2108191254201739902296"
      description: "Shopsy QC task"

legionClientConfig:
  httpConfig:
    clientId: LegionClient
    serviceName: legion
    environment: stage
    usingZookeeper: false
    host: legion.nixy.stg-drove.phonepe.nb6
    port: 80
    connections: 10
    idleTimeOutSeconds: 30

reporterConfig:
  prefix: phonepe.stage.gladius
  pollingInterval: 30
  attributes:
  cluster: merchant-offline-riemann
  metricServiceConfig:
    namespace: phonepe
    logLevel: BASIC
    zookeeper: ${ZK_CONNECTION_STRING}
    httpConfiguration:
      clientId: MetricsIngestion
      usingZookeeper: true
      host: metricingestion.nixy.stg-drove.phonepe.nb6
      serviceName: metricingestion
      environment: stage
      connections: 10
      idleTimeOutSeconds: 1
  farm: nb6

rangerHubConfiguration:
  type: HTTP
  namespace: phonepe
  nodeRefreshTimeMs: 5000
  hubHost: rangerhttp.nixy.stg-drove.phonepe.nb6
  port: 80
  secure: false
  registry:
    scheme: HTTPS
    host: zeus.nixy.stg-drove.phonepe.nb6
    port: 443
  services:
    - clientId: IntelService
      usingDiscovery: true
      usingRegistry: false
      serviceName: intel
      environment: stage
      host: intel.nixy.stg-drove.phonepe.nb6
      port: 80
      connections: 10
      idleTimeOutSeconds: 30
    - clientId: OdinService
      usingDiscovery: false
      usingRegistry: false
      serviceName: odin
      environment: stage
      host: odin.nixy.stg-drove.phonepe.nb6
      port: 80
      connections: 10
      idleTimeOutSeconds: 30
    - clientId: HermodService
      usingDiscovery: true
      usingRegistry: false
      serviceName: hermod
      environment: stage
      host: hermod.nixy.stg-drove.phonepe.nb6
      port: 80
      connections: 10
      idleTimeOutSeconds: 30
    - clientId: FortunaService
      usingDiscovery: true
      usingRegistry: false
      serviceName: fortuna
      environment: stage
      host: fortuna.traefik.stg.phonepe.nb6
      port: 80
      connections: 10
      idleTimeOutSeconds: 30
    - clientId: ParadoxService
      usingDiscovery: true
      usingRegistry: false
      serviceName: paradox
      environment: stage
      host: paradox.nixy.stg-drove.phonepe.nb6
      port: 80
      connections: 10
      idleTimeOutSeconds: 30
    - clientId: LegionService
      usingDiscovery: true
      usingRegistry: false
      serviceName: legion
      environment: stage
      host: legion.nixy.stg-drove.phonepe.nb6
      port: 80
      connections: 10
      idleTimeOutSeconds: 5
    - clientId: BrickbatService
      usingDiscovery: true
      usingRegistry: false
      serviceName: brickbat
      environment: stage
      host: brickbat.traefik.stg.phonepe.nb6
      port: 80
      connections: 10
      idleTimeOutSeconds: 30
    - clientId: FoxtrotService
      usingDiscovery: true
      usingRegistry: false
      serviceName: moses
      environment: stage
      host: moses.nixy.stg-drove.phonepe.nb6
      port: 80
      connections: 10
      idleTimeOutSeconds: 30
    - clientId: ClockWorkService
      usingDiscovery: true
      usingRegistry: false
      serviceName: clockwork-server
      environment: stage
      host: clockwork.traefik.stg.phonepe.nb6
      port: 80
      connections: 10
      idleTimeOutSeconds: 30
    - clientId: MerchantOnboardingService
      usingRegistry: true
      serviceComponentId: MERCHANT_ONBOARDING_PHONEPE
      environment: stage
      connections: 10
      idleTimeOutSeconds: 30
    - clientId: MerchantService
      usingDiscovery: true
      usingRegistry: false
      serviceName: merchant-service
      environment: stage
      host: merchant-service.nixy.stg-drove.phonepe.nb6
      port: 80
      connections: 10
      idleTimeOutSeconds: 30
    - clientId: TmsService
      usingZookeeper: false
      serviceName: tmsservice
      environment: stage
      host: tmsservice.nixy.stg-drove.phonepe.nb6
      port: 80
      connections: 10
      idleTimeOutSeconds: 30
    - clientId: AtlasService
      usingDiscovery: true
      usingRegistry: false
      serviceName: atlas
      environment: stage
      host: atlas.nixy.stg-drove.phonepe.nb6
      port: 80
      connections: 10
      idleTimeOutSeconds: 30
    - clientId: ScoutService
      usingDiscovery: true
      usingRegistry: false
      serviceName: scout
      environment: stage
      host: scout.nixy.stg-drove.phonepe.nb6
      port: 80
      connections: 10
      idleTimeOutSeconds: 30
    - clientId: KillswitchService
      usingRegistry: true
      serviceComponentId: KILLSWITCH
      environment: stage
      connections: 10
      idleTimeOutSeconds: 30
    - clientId: GeminiService
      usingZookeeper: true
      usingRegistry: false
      serviceName: gemini
      environment: stage
      host: gemini.nixy.stg-drove.phonepe.nb6
      port: 80
      connections: 10
      idleTimeOutSeconds: 30

  farm: nb6                 #inject FARM_ID as env variable from marathon/rosey

miscellaneous:
  maxGeoSortDistance: 50
  rescheduleOffsetInDays: 10
  maxPossibleRescheduleOffsetInDays: 14
  formSubmitLocationRadiusInMeters: 100

reportPeriodFilters:
  INDIVIDUAL:
    - key: TODAY
      displayText: "Today"
    - key: YESTERDAY
      displayText: "Yesterday"
    - key: THIS_WEEK
      displayText: "This Week"
    - key: THIS_MONTH
      displayText: "This Month"
    - key: LAST_MONTH
      displayText: "Previous Month"

  TEAM:
    - key: TODAY
      displayText: "Today"
    - key: YESTERDAY
      displayText: "Yesterday"
    - key: THIS_WEEK
      displayText: "This Week"
    - key: THIS_MONTH
      displayText: "This Month"
    - key: LAST_MONTH
      displayText: "Previous Month"

formConfigs:
  NOC:
    -  merchantBusinessUnits:
       agentRoles:
         - AGENT
         - FREELANCER
         - DISTRIBUTOR
         - DFOS
         - RDD
         - DDP_FOS
         - DDP
       formConfig:
         url: https://surveys-stg.phonepe.com/v1/campaign/C2111171154383026316520/index.html
         locationValidation: false
         radius:
         feedbackAuditConfigured: false

  STORE_SURVEY:
    - merchantBusinessUnits:
      agentRoles:
      formConfig:
        url: https://surveys-stg.phonepe.com/v1/campaign/C2404031356228751414840/index.html
        locationValidation: true
        radius: 100
        feedbackAuditConfigured: false

    - merchantBusinessUnits:
        - ENTERPRISE
        - NKA
        - LKA
        - TRANSIT_AND_GOVT
        - OFFLINE_AGGREGATOR
        - ONLINE_AGGREGATOR
      agentRoles:
      formConfig:
        url: https://surveys-stg.phonepe.com/v1/campaign/C2404031356228751414840/index.html
        locationValidation: true
        radius: 100
        feedbackAuditConfigured: false

  STORE_AUDIT:
    - merchantBusinessUnits:
      agentRoles:
        - TL
        - TSM
        - ASM
        - SH
        - ZSM
        - NSH
        - RH
        - BM
      formConfig:
        url: https://surveys-stg.phonepe.com/v1/campaign/C2111171757578108420174/index.html
        locationValidation: true
        radius: 100
        feedbackAuditConfigured: false

  EDC_MERCHANT_FORM_TD:
    - merchantBusinessUnits:
      agentRoles:
      formConfig:
        url: https://surveys-stg.phonepe.com/v2/campaign/index.html?campaignId=C2506161823028110656484
        locationValidation: false
        radius:
        feedbackAuditConfigured: true

  SS_MERCHANT_FORM_TD:
    - merchantBusinessUnits:
      agentRoles:
      formConfig:
        url: https://surveys-stg.phonepe.com/v2/campaign/index.html?campaignId=C2506271305458255990396
        locationValidation: false
        radius:
        feedbackAuditConfigured: true

  LENDING_MERCHANT_FORM_TD:
    - merchantBusinessUnits:
      agentRoles:
      formConfig:
        url:
        locationValidation: false
        radius:
        feedbackAuditConfigured: false

  AQR_CHECKLIST:
    - merchantBusinessUnits:
      agentRoles:
      formConfig:
        url: https://surveys-stg.phonepe.com/v1/campaign/C2110181539497818607978/index.html
        locationValidation: false
        radius:
        feedbackAuditConfigured: false

  NO_COMPETITION:
    - merchantBusinessUnits:
      agentRoles:
      formConfig:
        url: https://surveys-stg.phonepe.com/v1/campaign/C2111171150398226316481/index.html
        locationValidation: false
        radius:
        feedbackAuditConfigured: false

  SB_DEPLOY:
    - merchantBusinessUnits:
      agentRoles:
      formConfig:
        url: https://surveys-stg.phonepe.com/v1/campaign/C2505061613202480388066/index.html
        locationValidation: false
        radius:
        feedbackAuditConfigured: false

olympusIMClientConfig:
  httpConfig:
    clientId: olympus
    usingZookeeper: false
    host: olympus-im-stage.phonepe.com
    port: 443
    serviceName: olympusIM
    environment: stage
    connections: 10
    idleTimeOutSeconds: 30
    secure: true
  authConfig:
    componentId: GLADIUS
    componentInstanceId: GLADIUS_NB6
    clientId: GLADIUS_NB6
    clientKey: ca6c6137-7dce-4fb1-8e45-884c806aecad
  publicEndpoint: http://localhost:10000
  authEndpoint: https://olympus-im-stage.phonepe.com
  resourcePrefix: /olympus/im

atlasClientConfig:
  httpConfig:
    usingZookeeper: true
    serviceName: atlas
    clientId: atlas
    secure: false
    environment: stage
    connections: 10
    idleTimeOutSeconds: 30


userTaskCreationConfigs:
  taskCreationParametersList:
    - taskType: "USER_CREATED_SS_DEPLOYMENT"
      definitionId: TD2309141458517097843563
      campaignId: DEFAULT
    - taskType: "USER_CREATED_EDC_DEPLOYMENT"
      definitionId: TD2211180220455327912339
      campaignId: DEFAULT

eventBasedTaskCreationConfig:
  OQC_TASK:
    taskDefinitionId: TD2202180835107760921755
    campaignId: OQC_TASK_CAMPAIGN


chimeraLiteConfig:
  chimeraClientConfig:
    namespace: phonepe
    logLevel: BASIC
    httpClientConfiguration:
      clientId: Chimera
      usingZookeeper: false
      serviceName: Chimera
      environment: stage
      host: chimera.nixy.stg-drove.phonepe.nb6
      port: 80
      connections: 10
      idleTimeOutSeconds: 30
  teamName: ACE
  usecaseKeyMap:
    STATIC_FILTERS: staticFilters
    STATIC_FILTERS_V2: staticFiltersV2

commentCreationLimitPerDay: 100

auditableTaskActionIds:
  - PHONEPE_MERCHANT_LENDING
  - PITCH_PHONEPE_LOAN_BY_CALLING
  - EDC_ORDER_PLACEMENT
  - DEPLOY_PHONEPE_SMARTSPEAKER_PB_SELF_ORDER
  - DEPLOY_PHONEPE_SMARTSPEAKER
  - PP_STORE_CHECK_IN
  - EDC_REPLACEMENT
auditConfig:
  auditedFields:
    TaskInstance:
      - fieldName: "REMARKS"
        jsonPath: "$['instanceMeta']['taskMetaAsMap']['REMARK']"
      - fieldName: "RESCHEDULED_AT"
        jsonPath: "$['rescheduledAt']"
      - fieldName: "LEAD_INTENT"
        jsonPath: "$['instanceMeta']['taskMetaAsMap']['LEAD_INTENT']"
      - fieldName: "CURRENT_STATE"
        jsonPath: "$['curState']"
        conditions:
          eligibleValues: ["CREATED", "DELETED", "COMPLETED", "EXPIRED"]
    StoredCommentsOnTask:
      - fieldName: "COMMENT"
        jsonPath: "$['content']"

attributeInfo:
  Points:
    icon : https://imgstatic.phonepe.com/images/ace/assets/64/64/star.png
    unit : Pts
  Distance:
    icon : https://imgstatic.phonepe.com/images/ace/assets/64/64/location.png
    unit : kms
  StartDueDate:
    icon: https://imgstatic.phonepe.com/images/ace/assets/64/64/event_later.png
    unit:
  CompletedOn:
    icon: https://imgstatic.phonepe.com/images/ace/48/48/Check.png
    unit:

restrictionContextSectorProfileTags:
  - EXCLUSIVE_LENDING
  - DBR_UPI_MANDATE
  - RDD_UPI_MANDATE

discoveryViewRestrictionConfig:
  sectorProfileTags:
    - EXCLUSIVE_LENDING
    - DBR_UPI_MANDATE
    - RDD_UPI_MANDATE
  chimeraKey: gladius_restrictions_config_v4

esTaskIndexName: agent_tasks_v6