<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>

    <parent>
        <groupId>com.phonepe</groupId>
        <artifactId>pp-parent-pom</artifactId>
        <version>0.115</version>
    </parent>

    <groupId>com.phonepe.merchant</groupId>
    <artifactId>gladius</artifactId>
    <version>1.0.375-SNAPSHOT</version>

    <modules>
        <module>gladius-models</module>
        <module>gladius-core</module>
        <module>gladius-server</module>
        <module>gladius-tasks</module>
        <module>gladius-external</module>
        <module>gladius-hotspots</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <dropwizard.oor.version>2.0.24-1</dropwizard.oor.version>
        <dropwizard.swagger.version>2.0.28-3</dropwizard.swagger.version>
        <http.client.version>5.0.3</http.client.version>
        <hibernate.envers.version>5.6.1.Final</hibernate.envers.version>
        <validation.api.version>2.0.1.Final</validation.api.version>
        <okhttp.version>4.11.0-PPE</okhttp.version>
        <killswitch.version>1.0.76</killswitch.version>
        <api-killer-version>1.26</api-killer-version>
        <rmq-helper.version>1.3</rmq-helper.version>
        <zeus.version>2.1.3</zeus.version>

        <!-- Models -->
        <phonepe-model.version>2.1.395</phonepe-model.version>
        <upi-client-model.version>0.2.77</upi-client-model.version>
        <odin.models.version>1.63</odin.models.version>
        <legion.models.version>1.0.557</legion.models.version>
        <gemini.models.version>1.83</gemini.models.version>

        <!--Internal Client-->
        <event.ingestion.client.version>2.1.0-12</event.ingestion.client.version>
        <function.metric.version>1.0.5</function.metric.version>
        <gandalf-client.version>2.0.40</gandalf-client.version>
        <validation-bundle.version>********</validation-bundle.version>
        <jersey-media-multipart.version>2.23.1</jersey-media-multipart.version>
        <commons-lang3.version>3.9</commons-lang3.version>
        <validation-api.version>2.0.1.Final</validation-api.version>
        <sonar-break-maven-plugin-version>1.2.7</sonar-break-maven-plugin-version>
        <legion.client.version>1.0.331</legion.client.version>

        <!--External libraries-->
        <lombok.version>1.18.22</lombok.version>
        <guiceVersion>5.4.2</guiceVersion>
        <swagger.version>1.0.0-1</swagger.version>
        <jackson-core.version>2.13.1</jackson-core.version>
        <aspectj.rt.version>1.9.8</aspectj.rt.version>
        <aspectj.runtime.version>1.9.8</aspectj.runtime.version>
        <opencsv.version>4.1</opencsv.version>
        <jaxb-api.version>2.3.0</jaxb-api.version>
        <metric-ingestion.version>1.134</metric-ingestion.version>
        <olympus-version>1.2.28</olympus-version>
        <filtercraft.version>0.0.5</filtercraft.version>
        <merchant-service.model.version>1.114</merchant-service.model.version>
        <elasticsearch-geo.version>8.14.3</elasticsearch-geo.version>
        <google.geometry.version>2.0.0</google.geometry.version>

        <!-- Chimera-->
        <chimera.version>2.268</chimera.version>

        <!--Hystrix-->
        <hystrix-function-wrapper.version>1.1.9</hystrix-function-wrapper.version>
        <hystrix.dropwizard.bundle.version>1.0.2</hystrix.dropwizard.bundle.version>
        <hystrix.configurator.version>0.0.9</hystrix.configurator.version>

        <!--Plugins-->
        <maven-deploy-version>2.8.2</maven-deploy-version>
        <maven-compiler-version>3.10.1</maven-compiler-version>
        <maven-surefire-version>2.22.0</maven-surefire-version>
        <aspectj-maven-plugin.version>1.14.0</aspectj-maven-plugin.version>

        <!--Jacoco properties-->
        <jacoco.version>0.8.8</jacoco.version>
        <dropwizard.requestInfo.bundle.version>2.0.23-7</dropwizard.requestInfo.bundle.version>
        <jacoco.reportPath>${project.basedir}/../target/jacoco.exec</jacoco.reportPath>


        <!-- Dropwizard -->
        <dropwizard.version>2.0.28</dropwizard.version>
        <rosey.version>2.0.87</rosey.version>
        <db-sharding-bundle.version>2.0.28-2</db-sharding-bundle.version>
        <dropwizard.jmxmp.version>2.0.29-1</dropwizard.jmxmp.version>
        <dropwizard-service-discovery.version>2.0.28-1</dropwizard-service-discovery.version>
        <dropwizard-testing-framework-version>2.0.23</dropwizard-testing-framework-version>
        <oor.version>2.0.24-1</oor.version>
        <dropwizard.service.discovery.bundle.version>2.0.28-1</dropwizard.service.discovery.bundle.version>

        <foxtrot.version>8.9.0.52</foxtrot.version>

        <!--Elastic search properties-->
        <elasticsearch.version>7.17.8</elasticsearch.version>

        <dropwizard.rabbitmq.actors.version>2.0.28</dropwizard.rabbitmq.actors.version>
        <rmq.client.version>5.12.0</rmq.client.version>

        <!-- Test -->
        <hibernate.validator.version>6.0.17.Final</hibernate.validator.version>
        <junit.version>5.6.2</junit.version>
        <h2.version>1.4.200</h2.version>
        <testcontainers.version>1.9.1</testcontainers.version>
        <mockito.version>4.3.1</mockito.version>


        <maven.release.ignore.snapshots>true</maven.release.ignore.snapshots>
        <aspectjrt.version>1.9.8</aspectjrt.version>
        <maria-java-client-version>3.0.4</maria-java-client-version>
        <api.metrics.version>0.0.17</api.metrics.version>
        <maven.javadoc.skip>true</maven.javadoc.skip>
        <maven.deploy.skip>false</maven.deploy.skip>
        <ranger.version>1.1.1</ranger.version>

        <!-- Sonar properties -->
        <sonar.sources>src/main</sonar.sources>
        <sonar.tests>src/test</sonar.tests>
        <sonar.java.coveragePlugin>jacoco</sonar.java.coveragePlugin>
        <sonar.dynamicAnalysis>reuseReports</sonar.dynamicAnalysis>
        <sonar.jacoco.reportPaths>${project.basedir}/target/jacoco.exec</sonar.jacoco.reportPaths>
        <sonar.jacoco.itReportPath>${project.basedir}/target/jacoco-it.exec</sonar.jacoco.itReportPath>
        <sonar.language>java</sonar.language>
        <sonar.coverage.exclusions>
            **com/phonepe/merchant/legion/server/App.java,
            **com/phonepe/merchant/legion/core/entitystore/ESConnection.java,
            **com/phonepe/merchant/legion/core/repository/impl/ESRepositoryImpl.java,
            **com/phonepe/merchant/legion/tasks/utils/BulkSectorUpdater.java,
            **com/phonepe/merchant/legion/tasks/services/impl/TaskHousekeepingServiceImpl.java,
            **com/phonepe/merchant/legion/tasks/utils/TaskEsUtils.java,
            **com/phonepe/merchant/legion/tasks/search/query/geopolygon/RemappingGeoPolygonQueryBuilder.java
        </sonar.coverage.exclusions>
        <sonar.exclusions>
            **com/phonepe/merchant/legion/core/entitystore/ESConnection.java,
            **com/phonepe/merchant/legion/core/repository/impl/ESRepositoryImpl.java,
            **com/phonepe/merchant/legion/tasks/utils/BulkSectorUpdater.java,
            **com/phonepe/merchant/legion/tasks/services/impl/TaskHousekeepingServiceImpl.java,
            **com/phonepe/merchant/legion/tasks/utils/TaskEsUtils.java,
            **com/phonepe/merchant/legion/tasks/search/query/geopolygon/RemappingGeoPolygonQueryBuilder.java
        </sonar.exclusions>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.phonepe.platform.http.v2</groupId>
            <artifactId>http-common</artifactId>
            <version>${http.client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.phonepe.platform</groupId>
                    <artifactId>zeus-models</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.appform.ranger</groupId>
                    <artifactId>ranger-server-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.phonepe.platform.http.v2</groupId>
            <artifactId>http-client-all</artifactId>
            <version>${http.client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.phonepe.dataplatform</groupId>
                    <artifactId>yggdrasil-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.phonepe.platform</groupId>
            <artifactId>zeus-models</artifactId>
            <version>${zeus.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>io.swagger.core.v3</groupId>
                    <artifactId>swagger-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.phonepe.platform</groupId>
            <artifactId>zeus-provider</artifactId>
            <version>${zeus.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>io.appform.ranger</groupId>
                    <artifactId>ranger-server-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.phonepe.platform</groupId>
            <artifactId>zeus-router-registry</artifactId>
            <version>${zeus.version}</version>
        </dependency>
        <dependency>
            <artifactId>olympus-im-client</artifactId>
            <groupId>com.phonepe.olympus-im</groupId>
            <version>${olympus-version}</version>
            <exclusions>
                <exclusion>
                    <groupId>feign.ranger</groupId>
                    <artifactId>feign-ranger</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.phonepe.platform</groupId>
                    <artifactId>zeus-models</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.raven.dropwizard</groupId>
                    <artifactId>dropwizard-primer</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.smoketurner</groupId>
                    <artifactId>dropwizard-swagger</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.swagger</groupId>
                    <artifactId>swagger-jersey2-jaxrs</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.swagger</groupId>
                    <artifactId>swagger-annotations</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.dropwizard</groupId>
            <artifactId>dropwizard-core</artifactId>
            <version>${dropwizard.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>${jackson-core.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson-core.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>${jackson-core.version}</version>
        </dependency>
        <dependency>
            <groupId>in.vectorpro.dropwizard</groupId>
            <artifactId>dropwizard-swagger</artifactId>
            <version>${dropwizard.swagger.version}</version>
        </dependency>
        <dependency>
            <groupId>com.phonepe.platform</groupId>
            <artifactId>requestinfo-bundle</artifactId>
            <version>${dropwizard.requestInfo.bundle.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>feign.ranger</groupId>
                    <artifactId>feign-ranger</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.phonepe.platform</groupId>
                    <artifactId>atlas-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.appform.functionmetrics</groupId>
            <artifactId>function-metrics</artifactId>
            <version>${function.metric.version}</version>
        </dependency>
        <dependency>
            <groupId>com.phonepe.merchant</groupId>
            <artifactId>filtercraft-client</artifactId>
            <version>${filtercraft.version}</version>
        </dependency>
        <dependency>
            <groupId>com.phonepe.platform</groupId>
            <artifactId>atlas-client</artifactId>
            <version>2.1.152</version>
            <exclusions>
                <exclusion>
                    <groupId>io.sgr</groupId>
                    <artifactId>s2-geometry-library-java</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.phonepe.platform</groupId>
            <artifactId>atlas-model</artifactId>
            <version>2.1.152</version>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-core</artifactId>
            <version>11.8</version>
        </dependency>
        <dependency>
            <groupId>com.google.geometry</groupId>
            <artifactId>s2-geometry</artifactId>
            <version>${google.geometry.version}</version>
        </dependency>

        <dependency>
            <groupId>io.appform.ranger</groupId>
            <artifactId>ranger-http-client</artifactId>
            <version>${ranger.version}</version>
        </dependency>

        <dependency>
            <groupId>io.appform.ranger</groupId>
            <artifactId>ranger-zk-client</artifactId>
            <version>${ranger.version}</version>
        </dependency>

        <dependency>
            <groupId>io.appform.ranger</groupId>
            <artifactId>ranger-zookeeper</artifactId>
            <version>${ranger.version}</version>
        </dependency>

        <dependency>
            <groupId>io.appform.ranger</groupId>
            <artifactId>ranger-discovery-bundle</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>junit</artifactId>
                    <groupId>junit</groupId>
                </exclusion>
            </exclusions>
            <version>${ranger.version}</version>
        </dependency>
        <dependency>
            <groupId>com.phonepe.merchants.rmq</groupId>
            <artifactId>rmq-helper-core</artifactId>
            <version>${rmq-helper.version}</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>com.phonepe.merchant</groupId>
                <artifactId>legion-client</artifactId>
                <version>${legion.client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>2.13.1</version>
            </dependency>

            <dependency>
                <groupId>io.raven.dropwizard</groupId>
                <artifactId>dropwizard-jmxmp</artifactId>
                <version>${dropwizard.jmxmp.version}</version>
            </dependency>

            <dependency>
                <groupId>com.phonepe.nexus</groupId>
                <artifactId>nexus-models</artifactId>
                <version>0.0.47</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate.validator.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.hibernate</groupId>
                        <artifactId>hibernate-validator</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-framework</artifactId>
                <version>4.2.0</version>
            </dependency>

            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-recipes</artifactId>
                <version>4.2.0</version>
            </dependency>

            <dependency>
                <groupId>com.phonepe.models</groupId>
                <artifactId>phonepe-model</artifactId>
                <version>${phonepe-model.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.phonepe.models</groupId>
                <artifactId>merchant-service-model</artifactId>
                <version>${merchant-service.model.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--UPI CLIENT MODEL-->
            <dependency>
                <groupId>com.phonepe.payments</groupId>
                <artifactId>upi-client-model</artifactId>
                <version>${upi-client-model.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjrt</artifactId>
                <version>${aspectjrt.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>com.phonepe.platform.http.v2</groupId>
                <artifactId>http-discovery</artifactId>
                <version>${http.client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.appform.ranger.discovery</groupId>
                        <artifactId>dropwizard-service-discovery</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.platform</groupId>
                <artifactId>validation-bundle</artifactId>
                <version>${validation-bundle.version}</version>
            </dependency>

            <dependency>
                <groupId>com.flipkart.foxtrot</groupId>
                <artifactId>foxtrot-common</artifactId>
                <version>${foxtrot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.appform.hbase.ds</groupId>
                        <artifactId>hbase-ds</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.hbase</groupId>
                        <artifactId>hbase-shaded-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.marathon.hazelcast.servicediscovery</groupId>
                        <artifactId>hazelcast-marathon-discovery</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.codahale.metrics</groupId>
                        <artifactId>metrics-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.phonepe.gandalf</groupId>
                        <artifactId>gandalf-models</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.raskasa.metrics</groupId>
                        <artifactId>metrics-okhttp</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.dropwizard</groupId>
                        <artifactId>dropwizard-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.dropwizard</groupId>
                        <artifactId>dropwizard-validation</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.smoketurner</groupId>
                        <artifactId>dropwizard-swagger</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.dropwizard</groupId>
                        <artifactId>dropwizard-jackson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.phonepe.platform</groupId>
                        <artifactId>event-ingestion-models</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.phonepe.frontend</groupId>
                <artifactId>chimera-lite</artifactId>
                <version>${chimera.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.phonepe.gandalf</groupId>
                        <artifactId>gandalf-models</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.google.guava</groupId>
                        <artifactId>guava</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.dropwizard</groupId>
                        <artifactId>dropwizard-metrics</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.github.openfeign</groupId>
                        <artifactId>feign-jackson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.github.openfeign</groupId>
                        <artifactId>feign-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.github.sgoertzen</groupId>
                        <artifactId>sonar-break-maven-plugin</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.phonepe.platform.http</groupId>
                        <artifactId>http-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.github.openfeign</groupId>
                        <artifactId>feign-okhttp</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.jayway.jsonpath</groupId>
                        <artifactId>json-path</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>feign.ranger</groupId>
                        <artifactId>feign-ranger</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>io.appform.dropwizard.sharding</groupId>
                <artifactId>db-sharding-bundle</artifactId>
                <version>${db-sharding-bundle.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.javassist</groupId>
                        <artifactId>javassist</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.hibernate</groupId>
                        <artifactId>hibernate-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.reflections</groupId>
                        <artifactId>reflections</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.dropwizard</groupId>
                        <artifactId>dropwizard-jersey</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.reflections</groupId>
                <artifactId>reflections</artifactId>
                <version>0.9.11</version>
                <exclusions>
                    <exclusion>
                        <artifactId>javassist</artifactId>
                        <groupId>org.javassist</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>${validation-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.h2database</groupId>
                <artifactId>h2</artifactId>
                <version>${h2.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.mariadb.jdbc</groupId>
                <artifactId>mariadb-java-client</artifactId>
                <version>${maria-java-client-version}</version>
            </dependency>

            <dependency>
                <groupId>feign.ranger</groupId>
                <artifactId>feign-ranger</artifactId>
                <version>0.1.8</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.phonepe.gandalf</groupId>
                <artifactId>gandalf-client</artifactId>
                <version>${gandalf-client.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>feign-ranger</artifactId>
                        <groupId>feign.ranger</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>olympus-im-routing</artifactId>
                        <groupId>com.phonepe.olympus-im</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>io.appform.ranger</groupId>
                <artifactId>ranger-discovery-bundle</artifactId>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>junit</artifactId>
                        <groupId>junit</groupId>
                    </exclusion>
                </exclusions>
                <version>${ranger.version}</version>
            </dependency>

            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-envers</artifactId>
                <version>5.6.1.Final</version>
            </dependency>

            <dependency>
                <groupId>ru.vyarus</groupId>
                <artifactId>dropwizard-guicey</artifactId>
                <version>${guiceVersion}</version>
            </dependency>

            <dependency>
                <groupId>com.github.phaneesh</groupId>
                <artifactId>sonar-break-maven-plugin</artifactId>
                <version>${sonar-break-maven-plugin-version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.jaxrs</groupId>
                <artifactId>jackson-jaxrs-json-provider</artifactId>
                <version>2.11.0</version>
            </dependency>

            <dependency>
                <groupId>io.raven.dropwizard</groupId>
                <artifactId>dropwizard-oor</artifactId>
                <version>${oor.version}</version>
            </dependency>

            <!-- ElasticSearch Dependencies -->
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>

            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-client</artifactId>
                <version>8.14.3</version>
            </dependency>
            <dependency>
                <groupId>co.elastic.clients</groupId>
                <artifactId>elasticsearch-java</artifactId>
                <version>8.14.3</version>
            </dependency>

            <dependency>
                <groupId>org.glassfish.jersey.media</groupId>
                <artifactId>jersey-media-multipart</artifactId>
                <version>${jersey-media-multipart.version}</version>
            </dependency>

            <dependency>
                <groupId>com.phonepe.platform</groupId>
                <artifactId>rosey-dropwizard-config</artifactId>
                <version>${rosey.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.hibernate</groupId>
                        <artifactId>hibernate-validator</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.phonepe.platform</groupId>
                <artifactId>rosey-standanlone-config</artifactId>
                <version>${rosey.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- Hystrix -->
            <dependency>
                <groupId>io.appform.core</groupId>
                <artifactId>hystrix-function-wrapper</artifactId>
                <version>${hystrix-function-wrapper.version}</version>
            </dependency>
            <dependency>
                <groupId>org.zapodot</groupId>
                <artifactId>hystrix-dropwizard-bundle</artifactId>
                <version>${hystrix.dropwizard.bundle.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.dropwizard</groupId>
                        <artifactId>dropwizard-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>in.vectorpro.dropwizard</groupId>
                        <artifactId>dropwizard-swagger</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.hystrix</groupId>
                <artifactId>hystrix-configurator</artifactId>
                <version>${hystrix.configurator.version}</version>
            </dependency>
            <!-- Hystrix -->

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>

            <dependency>
                <groupId>io.github.openfeign</groupId>
                <artifactId>feign-httpclient</artifactId>
                <version>11.0</version>
            </dependency>

            <dependency>
                <groupId>com.phonepe.dataplatform</groupId>
                <artifactId>event-ingestion-client</artifactId>
                <version>${event.ingestion.client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.hibernate</groupId>
                        <artifactId>hibernate-validator</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.phonepe.models</groupId>
                        <artifactId>phonepe-model</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.mockito</groupId>
                        <artifactId>mockito-inline</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-simple</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.raskasa.metrics</groupId>
                        <artifactId>metrics-okhttp</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.phonepe.platform.http.v2</groupId>
                        <artifactId>http-client-all</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.curator</groupId>
                        <artifactId>curator-framework</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.curator</groupId>
                        <artifactId>curator-recipes</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-databind</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.appform.ranger.discovery</groupId>
                        <artifactId>dropwizard-service-discovery-bundle</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.phonepe.platform</groupId>
                <artifactId>metric-ingestion-bundle</artifactId>
                <version>${metric-ingestion.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.github.openfeign</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.hibernate</groupId>
                        <artifactId>hibernate-validator</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>feign-ranger</artifactId>
                        <groupId>feign.ranger</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.phonepe.platform</groupId>
                        <artifactId>zeus-models</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>


            <!-- CLOCK WORK -->
            <dependency>
                <artifactId>clockwork-models</artifactId>
                <groupId>com.phonepe.platform</groupId>
                <version>2.0.34</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.google.inject</groupId>
                <artifactId>guice</artifactId>
                <version>5.1.0</version>
            </dependency>

            <dependency>
                <groupId>io.dropwizard.metrics</groupId>
                <artifactId>metrics-core</artifactId>
                <version>4.1.0-rc3</version>
            </dependency>

            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-params</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-engine</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>${mockito.version}</version>
                <scope>compile</scope>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>mariadb</artifactId>
                <version>1.15.0</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>3.4.5</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>javax.xml.bind</groupId>
                <artifactId>jaxb-api</artifactId>
                <version>${jaxb-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.opencsv</groupId>
                <artifactId>opencsv</artifactId>
                <version>${opencsv.version}</version>
            </dependency>

            <dependency>
                <groupId>com.phonepe.platform.http.server.metrics</groupId>
                <artifactId>api-metrics</artifactId>
                <version>${api.metrics.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.ws.rs</groupId>
                        <artifactId>javax.ws.rs-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.dropwizard.logback</groupId>
                        <artifactId>logback-throttling-appender</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.phonepe.merchant</groupId>
                <artifactId>legion-models</artifactId>
                <version>${legion.models.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.phonepe.gemini</groupId>
                <artifactId>gemini-model</artifactId>
                <version>${gemini.models.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- State machine -->
            <dependency>
                <groupId>io.github.fsm</groupId>
                <artifactId>fsm</artifactId>
                <version>0.0.2-1</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- TO cover later: this should be a plugin, why is it a dep? -->
            <!-- also, where is report aggregate? -->
            <dependency>
                <groupId>org.jacoco</groupId>
                <artifactId>org.jacoco.agent</artifactId>
                <classifier>runtime</classifier>
                <scope>test</scope>
                <version>0.8.3</version>
            </dependency>

            <dependency>
                <groupId>io.appform.dropwizard.actors</groupId>
                <artifactId>dropwizard-rabbitmq-actors</artifactId>
                <version>${dropwizard.rabbitmq.actors.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.httpcomponents</groupId>
                        <artifactId>httpclient</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-core</artifactId>
                <version>5.6.1.Final</version>
            </dependency>

            <dependency>
                <groupId>com.rabbitmq</groupId>
                <artifactId>amqp-client</artifactId>
                <version>${rmq.client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.phonepe.data.lucy</groupId>
                <artifactId>conduit-client</artifactId>
                <version>1.0.23-RELEASE</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-slf4j-impl</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.phonepe.platform</groupId>
                <artifactId>brickbat-models</artifactId>
                <version>1.2.56</version>
                <exclusions>
                    <exclusion>
                        <artifactId>*</artifactId>
                        <groupId>*</groupId>
                    </exclusion>

                    <exclusion>
                        <groupId>org.apache.hbase</groupId>
                        <artifactId>hbase-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.hibernate</groupId>
                        <artifactId>hibernate-validator</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>dropwizard-swagger</artifactId>
                        <groupId>in.vectorpro.dropwizard</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.phonepe.platform</groupId>
                <artifactId>atlas-model</artifactId>
                <version>2.1.152</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>

            <dependency>
                <groupId>com.phonepe.merchants</groupId>
                <artifactId>paradox-models</artifactId>
                <version>1.67</version>
            </dependency>

            <dependency>
                <groupId>com.phonepe.growth</groupId>
                <artifactId>neuron-client</artifactId>
                <version>1.1.26</version>
<!--                <version>1.2.33</version>-->
                <exclusions>
                    <exclusion>
                        <artifactId>*</artifactId>
                        <groupId>*</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.phonepe.platform.filters</groupId>
                <artifactId>api-killer-core</artifactId>
                <version>${api-killer-version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.phonepe.olympus-im</groupId>
                        <artifactId>olympus-im-models</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.github.ben-manes.caffeine</groupId>
                        <artifactId>caffeine</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.phonepe.platform.filters</groupId>
                <artifactId>api-killer-killswitch</artifactId>
                <version>${api-killer-version}</version>
            </dependency>

            <dependency>
                <groupId>com.phonepe.platform.killswitch</groupId>
                <artifactId>killswitch-client</artifactId>
                <version>${killswitch.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.appform.hope</groupId>
                        <artifactId>hope-lang</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.phonepe.growth</groupId>
                <artifactId>neuron-model</artifactId>
                <version>1.1.26</version>
<!--                <version>1.2.33</version>-->
                <exclusions>
                    <exclusion>
                        <artifactId>*</artifactId>
                        <groupId>*</groupId>
                    </exclusion>
                </exclusions>
            </dependency>


            <dependency>
                <groupId>com.phonepe.merchants</groupId>
                <artifactId>odin-ace-models</artifactId>
                <version>${odin.models.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.phonepe.platform</groupId>
                        <artifactId>dropwizard-requestinfo-bundle</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.appform.dropwizard.discovery</groupId>
                        <artifactId>dropwizard-service-discovery-bundle</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <profiles>
        <profile>
            <id>docker</id>
        </profile>
    </profiles>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-version}</version>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>3.4.1</version>
                <executions>
                    <execution>
                        <id>enforce-ranger-dependencies-ban</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <fail>true</fail>
                            <rules>
                                <requireMavenVersion>
                                    <version>[3.0,)</version>
                                </requireMavenVersion>
                                <requireJavaVersion>
                                    <version>1.8</version>
                                </requireJavaVersion>
                                <bannedDependencies>
                                    <excludes>
                                        <exclude>io.appform.ranger.discovery:*</exclude>
                                        <exclude>com.flipkart.ranger:*</exclude>
                                        <exclude>feign.ranger:feign-ranger</exclude>
                                    </excludes>
                                </bannedDependencies>
                                <banDuplicatePomDependencyVersions />
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>aspectj-maven-plugin</artifactId>
                <version>${aspectj-maven-plugin.version}</version>
                <dependencies>
                    <dependency>
                        <groupId>org.aspectj</groupId>
                        <artifactId>aspectjrt</artifactId>
                        <version>${aspectjrt.version}</version>
                    </dependency>
                    <dependency>
                        <groupId>org.aspectj</groupId>
                        <artifactId>aspectjtools</artifactId>
                        <version>${aspectjrt.version}</version>
                    </dependency>
                </dependencies>

                <configuration>
                    <complianceLevel>17</complianceLevel>
                    <source>17</source>
                    <target>17</target>
                    <showWeaveInfo>true</showWeaveInfo>
                    <forceAjcCompile>true</forceAjcCompile>
                    <sources />
                    <weaveDirectories>
                        <weaveDirectory>${project.build.directory}/classes</weaveDirectory>
                    </weaveDirectories>
                    <verbose>true</verbose>
                    <Xlint>ignore</Xlint>
                    <aspectLibraries>
                        <aspectLibrary>
                            <groupId>io.appform.functionmetrics</groupId>
                            <artifactId>function-metrics</artifactId>
                        </aspectLibrary>
                    </aspectLibraries>
                </configuration>
                <executions>
                    <execution>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>