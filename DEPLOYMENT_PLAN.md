# Gladius Service - Legion Org Migration Deployment Plan

## Overview

This document outlines the step-by-step deployment plan for migrating Gladius service to support Legion's new org-level structure. The plan follows the migration phases defined in the Legion revamp documentation and ensures zero-downtime migration.

## Pre-Deployment Checklist

### 1. Code Readiness
- [ ] All new services and models implemented
- [ ] Configuration files updated with migration settings
- [ ] Unit tests passing for all new components
- [ ] Integration tests validated
- [ ] Code review completed

### 2. Infrastructure Readiness
- [ ] Legion V2 APIs available and tested
- [ ] Database schema updates prepared
- [ ] Monitoring and alerting configured
- [ ] Rollback procedures documented

### 3. Testing Validation
- [ ] End-to-end testing in staging environment
- [ ] Performance testing completed
- [ ] Load testing with org-level data
- [ ] Fallback scenarios validated

## Deployment Phases

### Phase 1: Dual Writes Setup (Week 1)

#### Objectives
- Deploy new code with V2 service layer
- Enable dual writes to both V1 and V2 systems
- Maintain 100% V1 reads for stability

#### Configuration
```yaml
legionMigrationConfig:
  useV2ForReads: false
  useV2ForWrites: true
  fallbackToV1OnError: true
  enableOrgLevelLogic: false
  v2TrafficPercentage: 0
```

#### Deployment Steps
1. **Deploy Code** (2 hours)
   - Deploy Gladius service with new org-level code
   - Verify service health and basic functionality
   - Monitor error rates and performance metrics

2. **Enable Dual Writes** (1 hour)
   - Update configuration to enable V2 writes
   - Monitor write success rates to both systems
   - Validate data consistency between V1 and V2

3. **Validation** (4 hours)
   - Run smoke tests on all major workflows
   - Verify existing functionality unchanged
   - Monitor system performance and error rates

#### Success Criteria
- [ ] All existing APIs functioning normally
- [ ] Dual writes successful with <1% error rate
- [ ] No performance degradation >5%
- [ ] Zero customer-facing issues

#### Rollback Plan
- Disable V2 writes via configuration
- Revert to V1-only mode
- Monitor system recovery

### Phase 2: User Data Migration (Week 2-3)

#### Objectives
- Migrate existing user data to V2 format
- Maintain V1 reads during migration
- Validate data integrity

#### Configuration
```yaml
legionMigrationConfig:
  useV2ForReads: false
  useV2ForWrites: true
  fallbackToV1OnError: true
  enableOrgLevelLogic: false
```

#### Deployment Steps
1. **Data Migration Scripts** (8 hours)
   - Run user data migration DAGs
   - Validate migrated data integrity
   - Monitor migration progress and errors

2. **Validation** (4 hours)
   - Compare V1 and V2 data consistency
   - Run data validation scripts
   - Verify user profile completeness

#### Success Criteria
- [ ] 100% user data migrated successfully
- [ ] Data consistency validation passed
- [ ] No data loss or corruption
- [ ] Migration completed within SLA

### Phase 3: Dual Reads Enablement (Week 4)

#### Objectives
- Enable reads from V2 APIs
- Gradual traffic shifting to V2
- Maintain fallback capabilities

#### Configuration (Initial)
```yaml
legionMigrationConfig:
  useV2ForReads: true
  useV2ForWrites: true
  fallbackToV1OnError: true
  enableOrgLevelLogic: false
  v2TrafficPercentage: 10
```

#### Deployment Steps
1. **Enable V2 Reads** (2 hours)
   - Update configuration for 10% V2 traffic
   - Monitor V2 API performance and error rates
   - Validate response consistency

2. **Gradual Traffic Increase** (1 week)
   - Day 1: 10% → 25% V2 traffic
   - Day 3: 25% → 50% V2 traffic
   - Day 5: 50% → 75% V2 traffic
   - Day 7: 75% → 100% V2 traffic

3. **Monitoring and Validation** (Continuous)
   - Monitor API response times
   - Track error rates and fallback usage
   - Validate business logic correctness

#### Success Criteria
- [ ] V2 API response time <200ms (95th percentile)
- [ ] Error rate <0.1% for V2 APIs
- [ ] Successful traffic migration to 100% V2
- [ ] No functional regressions

#### Rollback Plan
- Reduce V2 traffic percentage
- Enable fallback to V1 for all traffic
- Monitor system stability

### Phase 4: Org-Level Logic Enablement (Week 5-6)

#### Objectives
- Enable org-level business logic
- Migrate users to org-based hierarchy
- Validate org-level validations

#### Configuration
```yaml
legionMigrationConfig:
  useV2ForReads: true
  useV2ForWrites: true
  enableOrgLevelLogic: true
  enableOrgRoleValidation: true
  enableOrgHierarchyValidation: true
  migratedOrgIds: ["org-1", "org-2", "org-3"]
```

#### Deployment Steps
1. **Org Migration** (1 week)
   - Migrate users to org-based hierarchy
   - Update role mappings for org context
   - Validate org-level permissions

2. **Enable Org Logic** (2 hours)
   - Enable org-level validation logic
   - Test cross-org scenarios
   - Validate hierarchical permissions

3. **Validation** (8 hours)
   - Test all org-level workflows
   - Validate role-based access control
   - Test cross-org task assignments

#### Success Criteria
- [ ] All users migrated to org structure
- [ ] Org-level validations working correctly
- [ ] Role-based permissions functioning
- [ ] Cross-org scenarios handled properly

### Phase 5: V2 Contract Migration (Week 7)

#### Objectives
- Enable V2 API endpoints
- Migrate clients to new contracts
- Deprecate V1 endpoints

#### Configuration
```yaml
legionMigrationConfig:
  useV2ForReads: true
  useV2ForWrites: true
  enableOrgLevelLogic: true
  enableV2Endpoints: true
  v2TrafficPercentage: 100
```

#### Deployment Steps
1. **Enable V2 Endpoints** (2 hours)
   - Deploy V4 API endpoints
   - Update API documentation
   - Notify client teams

2. **Client Migration** (1 week)
   - Coordinate with client teams (MO, Paradox, Fostrack)
   - Provide migration support
   - Monitor client adoption

3. **V1 Deprecation** (After client migration)
   - Mark V1 endpoints as deprecated
   - Set deprecation timeline
   - Monitor V1 usage decline

#### Success Criteria
- [ ] V4 endpoints fully functional
- [ ] Client teams successfully migrated
- [ ] V1 endpoint usage <10%
- [ ] No breaking changes for clients

## Monitoring and Alerting

### Key Metrics
- API response times (V1 vs V2)
- Error rates by API version
- Fallback usage frequency
- Data consistency validation results
- Client adoption rates

### Alerts
- V2 API error rate >1%
- Fallback usage >10%
- Data inconsistency detected
- Performance degradation >20%

### Dashboards
- Migration progress tracking
- API performance comparison
- Error rate trends
- Client adoption metrics

## Risk Mitigation

### High-Risk Scenarios
1. **V2 API Outage**
   - Mitigation: Automatic fallback to V1
   - Recovery: Fix V2 issues, gradual re-enablement

2. **Data Inconsistency**
   - Mitigation: Real-time validation checks
   - Recovery: Data reconciliation scripts

3. **Performance Degradation**
   - Mitigation: Traffic throttling and caching
   - Recovery: Performance optimization and scaling

4. **Client Integration Issues**
   - Mitigation: Comprehensive testing and documentation
   - Recovery: Client-specific support and fixes

### Rollback Procedures
1. **Configuration Rollback**: Immediate via config changes
2. **Code Rollback**: Standard deployment rollback procedures
3. **Data Rollback**: Database restore from backups (last resort)

## Success Criteria

### Technical Success
- [ ] Zero data loss during migration
- [ ] <5% performance impact
- [ ] <0.1% error rate increase
- [ ] 100% feature parity maintained

### Business Success
- [ ] No customer-facing issues
- [ ] All client integrations successful
- [ ] Org-level features working as expected
- [ ] Migration completed within timeline

## Post-Deployment Activities

### Week 8-9: Stabilization
- Monitor system performance
- Address any issues or bugs
- Optimize performance if needed
- Gather feedback from users

### Week 10-12: Cleanup
- Remove V1 fallback code (gradual)
- Clean up deprecated configurations
- Update documentation
- Conduct post-mortem review

### Ongoing: Maintenance
- Regular monitoring of org-level features
- Performance optimization
- Feature enhancements based on feedback
- Continuous improvement

## Communication Plan

### Stakeholders
- Engineering teams
- Product management
- Client teams (MO, Paradox, Fostrack)
- Operations team
- QA team

### Communication Schedule
- **Pre-deployment**: Migration plan review and approval
- **During deployment**: Daily status updates
- **Post-deployment**: Weekly progress reports
- **Issues**: Immediate escalation and communication

This deployment plan ensures a safe, gradual migration to org-level support while maintaining system stability and zero downtime.
