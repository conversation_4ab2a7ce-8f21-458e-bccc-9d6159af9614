# Gladius Service - Legion Org Migration Implementation Guide

## Overview

This document outlines the implementation of org-level support in Gladius service following the Legion revamp. The migration enables Gladius to understand and work with the new org-level structure where agents belong to organizations with hierarchical roles and business units.

**Important Note**: Legion didn't create new V2 APIs. Instead, they enhanced existing APIs by adding org-level fields to the response structures. This implementation handles parsing these enhanced responses while maintaining backward compatibility.

## Key Changes Implemented

### 1. New Data Models

#### OrgProfile (`gladius-models/src/main/java/com/phonepe/merchant/gladius/models/legion/v2/OrgProfile.java`)
- Represents organization structure from Legion V2
- Contains org attributes (businessUnit, region, etc.)
- Includes org roles with hierarchy information

#### AgentProfileV2 (`gladius-models/src/main/java/com/phonepe/merchant/gladius/models/legion/v2/AgentProfileV2.java`)
- Extended agent profile with org-level information
- Backward compatibility methods for V1 data
- Helper methods for effective role/business unit resolution

#### Enhanced Entity Models
- Updated `AgentEntity` and `AgentEntityMeta` with org fields
- Added orgId, orgName, orgRoleId, orgRoleName fields

### 2. Service Layer Architecture

#### LegionServiceV2 (`gladius-core/src/main/java/com/phonepe/merchant/legion/core/services/LegionServiceV2.java`)
- Service for parsing enhanced Legion API responses with org fields
- Same API endpoints as V1 but enhanced response parsing
- Org-aware agent profile retrieval from enhanced responses
- Organization profile management (if new endpoints exist)

#### LegionServiceAdapter (`gladius-core/src/main/java/com/phonepe/merchant/legion/core/services/LegionServiceAdapter.java`)
- Abstraction layer for legacy vs enhanced response parsing
- Configuration-based switching between parsing strategies
- Fallback mechanisms for parsing errors
- Backward compatibility for existing code

#### OrgAwareValidationService (`gladius-core/src/main/java/com/phonepe/merchant/legion/core/services/OrgAwareValidationService.java`)
- Org-level hierarchy validation
- Role-based validation with org context
- Cross-org permission checks

### 3. Migration Configuration

#### LegionMigrationConfig (`gladius-core/src/main/java/com/phonepe/merchant/legion/core/config/LegionMigrationConfig.java`)
- Feature flags for migration phases
- Traffic percentage routing
- Test agent configurations
- Migration phase tracking

#### Configuration Integration
- Added to `AppConfig.java` and `stage.yml`
- Environment-specific migration settings
- Gradual rollout controls

### 4. Business Logic Updates

#### Updated Validation Logic
- Modified `ValidationServiceImpl` to use org-aware validation
- Updated `AgentTypeValidator` for org-level role checking
- Enhanced `Validations` class with effective role/BU resolution

#### Task Assignment Service
- New `OrgAwareTaskAssignmentService` for org-level task assignment
- Cross-org assignment validation
- Org-specific constraint checking

#### Task Discovery Updates
- Updated `TaskDiscoveryServiceImpl` to use adapter pattern
- Org-aware agent eligibility filtering
- Enhanced sector validation

### 5. API Enhancements

#### V4 API Endpoints (`TaskDiscoveryResourceV4.java`)
- New org-aware task listing endpoint
- Enhanced agent eligibility response with org context
- Backward compatible response structure
- Org-level filtering and validation

## Migration Phases

### Phase 1: Enhanced Response Parsing Ready
```yaml
legionMigrationConfig:
  useV2ForReads: false  # Use legacy response parsing
  fallbackToV1OnError: true
```

### Phase 2: User Level Data Migration
- Legion backend populates org fields in existing API responses
- Gladius continues using legacy response parsing
- Data consistency validation

### Phase 3: Enhanced Response Parsing
```yaml
legionMigrationConfig:
  useV2ForReads: true   # Parse enhanced responses with org fields
  enableOrgLevelLogic: false  # Don't use org logic yet
  fallbackToV1OnError: true
```

### Phase 4: Org Migration of Users
```yaml
legionMigrationConfig:
  useV2ForReads: true
  enableOrgLevelLogic: true
  enableOrgRoleValidation: true
  enableOrgHierarchyValidation: true
```

### Phase 5: Full Org-Level Feature Enablement
```yaml
legionMigrationConfig:
  useV2ForReads: true
  enableOrgLevelLogic: true
  enableV2Endpoints: true  # Enable new V4 endpoints with org context
  v2TrafficPercentage: 100
```

## Configuration Examples

### Development/Testing
```yaml
legionMigrationConfig:
  useV2ForReads: false
  useV2ForWrites: false
  fallbackToV1OnError: true
  v2TestAgentIds: ["test-agent-1", "test-agent-2"]
  v2TrafficPercentage: 0
```

### Gradual Rollout
```yaml
legionMigrationConfig:
  useV2ForReads: true
  v2TrafficPercentage: 25  # 25% of traffic to V2
  migratedOrgIds: ["org-1", "org-2"]
  enableOrgLevelLogic: true
```

### Full Migration
```yaml
legionMigrationConfig:
  useV2ForReads: true
  useV2ForWrites: true
  enableOrgLevelLogic: true
  enableV2Endpoints: true
  v2TrafficPercentage: 100
  enableOrgRoleValidation: true
  enableOrgSectorValidation: true
  enableOrgHierarchyValidation: true
```

## Key Features

### 1. Backward Compatibility
- All existing APIs continue to work
- V1 data structures supported
- Graceful fallback mechanisms

### 2. Gradual Migration
- Configuration-driven rollout
- Agent-level and percentage-based routing
- Safe rollback capabilities

### 3. Org-Level Features
- Cross-org task assignment validation
- Org-specific business rules
- Hierarchical role validation
- Org-aware sector management

### 4. Enhanced APIs
- V4 endpoints with org context
- Detailed org information in responses
- Org-level filtering and validation

## Testing Strategy

### Unit Tests
- Test org-aware validation logic
- Mock V2 API responses
- Configuration-based behavior testing

### Integration Tests
- End-to-end org-level workflows
- Migration phase testing
- Fallback scenario validation

### Performance Tests
- V2 API performance impact
- Adapter layer overhead
- Large-scale org operations

## Deployment Considerations

### 1. Database Changes
- New org-level fields in existing tables
- Migration scripts for data population
- Index optimization for org queries

### 2. Service Dependencies
- Legion V2 API availability
- Backward compatibility requirements
- Error handling and monitoring

### 3. Monitoring
- Migration phase metrics
- V1/V2 API usage tracking
- Error rate monitoring
- Performance impact assessment

## Next Steps

1. **Testing**: Comprehensive testing of all migration phases
2. **Documentation**: API documentation updates
3. **Monitoring**: Enhanced monitoring and alerting
4. **Deployment**: Phased rollout plan execution
5. **Training**: Team training on new org-level features

## Support and Troubleshooting

### Common Issues
- V2 API connectivity problems → Fallback to V1
- Org data inconsistencies → Validation and cleanup
- Performance degradation → Configuration tuning

### Rollback Plan
- Configuration-based rollback to V1
- Data consistency checks
- Service health monitoring

This implementation provides a robust foundation for the Legion org migration while maintaining backward compatibility and enabling gradual rollout.
